from plugins.location_tools.repo_ops.repo_ops import get_graph_entity_searcher, get_graph, load_repo_index, search_code_snippets, get_current_repo_modules
from plugins.location_tools.retriever.bm25_retriever import (
    build_code_retriever_from_repo as build_code_retriever,
    build_module_retriever_from_graph as build_module_retriever,
    build_retriever_from_persist_dir as load_retriever,
)
from plugins.location_tools.retriever.fuzzy_retriever import (
    fuzzy_retrieve_from_graph_nodes as fuzzy_retrieve,
    ratio,
)

import Stemmer

load_repo_index(repo="/home/<USER>/Codebase/gerrit/PATCHER/")

files, classes, funcs = get_current_repo_modules()
print(len(files))

searcher = get_graph_entity_searcher()
g = searcher.G
# for nid in g.nodes():
#     if (g.nodes[nid]['type'] == 'class' or g.nodes[nid]['type'] == 'file') and "PatchHistory" in nid:
#         node_data = g.nodes[nid]
#         print("class:", nid
#               #, node_data
#               )

# stemmer = Stemmer.Stemmer('english')
# o = stemmer.stemWord("Patcher")
# print(o)
# print(build_module_retriever(entity_searcher=get_graph_entity_searcher()).retrieve("PatchInfo"))
# nodes = build_module_retriever(entity_searcher=get_graph_entity_searcher(), similarity_top_k=50).retrieve("PatchHistory.java")


# print(ratio("history", 'daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/src/main/java/com/zte/daip/manager/patcher/impl/paas/PatcherApplication.java'))

# from plugins.location_tools.retriever.fuzzy_scorer import score_fuzzy, prepare_query
# from plugins.location_tools.retriever.fuzzy_score_item import score_item_fuzzy
# from plugins.location_tools.retriever.test_fuzzy_scorer import ResourceAccessor, URI

# resource = URI.file('xyz/some/path/someFile123.txt')


#         # Test path identity
# identity_res = score_item_fuzzy(resource, prepare_query("some"), True, ResourceAccessor, {})
# print(identity_res)



# prepared_query = prepare_query("HelLo-world")
# print(prepared_query)
# print(score_fuzzy('HelLo-World', prepared_query.normalized, prepared_query.normalizedLowercase, not prepared_query.expectContiguousMatch))

# nodes = fuzzy_retrieve("patchinfo", graph=g, search_scope='all', similarity_top_k=8)
# # PatchHistory
# for node in nodes:
#     # print(node.text, node.score)
#     print(node)

# build_code_retriever(absolute_repo_dir, persist_path=persist_path,
#                                          similarity_top_k=similarity_top_k)


# for file in files:
#     if "PatchDetailPo" in file["name"]:
#         print(file["name"])
#         print(file["content"])

# for clazz in classes:
#     if "PatchDetailPo" in clazz["name"]:
#         print(clazz["name"])
#         print(clazz["content"])


# for func in funcs[:10]:
#     print(func)
    # if "PatchDetailPo" in func["name"]:
    #     print(func["name"])
    #     print(func["content"])


# print(search_code_snippets(search_terms=["dapmanager_patch_detail_info"]))
print(search_code_snippets(search_terms=["history"]))



# [[ ## tool_name_3 ## ]]
# search_code_snippets

# [[ ## tool_args_3 ## ]]
# {"search_terms": ["dapmanager_patch_detail_info", "dapmanager_patch_dispatch", "dapmanager_patch_history"], "file_path_or_pattern": "**/*.java"}

# [[ ## tool_name_4 ## ]]
# search_code_snippets

# [[ ## tool_args_4 ## ]]
# {"search_terms": ["PatchDetailRepository", "PatchDispatchRepository", "PatchHistoryRepository"], "file_path_or_pattern": "**/*.java"}


# [[ ## tool_name_1 ## ]]
# explore_tree_structure

# [[ ## tool_args_1 ## ]]
# {"start_entities": ["daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java:PatchHandler"], "direction": "downstream", "traversal_depth": 2, "entity_type_filter": ["class", "interface"], "dependency_type_filter": ["invokes", "imports"]}
