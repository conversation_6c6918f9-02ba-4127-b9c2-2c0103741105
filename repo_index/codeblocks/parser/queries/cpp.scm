(translation_unit . (_) @child.first @definition.module) @root

(preproc_include
  (string_literal) @reference.identifier @identifier
) @root @definition.include @reference.includes

(preproc_include
  (system_lib_string) @reference.identifier @identifier
) @root @definition.include @reference.includes

(preproc_def
  (identifier) @identifier
) @root @definition.macro

(preproc_function_def
  (identifier) @identifier
  (preproc_params
    "(" @child.first
  )
) @root @definition.macro

(namespace_definition
  (identifier) @identifier
  (declaration_list
    "{" @child.first
  )
) @root @definition.namespace

(class_specifier
  (type_identifier) @identifier
  (field_declaration_list
    "{" @child.first
  )
) @root @definition.class

(struct_specifier
  (type_identifier) @identifier
  (field_declaration_list
    "{" @child.first
  )
) @root @definition.struct

(union_specifier
  (type_identifier) @identifier
  (field_declaration_list
    "{" @child.first
  )
) @root @definition.union

(enum_specifier
  (type_identifier) @identifier
  (enumerator_list
    "{" @child.first
  )
) @root @definition.enum

(template_declaration
  (class_specifier
    (type_identifier) @identifier
  )
) @root @definition.template

(template_declaration
  (function_definition
    (function_declarator
      (identifier) @identifier
    )
  )
) @root @definition.template

(function_definition
  (function_declarator
    (identifier) @identifier
  )
  (compound_statement
    "{" @child.first
  )
) @root @definition.function

(function_definition
  (function_declarator
    (qualified_identifier
      (identifier) @identifier
    )
  )
  (compound_statement
    "{" @child.first
  )
) @root @definition.method

(function_definition
  (function_declarator
    (destructor_name
      (identifier) @identifier
    )
  )
  (compound_statement
    "{" @child.first
  )
) @root @definition.destructor

(declaration
  (init_declarator
    (identifier) @identifier
  )
) @root @definition.variable

(field_declaration
  (field_declarator
    (field_identifier) @identifier
  )
) @root @definition.field

(access_specifier) @root @definition.access

(base_class_clause
  (type_identifier) @reference.identifier @identifier
) @root @reference.inherits

(comment) @root @definition.comment

(_
  (compound_statement
    . "{" @child.first
  )
) @root @definition.statement
