(translation_unit . (_) @child.first @definition.module) @root

(preproc_include
  (string_literal) @reference.identifier @identifier
) @root @definition.include @reference.includes

(preproc_include
  (system_lib_string) @reference.identifier @identifier
) @root @definition.include @reference.includes

(preproc_def
  (identifier) @identifier
) @root @definition.macro

(preproc_function_def
  (identifier) @identifier
  (preproc_params
    "(" @child.first
  )
) @root @definition.macro

(struct_specifier
  (type_identifier) @identifier
  (field_declaration_list
    "{" @child.first
  )
) @root @definition.struct

(union_specifier
  (type_identifier) @identifier
  (field_declaration_list
    "{" @child.first
  )
) @root @definition.union

(enum_specifier
  (type_identifier) @identifier
  (enumerator_list
    "{" @child.first
  )
) @root @definition.enum

(typedef_declaration
  (type_identifier) @identifier
) @root @definition.typedef

(function_definition
  (function_declarator
    (identifier) @identifier
  )
  (compound_statement
    "{" @child.first
  )
) @root @definition.function

(declaration
  (init_declarator
    (identifier) @identifier
  )
) @root @definition.variable

(parameter_declaration
  (identifier) @identifier
) @root @definition.parameter

(comment) @root @definition.comment

(_
  (compound_statement
    . "{" @child.first
  )
) @root @definition.statement
