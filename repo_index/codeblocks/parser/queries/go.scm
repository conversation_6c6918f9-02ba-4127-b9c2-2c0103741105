(source_file . (_) @child.first @definition.module) @root

(package_clause
  (package_identifier) @identifier
) @root @definition.package

(import_declaration
  (import_spec_list
    (import_spec
      (interpreted_string_literal) @reference.identifier @identifier
    )
  )
) @root @definition.import @reference.imports

(import_declaration
  (import_spec
    (interpreted_string_literal) @reference.identifier @identifier
  )
) @root @definition.import @reference.imports

(type_declaration
  (type_spec
    (type_identifier) @identifier
    (struct_type
      (field_declaration_list
        ("{") @child.first
      )
    )
  )
) @root @definition.struct

(type_declaration
  (type_spec
    (type_identifier) @identifier
    (interface_type
      (method_spec_list
        ("{") @child.first
      )
    )
  )
) @root @definition.interface

(function_declaration
  (identifier) @identifier
  (block
    ("{") @child.first
  )
) @root @definition.function

(method_declaration
  (field_identifier) @identifier
  (block
    ("{") @child.first
  )
) @root @definition.method

(var_declaration
  (var_spec
    (identifier) @identifier
  )
) @root @definition.variable

(const_declaration
  (const_spec
    (identifier) @identifier
  )
) @root @definition.constant

(line_comment) @root @definition.comment
(block_comment) @root @definition.comment

(_
  (block
    . ("{") @child.first
  )
) @root @definition.statement
