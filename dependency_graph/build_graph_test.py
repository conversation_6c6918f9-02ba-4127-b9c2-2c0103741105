import os
import tempfile
import shutil
import networkx as nx
import pytest
from dependency_graph.build_graph import build_graph
from util.benchmark.setup_repo import setup_repo

def create_simple_repo():
    temp_dir = tempfile.mkdtemp()
    CURRENT_INSTANCE = {"repo": "astropy/astropy", "base_commit": "d5bd3f68bb6d5ce3a61bdce9883ee750d1afade5"}
    # 获取当前文件路径的上一级目录下的repos目录
    current_file = os.path.abspath(__file__)
    parent_dir = os.path.dirname(os.path.dirname(current_file))
    REPO_CLONE_DIR = os.path.join(parent_dir, 'repos')

    return setup_repo(instance_data=CURRENT_INSTANCE, repo_base_dir=temp_dir, dataset=None, clone_dir=REPO_CLONE_DIR)

@pytest.fixture
def simple_repo():
    repo_path = create_simple_repo()
    yield repo_path
    shutil.rmtree(repo_path)

def test_build_graph_for_python(simple_repo):
    graph = build_graph(simple_repo)
    # 打印主要信息
    print(f"节点数: {graph.number_of_nodes()}")
    print(f"边数: {graph.number_of_edges()}")
    from collections import Counter
    node_types = [data['type'] for _, data in graph.nodes(data=True)]
    nodes_counter = Counter(node_types)
    print(f"节点类型统计: {nodes_counter}")
    edge_types = [data['type'] for _, _, data in graph.edges(data=True)]
    edge_counter = Counter(edge_types)
    print(f"边类型统计: {edge_counter}")

    assert nodes_counter['function'] == 10299
    assert nodes_counter['class'] == 1478
    assert nodes_counter['file'] == 719
    assert nodes_counter['directory'] == 106

    assert edge_counter['invokes'] == 32964
    assert edge_counter['contains'] == 12798
    assert edge_counter['imports'] == 4682
    assert edge_counter['inherits'] == 1038


