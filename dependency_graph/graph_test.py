import networkx as nx

graph = nx.MultiDiGraph()
graph.add_node("A", type="directory")
graph.add_node("B", type="file")
graph.add_node("C", type="directory")
graph.add_node("D", type="file")
graph.add_edge("A", "B", type="contains")
graph.add_edge("A", "C", type="contains")
graph.add_edge("C", "D", type="contains")

print(graph.nodes(data=True))
print(graph.edges(data=True))


print([id for id in graph.successors("A")])
print(any([id for id in graph.successors("A") if graph.nodes[id]['type'] == 'file']))
