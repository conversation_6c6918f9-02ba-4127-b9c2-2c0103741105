import argparse
import os
import re
import pickle
import chardet
from collections import Counter, defaultdict
from typing import List, Dict, Set
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from multiprocessing import cpu_count

import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from tree_sitter_languages import get_language
from tree_sitter import Language, Node, Parser

# Utility functions to avoid circular import
def is_delete_file(file_path: str):
    return file_path.endswith('.locagent_delete')

def get_raw_name_of_delete_file(file_path: str):
    suffix = ".locagent_delete"
    if not file_path.endswith(suffix):
        return file_path
    return file_path[:-len(suffix)]


VERSION = 'v1.0'
NODE_TYPE_DIRECTORY = 'directory'
NODE_TYPE_FILE = 'file'
NODE_TYPE_CLASS = 'class'
NODE_TYPE_FUNCTION = 'method'
NODE_TYPE_INTERFACE = 'interface'
NODE_TYPE_ENUM = 'enum'
EDGE_TYPE_CONTAINS = 'contains'
EDGE_TYPE_INHERITS = 'inherits'
EDGE_TYPE_INVOKES = 'invokes'
EDGE_TYPE_IMPORTS = 'imports'
EDGE_TYPE_IMPLEMENTS = 'implements'

VALID_NODE_TYPES = [NODE_TYPE_DIRECTORY, NODE_TYPE_FILE, NODE_TYPE_CLASS, NODE_TYPE_FUNCTION, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]
VALID_EDGE_TYPES = [EDGE_TYPE_CONTAINS, EDGE_TYPE_INHERITS, EDGE_TYPE_INVOKES, EDGE_TYPE_IMPORTS, EDGE_TYPE_IMPLEMENTS]

SKIP_DIRS = ['.github', '.git', 'target', 'build', '.idea', '.vscode']

def is_skip_dir(dirname):
    for skip_dir in SKIP_DIRS:
        if skip_dir in dirname:
            return True
    return False


class CacheManager:
    """Global cache manager for ClassResolver and GraphIndexes"""

    def __init__(self):
        self._class_resolvers = {}  # repo_path -> ClassResolver
        self._graph_indexes = {}    # graph_id -> GraphIndexes

    def get_class_resolver(self, repo_path: str) -> 'ClassResolver':
        """Get or create ClassResolver for the given repo path"""
        if repo_path not in self._class_resolvers:
            print(f"Creating new ClassResolver for repo: {repo_path}")
            self._class_resolvers[repo_path] = ClassResolver(repo_path)
        return self._class_resolvers[repo_path]

    def get_graph_indexes(self, graph) -> 'GraphIndexes':
        """Get or create GraphIndexes for the given graph"""
        graph_id = id(graph)
        if graph_id not in self._graph_indexes:
            print(f"Creating new GraphIndexes for graph: {graph_id}")
            graph_indexes = GraphIndexes()
            # Build indexes from current graph
            for node, attributes in graph.nodes(data=True):
                node_type = attributes.get('type')
                if node_type in [NODE_TYPE_CLASS, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM, NODE_TYPE_FUNCTION]:
                    graph_indexes.add_node(node, node_type)
            self._graph_indexes[graph_id] = graph_indexes
        return self._graph_indexes[graph_id]

    def clear_cache(self):
        """Clear all caches"""
        self._class_resolvers.clear()
        self._graph_indexes.clear()

# Global cache manager instance
_cache_manager = CacheManager()


class ClassResolver:
    """Optimized class resolver with caching and pre-built index"""

    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.class_file_cache = {}  # class_name -> file_path
        self.package_cache = {}     # file_path -> package_name
        self.file_index_built = False

    def _build_file_index(self):
        """Build index of all Java files and their classes once"""
        if self.file_index_built:
            return

        print("Building file index for class resolution...")
        for root, dirs, files in os.walk(self.repo_path):
            # Skip directories early
            dirs[:] = [d for d in dirs if not any(skip in d for skip in SKIP_DIRS)]

            for file in files:
                if file.endswith('.java'):
                    file_path = os.path.join(root, file)

                    # Extract class name from filename
                    class_name = file[:-5]  # Remove .java extension
                    self.class_file_cache[class_name] = file_path

                    # Extract package name and cache it
                    package_name = self._extract_package_from_file(file_path)
                    if package_name:
                        self.package_cache[file_path] = package_name
                        # Also index with full qualified name
                        full_class_name = f"{package_name}.{class_name}"
                        self.class_file_cache[full_class_name] = file_path

        self.file_index_built = True
        print(f"File index built: {len(self.class_file_cache)} class entries")

    def _extract_package_from_file(self, file_path: str) -> str:
        """Extract package name from Java file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # Read only first few lines to find package declaration
                for _ in range(20):  # Limit search to first 20 lines
                    line = f.readline()
                    if not line:
                        break
                    package_match = re.match(r'package\s+([^;]+);', line.strip())
                    if package_match:
                        return package_match.group(1).strip()
        except:
            pass
        return None

    def resolve_class(self, class_name: str, current_package: str = None) -> str:
        """Resolve class name to file path with caching"""
        self._build_file_index()

        # Try exact match first
        if class_name in self.class_file_cache:
            return self.class_file_cache[class_name]

        # Try with current package
        if current_package and not '.' in class_name:
            full_name = f"{current_package}.{class_name}"
            if full_name in self.class_file_cache:
                return self.class_file_cache[full_name]

        # Try simple class name match
        simple_name = class_name.split('.')[-1]
        if simple_name in self.class_file_cache:
            return self.class_file_cache[simple_name]

        return None


class GraphIndexes:
    """Optimized indexes for fast node lookup"""

    def __init__(self):
        self.class_nodes = {}       # class_name -> [node_ids]
        self.method_nodes = {}      # method_name -> [node_ids]
        self.simple_class_names = {}  # simple_name -> [node_ids]
        self.simple_method_names = {} # simple_name -> [node_ids]
        self.node_types = {}        # node_id -> type

    def add_node(self, node_id: str, node_type: str, node_name: str = None):
        """Add node to indexes"""
        self.node_types[node_id] = node_type

        if not node_name:
            # Extract name from node_id (format: filename:class.method)
            if ':' in node_id:
                node_name = node_id.split(':', 1)[1]
            else:
                return

        if node_type in [NODE_TYPE_CLASS, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]:
            # Index full name
            if node_name not in self.class_nodes:
                self.class_nodes[node_name] = []
            self.class_nodes[node_name].append(node_id)

            # Index simple name
            simple_name = node_name.split('.')[-1]
            if simple_name not in self.simple_class_names:
                self.simple_class_names[simple_name] = []
            self.simple_class_names[simple_name].append(node_id)

        elif node_type == NODE_TYPE_FUNCTION:
            # Index full name
            if node_name not in self.method_nodes:
                self.method_nodes[node_name] = []
            self.method_nodes[node_name].append(node_id)

            # Index simple name
            simple_name = node_name.split('.')[-1]
            if simple_name not in self.simple_method_names:
                self.simple_method_names[simple_name] = []
            self.simple_method_names[simple_name].append(node_id)

    def remove_node(self, node_id: str):
        """Remove node from all indexes"""
        if node_id not in self.node_types:
            return

        node_type = self.node_types[node_id]

        # Extract name from node_id
        if ':' in node_id:
            node_name = node_id.split(':', 1)[1]
        else:
            del self.node_types[node_id]
            return

        # Remove from appropriate indexes
        if node_type in [NODE_TYPE_CLASS, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]:
            if node_name in self.class_nodes:
                self.class_nodes[node_name] = [n for n in self.class_nodes[node_name] if n != node_id]
                if not self.class_nodes[node_name]:
                    del self.class_nodes[node_name]

            simple_name = node_name.split('.')[-1]
            if simple_name in self.simple_class_names:
                self.simple_class_names[simple_name] = [n for n in self.simple_class_names[simple_name] if n != node_id]
                if not self.simple_class_names[simple_name]:
                    del self.simple_class_names[simple_name]

        elif node_type == NODE_TYPE_FUNCTION:
            if node_name in self.method_nodes:
                self.method_nodes[node_name] = [n for n in self.method_nodes[node_name] if n != node_id]
                if not self.method_nodes[node_name]:
                    del self.method_nodes[node_name]

            simple_name = node_name.split('.')[-1]
            if simple_name in self.simple_method_names:
                self.simple_method_names[simple_name] = [n for n in self.simple_method_names[simple_name] if n != node_id]
                if not self.simple_method_names[simple_name]:
                    del self.simple_method_names[simple_name]

        del self.node_types[node_id]

    def find_class_nodes(self, class_name: str, fuzzy_search: bool = True) -> List[str]:
        """Find class nodes by name using indexes"""
        if not fuzzy_search:
            return self.class_nodes.get(class_name, [])

        # Try exact match first
        if class_name in self.class_nodes:
            return self.class_nodes[class_name]

        # Try simple name match
        simple_name = class_name.split('.')[-1]
        return self.simple_class_names.get(simple_name, [])

    def find_method_nodes(self, method_name: str, fuzzy_search: bool = True) -> List[str]:
        """Find method nodes by name using indexes"""
        if not fuzzy_search:
            return self.method_nodes.get(method_name, [])

        # Try exact match first
        if method_name in self.method_nodes:
            return self.method_nodes[method_name]

        # Try simple name match
        simple_name = method_name.split('.')[-1]
        return self.simple_method_names.get(simple_name, [])


class JavaCodeAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.nodes = []
        self.imports = []
        self.parser = Parser()
        self.parser.set_language(get_language('java'))
        
    def analyze(self, content):
        """Analyze Java file content using tree-sitter"""
        tree = self.parser.parse(content.encode('utf-8'))
        self._traverse_node(tree.root_node, content)
        return self.nodes, self.imports
    
    def _traverse_node(self, node: Node, content: str, parent_name: str = ""):
        """Traverse tree-sitter AST nodes"""
        if node.type == 'class_declaration':
            self._handle_class(node, content, parent_name)
        elif node.type == 'interface_declaration':
            self._handle_interface(node, content, parent_name)
        elif node.type == 'enum_declaration':
            self._handle_enum(node, content, parent_name)
        elif node.type == 'method_declaration':
            self._handle_method(node, content, parent_name)
        elif node.type == 'constructor_declaration':
            self._handle_constructor(node, content, parent_name)
        elif node.type == 'import_declaration':
            self._handle_import(node, content)
        
        # Recursively traverse child nodes
        for child in node.children:
            current_parent = parent_name
            if node.type in ['class_declaration', 'interface_declaration', 'enum_declaration']:
                class_name = self._get_identifier(node)
                if class_name:
                    current_parent = f"{parent_name}.{class_name}" if parent_name else class_name
            self._traverse_node(child, content, current_parent)
    
    def _get_identifier(self, node: Node) -> str:
        """Extract identifier from a node"""
        for child in node.children:
            if child.type == 'identifier':
                return child.text.decode('utf-8')
        return ""
    
    def _get_source_segment(self, node: Node, content: str) -> str:
        """Get source code segment for a node"""
        start_byte = node.start_byte
        end_byte = node.end_byte
        return content.encode('utf-8')[start_byte:end_byte].decode('utf-8')
    
    def _handle_class(self, node: Node, content: str, parent_name: str):
        """Handle class declaration"""
        class_name = self._get_identifier(node)
        if class_name:
            full_name = f"{parent_name}.{class_name}" if parent_name else class_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_CLASS,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'extends': self._get_extends(node),
                'implements': self._get_implements(node)
            })
    
    def _handle_interface(self, node: Node, content: str, parent_name: str):
        """Handle interface declaration"""
        interface_name = self._get_identifier(node)
        if interface_name:
            full_name = f"{parent_name}.{interface_name}" if parent_name else interface_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_INTERFACE,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'extends': self._get_extends(node)
            })
    
    def _handle_enum(self, node: Node, content: str, parent_name: str):
        """Handle enum declaration"""
        enum_name = self._get_identifier(node)
        if enum_name:
            full_name = f"{parent_name}.{enum_name}" if parent_name else enum_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_ENUM,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'implements': self._get_implements(node)
            })
    
    def _handle_method(self, node: Node, content: str, parent_name: str):
        """Handle method declaration"""
        method_name = self._get_identifier(node)
        if method_name:
            full_name = f"{parent_name}.{method_name}" if parent_name else method_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_FUNCTION,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'invocations': self._get_method_invocations(node)
            })
    
    def _handle_constructor(self, node: Node, content: str, parent_name: str):
        """Handle constructor declaration"""
        constructor_name = self._get_identifier(node)
        if constructor_name:
            full_name = f"{parent_name}.{constructor_name}" if parent_name else constructor_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_FUNCTION,  # Treat constructor as method
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'invocations': self._get_method_invocations(node),
                'is_constructor': True
            })
    
    def _handle_import(self, node: Node, content: str):
        """Handle import declaration"""
        import_text = self._get_source_segment(node, content).strip()
        # Extract import path from "import package.Class;" or "import package.*;"
        import_match = re.match(r'import\s+(static\s+)?([^;]+);', import_text)
        if import_match:
            is_static = import_match.group(1) is not None
            import_path = import_match.group(2).strip()
            self.imports.append({
                'path': import_path,
                'is_static': is_static,
                'is_wildcard': import_path.endswith('*')
            })
    
    def _get_extends(self, node: Node) -> List[str]:
        """Get extends clause from class/interface declaration"""
        extends = []
        for child in node.children:
            if child.type == 'superclass':
                for grandchild in child.children:
                    if grandchild.type == 'type_identifier':
                        extends.append(grandchild.text.decode('utf-8'))
            elif child.type == 'super_interfaces':
                for grandchild in child.children:
                    if grandchild.type == 'type_list':
                        for type_node in grandchild.children:
                            if type_node.type == 'type_identifier':
                                extends.append(type_node.text.decode('utf-8'))
        return extends
    
    def _get_implements(self, node: Node) -> List[str]:
        """Get implements clause from class declaration"""
        implements = []
        for child in node.children:
            if child.type == 'super_interfaces':
                for grandchild in child.children:
                    if grandchild.type == 'type_list':
                        for type_node in grandchild.children:
                            if type_node.type == 'type_identifier':
                                implements.append(type_node.text.decode('utf-8'))
        return implements
    
    def _get_method_invocations(self, node: Node) -> List[str]:
        """Get method invocations from method body"""
        invocations = []
        self._find_method_calls(node, invocations)
        return invocations
    
    def _find_method_calls(self, node: Node, invocations: List[str]):
        """Recursively find method calls in AST"""
        if node.type == 'method_invocation':
            # Extract method name from method_invocation
            for child in node.children:
                if child.type == 'identifier':
                    invocations.append(child.text.decode('utf-8'))
                elif child.type == 'field_access':
                    # Handle chained method calls like obj.method()
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            invocations.append(grandchild.text.decode('utf-8'))
        
        # Recursively search in children
        for child in node.children:
            self._find_method_calls(child, invocations)


def read_file_safely(filepath: str) -> str:
    """Safely read file with encoding detection"""
    with open(filepath, "rb") as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result["encoding"] or "utf-8"
    
    with open(filepath, "r", encoding=encoding) as f:
        return f.read()


def analyze_java_file(filepath):
    """Analyze a Java file and extract classes, methods, imports"""
    try:
        content = read_file_safely(filepath)
    except:
        raise UnicodeDecodeError

    try:
        analyzer = JavaCodeAnalyzer(filepath)
        nodes, imports = analyzer.analyze(content)
        return nodes, imports
    except Exception as e:
        raise SyntaxError(f"Failed to parse {filepath}: {e}")


def process_single_file(args):
    """Process a single Java file"""
    filename, repo_path = args
    file_path = os.path.join(repo_path, filename)

    # Skip if file doesn't exist or is a symlink
    if not os.path.exists(file_path) or os.path.islink(file_path):
        return None

    # Skip if in skip directory
    if is_skip_dir(os.path.dirname(filename)):
        return None

    try:
        # Read file content
        with open(file_path, "r", encoding="utf-8") as f:
            file_content = f.read()

        # Extract package name
        package_name = extract_package_name(file_path, repo_path)

        # Analyze Java file
        nodes, imports = analyze_java_file(file_path)

        return {
            'filename': filename,
            'file_content': file_content,
            'package_name': package_name,
            'nodes': nodes,
            'imports': imports,
            'file_path': file_path
        }

    except (UnicodeDecodeError, SyntaxError) as e:
        print(f"Skipping file {file_path}: {e}")
        return None


def process_file_batch(args):
    """Process a batch of Java files"""
    filenames, repo_path = args
    results = []

    for filename in filenames:
        result = process_single_file((filename, repo_path))
        if result is not None:
            results.append(result)

    return results


def resolve_java_class(class_name, package_name, repo_path, class_resolver=None):
    """
    Resolve a Java class name to a file path in the repo using caching.
    Returns the file path if found, or None if not found.
    """
    # If no class_resolver provided, get one from global cache manager
    if not class_resolver:
        class_resolver = _cache_manager.get_class_resolver(repo_path)

    return class_resolver.resolve_class(class_name, package_name)


def extract_package_name(filepath, repo_path=None):
    """Extract package name from Java file"""
    try:
        content = read_file_safely(filepath)
        package_match = re.search(r'package\s+([^;]+);', content)
        if package_match:
            return package_match.group(1).strip()
    except:
        pass
    return None


def add_java_imports(root_node, imports, graph, repo_path, package_name, class_resolver=None):
    """Add import edges to the graph"""
    for imp in imports:
        import_path = imp['path']

        if imp['is_wildcard']:
            # Handle wildcard imports like java.util.*
            continue  # Skip wildcard imports for now

        # Try to resolve the imported class
        if '.' in import_path:
            # Full qualified import
            class_name = import_path.split('.')[-1]
            class_file_path = resolve_java_class(class_name, None, repo_path, class_resolver)
        else:
            # Simple class name
            class_file_path = resolve_java_class(import_path, package_name, repo_path, class_resolver)

        if class_file_path:
            imp_filename = os.path.relpath(class_file_path, repo_path)
            if graph.has_node(imp_filename):
                graph.add_edge(root_node, imp_filename, type=EDGE_TYPE_IMPORTS)

            # Also try to link to specific class within the file
            class_node = f"{imp_filename}:{class_name if '.' in import_path else import_path}"
            if graph.has_node(class_node):
                graph.add_edge(root_node, class_node, type=EDGE_TYPE_IMPORTS)


'''
def build_graph(repo_path, fuzzy_search=True, global_import=False):
    """
    Build dependency graph for Java files in the repository
    """
    graph = nx.MultiDiGraph()
    file_nodes = {}

    # Add root directory node
    graph.add_node('/', type=NODE_TYPE_DIRECTORY)
    dir_stack: List[str] = []
    dir_include_stack: List[bool] = []

    # First pass: Add nodes (directories, files, classes, methods)
    for root, _, files in os.walk(repo_path):
        # Add directory nodes and edges
        dirname = os.path.relpath(root, repo_path)
        if dirname == '.':
            dirname = '/'
        elif is_skip_dir(dirname):
            continue
        else:
            graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)
            parent_dirname = os.path.dirname(dirname)
            if parent_dirname == '':
                parent_dirname = '/'
            graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)

        # Remove directories that don't contain Java files
        while len(dir_stack) > 0 and not dirname.startswith(dir_stack[-1]):
            if not dir_include_stack[-1]:
                graph.remove_node(dir_stack[-1])
            dir_stack.pop()
            dir_include_stack.pop()
        if dirname != '/':
            dir_stack.append(dirname)
            dir_include_stack.append(False)

        dir_has_java = False
        for file in files:
            if file.endswith('.java'):
                dir_has_java = True

                # Add file nodes
                try:
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, repo_path)

                    if os.path.islink(file_path):
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    graph.add_node(filename, type=NODE_TYPE_FILE, code=file_content)

                    # Extract package name
                    package_name = extract_package_name(file_path, repo_path)

                    # Analyze Java file
                    nodes, imports = analyze_java_file(file_path)

                except (UnicodeDecodeError, SyntaxError) as e:
                    print(f"Skipping file {file_path}: {e}")
                    continue

                # Add class/method/interface nodes
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    node_attrs = {
                        'type': node['type'],
                        'code': node['code'],
                        'start_line': node['start_line'],
                        'end_line': node['end_line']
                    }

                    # Add type-specific attributes
                    if 'extends' in node:
                        node_attrs['extends'] = node['extends']
                    if 'implements' in node:
                        node_attrs['implements'] = node['implements']
                    if 'invocations' in node:
                        node_attrs['invocations'] = node['invocations']
                    if 'is_constructor' in node:
                        node_attrs['is_constructor'] = node['is_constructor']

                    graph.add_node(full_name, **node_attrs)

                # Add containment edges
                graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    name_parts = node['name'].split('.')
                    if len(name_parts) == 1:
                        # Top-level class/interface
                        graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
                    else:
                        # Nested class/method
                        parent_name = '.'.join(name_parts[:-1])
                        full_parent_name = f'{filename}:{parent_name}'
                        if graph.has_node(full_parent_name):
                            graph.add_edge(full_parent_name, full_name, type=EDGE_TYPE_CONTAINS)
                        else:
                            # Fallback to file containment
                            graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)

                # Store imports for later processing
                file_nodes[filename] = {
                    'path': file_path,
                    'imports': imports,
                    'package': package_name
                }

        # Keep all parent directories that contain Java files
        if dir_has_java:
            for i in range(len(dir_include_stack)):
                dir_include_stack[i] = True

    # Clean up remaining directories
    while len(dir_stack) > 0:
        if not dir_include_stack[-1]:
            graph.remove_node(dir_stack[-1])
        dir_stack.pop()
        dir_include_stack.pop()

    # Second pass: Add import, inheritance, implementation, and invocation edges
    for filename, file_info in file_nodes.items():
        if isinstance(file_info, dict):
            file_path = file_info['path']
            imports = file_info['imports']
            package_name = file_info['package']

            # Add import edges
            add_java_imports(filename, imports, graph, repo_path, package_name)

    # Third pass: Add inheritance, implementation, and method invocation edges
    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') not in [NODE_TYPE_CLASS, NODE_TYPE_FUNCTION, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]:
            continue

        # Handle inheritance (extends)
        if 'extends' in attributes:
            for parent_class in attributes['extends']:
                # Find the parent class node
                parent_nodes = find_class_nodes(graph, parent_class, fuzzy_search)
                for parent_node in parent_nodes:
                    graph.add_edge(node, parent_node, type=EDGE_TYPE_INHERITS)

        # Handle implementation (implements)
        if 'implements' in attributes:
            for interface in attributes['implements']:
                # Find the interface node
                interface_nodes = find_class_nodes(graph, interface, fuzzy_search)
                for interface_node in interface_nodes:
                    graph.add_edge(node, interface_node, type=EDGE_TYPE_IMPLEMENTS)

        # Handle method invocations
        if 'invocations' in attributes:
            for method_name in attributes['invocations']:
                # Find possible method nodes
                method_nodes = find_method_nodes(graph, method_name, fuzzy_search)
                for method_node in method_nodes:
                    graph.add_edge(node, method_node, type=EDGE_TYPE_INVOKES)

    return graph
'''


def find_class_nodes(graph, class_name, fuzzy_search=True, graph_indexes=None):
    """Find class/interface/enum nodes by name using indexing"""
    # If no graph_indexes provided, get one from global cache manager
    if not graph_indexes:
        graph_indexes = _cache_manager.get_graph_indexes(graph)

    return graph_indexes.find_class_nodes(class_name, fuzzy_search)


def find_method_nodes(graph, method_name, fuzzy_search=True, graph_indexes=None):
    """Find method nodes by name using indexing"""
    # If no graph_indexes provided, get one from global cache manager
    if not graph_indexes:
        graph_indexes = _cache_manager.get_graph_indexes(graph)

    return graph_indexes.find_method_nodes(method_name, fuzzy_search)


def visualize_java_graph(G):
    """Visualize the Java dependency graph"""
    node_types = set(nx.get_node_attributes(G, 'type').values())
    node_shapes = {
        NODE_TYPE_CLASS: 'o',
        NODE_TYPE_FUNCTION: 's',
        NODE_TYPE_FILE: 'D',
        NODE_TYPE_DIRECTORY: '^',
        NODE_TYPE_INTERFACE: 'v',
        NODE_TYPE_ENUM: 'p'
    }
    node_colors = {
        NODE_TYPE_CLASS: 'lightgreen',
        NODE_TYPE_FUNCTION: 'lightblue',
        NODE_TYPE_FILE: 'lightgrey',
        NODE_TYPE_DIRECTORY: 'orange',
        NODE_TYPE_INTERFACE: 'lightcoral',
        NODE_TYPE_ENUM: 'lightyellow'
    }

    edge_types = set(nx.get_edge_attributes(G, 'type').values())
    edge_colors = {
        EDGE_TYPE_IMPORTS: 'forestgreen',
        EDGE_TYPE_CONTAINS: 'skyblue',
        EDGE_TYPE_INVOKES: 'magenta',
        EDGE_TYPE_INHERITS: 'brown',
        EDGE_TYPE_IMPLEMENTS: 'purple'
    }
    edge_styles = {
        EDGE_TYPE_IMPORTS: 'solid',
        EDGE_TYPE_CONTAINS: 'dashed',
        EDGE_TYPE_INVOKES: 'dotted',
        EDGE_TYPE_INHERITS: 'dashdot',
        EDGE_TYPE_IMPLEMENTS: (0, (3, 1, 1, 1))
    }

    pos = nx.spring_layout(G, k=2, iterations=50)

    plt.figure(figsize=(20, 20))
    plt.margins(0.15)

    # Draw nodes with different shapes and colors based on their type
    for ntype in node_types:
        nodelist = [n for n, d in G.nodes(data=True) if d['type'] == ntype]
        if nodelist:  # Only draw if there are nodes of this type
            nx.draw_networkx_nodes(
                G,
                pos,
                nodelist=nodelist,
                node_shape=node_shapes.get(ntype, 'o'),
                node_color=node_colors.get(ntype, 'lightgray'),
                node_size=700,
                label=ntype,
            )

    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=8, font_family='sans-serif')

    # Group edges between the same pair of nodes
    edge_groups = {}
    for u, v, key, data in G.edges(keys=True, data=True):
        if (u, v) not in edge_groups:
            edge_groups[(u, v)] = []
        edge_groups[(u, v)].append((key, data))

    # Draw edges with adjusted 'rad' values
    for (u, v), edges in edge_groups.items():
        num_edges = len(edges)
        for i, (key, data) in enumerate(edges):
            edge_type = data['type']
            # Adjust 'rad' to spread the edges
            rad = 0.1 * (i - (num_edges - 1) / 2)
            nx.draw_networkx_edges(
                G,
                pos,
                edgelist=[(u, v)],
                edge_color=edge_colors.get(edge_type, 'black'),
                style=edge_styles.get(edge_type, 'solid'),
                connectionstyle=f'arc3,rad={rad}',
                arrows=True,
                arrowstyle='-|>',
                arrowsize=15,
                min_source_margin=15,
                min_target_margin=15,
                width=1.5
            )

    # Create legends for edge types and node types
    edge_legend_elements = [
        Line2D([0], [0], color=edge_colors.get(etype, 'black'), lw=2,
               linestyle=edge_styles.get(etype, 'solid'), label=etype)
        for etype in edge_types
    ]
    node_legend_elements = [
        Line2D([0], [0], marker=node_shapes.get(ntype, 'o'), color='w', label=ntype,
               markerfacecolor=node_colors.get(ntype, 'lightgray'), markersize=15)
        for ntype in node_types
    ]

    # Combine legends
    plt.legend(handles=edge_legend_elements + node_legend_elements, loc='upper left')
    plt.axis('off')
    plt.savefig('plots/java_dependency_graph.png', dpi=300, bbox_inches='tight')
    plt.show()


def traverse_java_directory_structure(graph, root='/'):
    """Traverse and print the directory structure of the Java project"""
    def traverse(node, prefix, is_last):
        if node == root:
            print(f"{node}")
            new_prefix = ''
        else:
            connector = '└── ' if is_last else '├── '
            print(f"{prefix}{connector}{node}")
            new_prefix = prefix + ('    ' if is_last else '│   ')

        # Stop if the current node is a file (leaf node)
        if graph.nodes[node].get('type') == NODE_TYPE_FILE:
            return

        # Traverse neighbors with edge type 'contains'
        neighbors = list(graph.neighbors(node))
        for i, neighbor in enumerate(neighbors):
            for key in graph[node][neighbor]:
                if graph[node][neighbor][key].get('type') == EDGE_TYPE_CONTAINS:
                    is_last_child = (i == len(neighbors) - 1)
                    traverse(neighbor, new_prefix, is_last_child)

    traverse(root, '', False)


def main():
    """Main function to build and analyze Java dependency graph"""
    parser = argparse.ArgumentParser(description='Build dependency graph for Java projects')
    parser.add_argument('--repo_path', type=str, required=True,
                        help='Path to the Java repository root')
    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualization of the dependency graph')
    parser.add_argument('--fuzzy_search', action='store_true', default=True,
                        help='Enable fuzzy search for class and method matching')
    parser.add_argument('--output_dir', type=str, default='plots',
                        help='Directory to save visualization plots')

    args = parser.parse_args()

    # Ensure output directory exists
    if args.visualize:
        os.makedirs(args.output_dir, exist_ok=True)

    print(f"Building Java dependency graph for: {args.repo_path}")

    try:
        # Build the dependency graph
        graph = build_graph(args.repo_path, fuzzy_search=args.fuzzy_search)

        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")

        # Print statistics
        node_types = []
        edge_types = []

        for node, data in graph.nodes(data=True):
            node_types.append(data.get('type', 'unknown'))

        for _, _, data in graph.edges(data=True):
            edge_types.append(data.get('type', 'unknown'))

        print("\nNode type distribution:")
        node_counter = Counter(node_types)
        for node_type, count in node_counter.items():
            print(f"  {node_type}: {count}")

        print("\nEdge type distribution:")
        edge_counter = Counter(edge_types)
        for edge_type, count in edge_counter.items():
            print(f"  {edge_type}: {count}")

        # Print directory structure
        print("\nProject structure:")
        traverse_java_directory_structure(graph)

        # Generate visualization if requested
        if args.visualize:
            print(f"\nGenerating visualization...")
            visualize_java_graph(graph)
            print(f"Visualization saved to {args.output_dir}/java_dependency_graph.png")

        # Save graph for further analysis (optional)
        with open(f"{args.output_dir}/java_graph.pkl", "wb") as f:
            pickle.dump(graph, f)
        print(f"Graph saved to {args.output_dir}/java_graph.pkl")

    except Exception as e:
        print(f"Error building graph: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


def remove_file_nodes(graph: nx.MultiDiGraph, filename: str, graph_indexes=None) -> None:
    """
    Remove all nodes related to a file from the graph
    This includes the file node itself and all class/method nodes within the file
    """
    nodes_to_remove = []

    # Find all nodes related to this file
    for node in graph.nodes():
        if node == filename or node.startswith(f"{filename}:"):
            nodes_to_remove.append(node)

    # Remove all found nodes (this will also remove associated edges)
    for node in nodes_to_remove:
        if graph.has_node(node):
            # Remove from indexes first
            if graph_indexes:
                graph_indexes.remove_node(node)
            graph.remove_node(node)

    if len(nodes_to_remove) > 0:
        print(f"Removed {len(nodes_to_remove)} nodes for file: {filename}")


def remove_directory_if_empty(graph: nx.MultiDiGraph, dirname: str) -> None:
    """
    Remove directory node if it has no children (contains no files or subdirectories)
    """
    if not graph.has_node(dirname):
        return

    # Check if directory has any children (outgoing 'contains' edges)
    has_children = False
    for successor in graph.successors(dirname):
        for edge_data in graph[dirname][successor].values():
            if edge_data.get("type") == EDGE_TYPE_CONTAINS:
                has_children = True
                break
        if has_children:
            break

    # If no children, remove the directory
    if not has_children and dirname != "/":  # Don't remove root directory
        graph.remove_node(dirname)
        print(f"Removed empty directory: {dirname}")

        # Recursively check parent directory
        parent_dirname = os.path.dirname(dirname)
        if parent_dirname == "":
            parent_dirname = "/"
        remove_directory_if_empty(graph, parent_dirname)


def ensure_directory_structure(graph: nx.MultiDiGraph, dirname: str) -> None:
    """
    Ensure that all parent directories exist in the graph
    """
    if dirname == "/" or dirname == "." or dirname == "":
        return

    # Normalize the path
    dirname = dirname.replace("\\", "/")

    # If directory already exists, we're done
    if graph.has_node(dirname):
        return

    # Create the directory node
    graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)

    # Ensure parent directory exists and create edge
    parent_dirname = os.path.dirname(dirname)
    if parent_dirname == "" or parent_dirname == ".":
        parent_dirname = "/"

    ensure_directory_structure(graph, parent_dirname)
    graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)


def get_repo_change_files(repo_path: str) -> tuple[list[str], list[str]]:
    """
    Get the list of files that have been added/modified or removed in the repo.
    """

    refresh_files = []
    removed_files = []

    for dir, _, files in os.walk(repo_path):
        for file in files:
            if is_delete_file(file):
                raw_file = get_raw_name_of_delete_file(file)
                removed_files.append(
                    os.path.relpath(os.path.join(dir, raw_file), repo_path)
                )
            else:
                refresh_files.append(
                    os.path.relpath(os.path.join(dir, file), repo_path)
                )

    return refresh_files, removed_files


def build_graph(
    graph: nx.MultiDiGraph | None,
    repo_path: str,
    fuzzy_search: bool = True,
    global_import: bool = False,
) -> nx.MultiDiGraph:
    """
    Build dependency graph for Java files in the repository with incremental updates
    """

    if graph is None:
        graph = nx.MultiDiGraph()

    # Initialize optimization components
    class_resolver = ClassResolver(repo_path)
    graph_indexes = GraphIndexes()
    # If graph has existing nodes, rebuild indexes
    if graph.number_of_nodes() > 0:
        print("Rebuilding indexes for existing graph...")
        for node, attrs in graph.nodes(data=True):
            node_type = attrs.get('type')
            if node_type in [NODE_TYPE_CLASS, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM, NODE_TYPE_FUNCTION]:
                graph_indexes.add_node(node, node_type)

    refresh_files, removed_files = get_repo_change_files(repo_path)

    if graph.number_of_nodes() != 0 and not refresh_files and not removed_files:
        print("No Java files have been added/deleted/modified. Skipping all build steps...")
        return graph

    # If graph is empty (first time), treat all Java files as added files
    if graph.number_of_nodes() == 0:
        print("Graph is empty, performing full build...")
        refresh_files = []
        for root, _, files in os.walk(repo_path):
            for file in files:
                if file.endswith(".java"):
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, repo_path)
                    if not os.path.islink(file_path) and not is_skip_dir(
                        os.path.dirname(filename)
                    ):
                        refresh_files.append(filename)
        removed_files = []

    print(
        f"Incremental build: {len(refresh_files)} added/modified, {len(removed_files)} deleted."
    )

    # Step 1: Remove nodes for deleted and modified files
    files_to_remove = removed_files + refresh_files

    for filename in files_to_remove:
        remove_file_nodes(graph, filename, graph_indexes)
        # Also remove empty parent directories
        dirname = os.path.dirname(filename)
        if dirname and dirname != ".":
            remove_directory_if_empty(graph, dirname)

    # Step 2: Process added and modified files
    files_to_process = refresh_files

    if not files_to_process:
        print("No files to process, returning existing graph")
        return graph

    file_nodes = {}

    # Ensure root directory node exists
    if not graph.has_node("/"):
        graph.add_node("/", type=NODE_TYPE_DIRECTORY)

    # Step 3: Process files to add/update

    # Determine optimal processing strategy
    num_files = len(files_to_process)
    max_workers = min(cpu_count(), 4)
    # Use multiprocessing only if it's beneficial
    use_multiprocessing = num_files > 100 and max_workers > 1

    if use_multiprocessing:
        print(f"Using {max_workers} processes for parallel file processing...")

        # Calculate optimal batch size for load balancing
        # Aim for 2-4 batches per worker to handle varying file sizes
        target_batches = max_workers * 3
        batch_size = max(1, num_files // target_batches)

        # Create batches
        file_batches = []
        for i in range(0, num_files, batch_size):
            batch = files_to_process[i:i + batch_size]
            file_batches.append((batch, repo_path))

        print(f"Created {len(file_batches)} batches with average size {batch_size}")

        processed_files = []
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit batch tasks
            future_to_batch = {
                executor.submit(process_file_batch, batch_args): i
                for i, batch_args in enumerate(file_batches)
            }

            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_results = future.result()
                    processed_files.extend(batch_results)
                except Exception as e:
                    print(f"Error processing batch {batch_idx}: {e}")

        print(f"Successfully processed {len(processed_files)} files in parallel")
    else:
        # Single-threaded processing for small number of files
        print("Using single-threaded processing...")
        processed_files = []
        for filename in files_to_process:
            result = process_single_file((filename, repo_path))
            if result is not None:
                processed_files.append(result)

    # Add processed results to graph
    for file_result in processed_files:
        filename = file_result['filename']
        file_content = file_result['file_content']
        package_name = file_result['package_name']
        nodes = file_result['nodes']
        imports = file_result['imports']
        file_path = file_result['file_path']

        # Add file node
        graph.add_node(filename, type=NODE_TYPE_FILE, code=file_content)

        # Ensure directory structure exists
        dirname = os.path.dirname(filename)
        if dirname and dirname != ".":
            ensure_directory_structure(graph, dirname)
            graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
        else:
            graph.add_edge("/", filename, type=EDGE_TYPE_CONTAINS)

        # Add class/method/interface nodes
        for node in nodes:
            full_name = f'{filename}:{node["name"]}'
            node_attrs = {
                "type": node["type"],
                "code": node["code"],
                "start_line": node["start_line"],
                "end_line": node["end_line"],
            }

            # Add type-specific attributes
            if "extends" in node:
                node_attrs["extends"] = node["extends"]
            if "implements" in node:
                node_attrs["implements"] = node["implements"]
            if "invocations" in node:
                node_attrs["invocations"] = node["invocations"]
            if "is_constructor" in node:
                node_attrs["is_constructor"] = node["is_constructor"]

            graph.add_node(full_name, **node_attrs)

            # Add to indexes
            graph_indexes.add_node(full_name, node["type"], node["name"])

            # Add containment edges
            name_parts = node["name"].split(".")
            if len(name_parts) == 1:
                # Top-level class/interface
                graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
            else:
                # Nested class/method
                parent_name = ".".join(name_parts[:-1])
                full_parent_name = f"{filename}:{parent_name}"
                if graph.has_node(full_parent_name):
                    graph.add_edge(
                        full_parent_name, full_name, type=EDGE_TYPE_CONTAINS
                    )
                else:
                    # Fallback to file containment
                    graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)

        # Store imports for later processing
        file_nodes[filename] = {
            "path": file_path,
            "imports": imports,
            "package": package_name,
        }

    # Step 4: Add import edges for processed files
    for filename, file_info in file_nodes.items():
        if isinstance(file_info, dict):
            file_path = file_info["path"]
            imports = file_info["imports"]
            package_name = file_info["package"]

            # Add import edges
            add_java_imports(filename, imports, graph, repo_path, package_name, class_resolver)

    # Step 5: Add inheritance, implementation, and method invocation edges
    # Only process nodes from the files we just added/modified
    processed_file_nodes = set()
    for filename in files_to_process:
        for node in graph.nodes():
            if node.startswith(f"{filename}:"):
                processed_file_nodes.add(node)

    for node in processed_file_nodes:
        if not graph.has_node(node):
            continue

        attributes = graph.nodes[node]
        if attributes.get("type") not in [
            NODE_TYPE_CLASS,
            NODE_TYPE_FUNCTION,
            NODE_TYPE_INTERFACE,
            NODE_TYPE_ENUM,
        ]:
            continue

        # Handle inheritance (extends)
        if "extends" in attributes:
            for parent_class in attributes["extends"]:
                # Find the parent class node using optimized indexes
                parent_nodes = find_class_nodes(graph, parent_class, fuzzy_search, graph_indexes)
                for parent_node in parent_nodes:
                    graph.add_edge(node, parent_node, type=EDGE_TYPE_INHERITS)

        # Handle implementation (implements)
        if "implements" in attributes:
            for interface in attributes["implements"]:
                # Find the interface node using optimized indexes
                interface_nodes = find_class_nodes(graph, interface, fuzzy_search, graph_indexes)
                for interface_node in interface_nodes:
                    graph.add_edge(node, interface_node, type=EDGE_TYPE_IMPLEMENTS)

        # Handle method invocations
        if "invocations" in attributes:
            for method_name in attributes["invocations"]:
                # Find possible method nodes using optimized indexes
                method_nodes = find_method_nodes(graph, method_name, fuzzy_search, graph_indexes)
                for method_node in method_nodes:
                    graph.add_edge(node, method_node, type=EDGE_TYPE_INVOKES)

    return graph


if __name__ == '__main__':
    exit(main())
