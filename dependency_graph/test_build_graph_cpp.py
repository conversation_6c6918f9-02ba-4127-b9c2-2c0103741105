#!/usr/bin/env python3
"""
Test script for build_graph_cpp.py
"""

import os
import tempfile
import shutil
from build_graph_cpp import build_graph, NODE_TYPE_CLASS, NODE_TYPE_FUNCTION, NODE_TYPE_METHOD, NODE_TYPE_FILE, EDGE_TYPE_CONTAINS, EDGE_TYPE_INCLUDES, EDGE_TYPE_INHERITS


def create_test_cpp_project():
    """Create a temporary C++ project for testing"""
    temp_dir = tempfile.mkdtemp()
    
    # Create directory structure
    src_dir = os.path.join(temp_dir, "src")
    include_dir = os.path.join(temp_dir, "include")
    os.makedirs(src_dir, exist_ok=True)
    os.makedirs(include_dir, exist_ok=True)
    
    # Create Animal.h
    animal_h = """#ifndef ANIMAL_H
#define ANIMAL_H

#include <string>

namespace Zoo {
    class Animal {
    protected:
        std::string name;
        int age;
        
    public:
        Animal(const std::string& name, int age);
        virtual ~Animal();
        
        virtual void makeSound() = 0;
        virtual std::string getType() const = 0;
        
        std::string getName() const;
        int getAge() const;
        void setAge(int age);
    };
}

#endif // ANIMAL_H
"""
    
    # Create Dog.h
    dog_h = """#ifndef DOG_H
#define DOG_H

#include "Animal.h"

namespace Zoo {
    class Dog : public Animal {
    private:
        std::string breed;
        
    public:
        Dog(const std::string& name, int age, const std::string& breed);
        virtual ~Dog();
        
        void makeSound() override;
        std::string getType() const override;
        
        std::string getBreed() const;
        void setBreed(const std::string& breed);
        
        void wagTail();
    };
}

#endif // DOG_H
"""
    
    # Create main.cpp
    main_cpp = """#include <iostream>
#include <vector>
#include <memory>
#include "Animal.h"
#include "Dog.h"

using namespace Zoo;
using namespace std;

int main() {
    cout << "Zoo Management System" << endl;
    
    vector<unique_ptr<Animal>> animals;
    
    animals.push_back(make_unique<Dog>("Buddy", 3, "Golden Retriever"));
    animals.push_back(make_unique<Dog>("Max", 5, "German Shepherd"));
    
    for (const auto& animal : animals) {
        cout << "Animal: " << animal->getName() 
             << ", Age: " << animal->getAge()
             << ", Type: " << animal->getType() << endl;
        animal->makeSound();
    }
    
    return 0;
}
"""
    
    # Create Animal.cpp
    animal_cpp = """#include "Animal.h"
#include <iostream>

namespace Zoo {
    Animal::Animal(const std::string& name, int age) 
        : name(name), age(age) {
    }
    
    Animal::~Animal() {
    }
    
    std::string Animal::getName() const {
        return name;
    }
    
    int Animal::getAge() const {
        return age;
    }
    
    void Animal::setAge(int age) {
        this->age = age;
    }
}
"""
    
    # Create Dog.cpp
    dog_cpp = """#include "Dog.h"
#include <iostream>

namespace Zoo {
    Dog::Dog(const std::string& name, int age, const std::string& breed)
        : Animal(name, age), breed(breed) {
    }
    
    Dog::~Dog() {
    }
    
    void Dog::makeSound() {
        std::cout << name << " says: Woof! Woof!" << std::endl;
    }
    
    std::string Dog::getType() const {
        return "Dog (" + breed + ")";
    }
    
    std::string Dog::getBreed() const {
        return breed;
    }
    
    void Dog::setBreed(const std::string& breed) {
        this->breed = breed;
    }
    
    void Dog::wagTail() {
        std::cout << name << " is wagging its tail!" << std::endl;
    }
}
"""
    
    # Write files
    with open(os.path.join(include_dir, "Animal.h"), "w") as f:
        f.write(animal_h)
    
    with open(os.path.join(include_dir, "Dog.h"), "w") as f:
        f.write(dog_h)
    
    with open(os.path.join(src_dir, "main.cpp"), "w") as f:
        f.write(main_cpp)
    
    with open(os.path.join(src_dir, "Animal.cpp"), "w") as f:
        f.write(animal_cpp)
    
    with open(os.path.join(src_dir, "Dog.cpp"), "w") as f:
        f.write(dog_cpp)
    
    return temp_dir


def test_cpp_graph_building():
    """Test the C++ graph building functionality"""
    print("Creating test C++ project...")
    temp_dir = create_test_cpp_project()
    
    try:
        print(f"Test project created at: {temp_dir}")
        
        # Build the graph
        print("Building C++ dependency graph...")
        graph, _ = build_graph(temp_dir)
        
        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")
        
        # Check for expected nodes
        cpp_files = []
        classes = []
        functions = []
        methods = []
        
        for node, data in graph.nodes(data=True):
            node_type = data.get('type')
            if node_type == NODE_TYPE_FILE and (node.endswith('.cpp') or node.endswith('.h')):
                cpp_files.append(node)
            elif node_type == NODE_TYPE_CLASS:
                classes.append(node)
            elif node_type == NODE_TYPE_FUNCTION:
                functions.append(node)
            elif node_type == NODE_TYPE_METHOD:
                methods.append(node)
        
        print(f"\nFound {len(cpp_files)} C++/H files:")
        for file in cpp_files:
            print(f"  - {file}")
        
        print(f"\nFound {len(classes)} classes:")
        for cls in classes:
            print(f"  - {cls}")
        
        print(f"\nFound {len(functions)} functions:")
        for function in functions:
            print(f"  - {function}")
        
        print(f"\nFound {len(methods)} methods:")
        for method in methods[:10]:  # Show first 10 methods
            print(f"  - {method}")
        if len(methods) > 10:
            print(f"  ... and {len(methods) - 10} more")
        
        # Check for expected edges
        contains_edges = []
        include_edges = []
        inherit_edges = []
        
        for u, v, data in graph.edges(data=True):
            edge_type = data.get('type')
            if edge_type == EDGE_TYPE_CONTAINS:
                contains_edges.append((u, v))
            elif edge_type == EDGE_TYPE_INCLUDES:
                include_edges.append((u, v))
            elif edge_type == EDGE_TYPE_INHERITS:
                inherit_edges.append((u, v))
        
        print(f"\nFound {len(contains_edges)} containment edges")
        print(f"Found {len(include_edges)} include edges")
        print(f"Found {len(inherit_edges)} inheritance edges")
        
        # Verify expected structure
        expected_files = ['main.cpp', 'Animal.cpp', 'Dog.cpp', 'Animal.h', 'Dog.h']
        found_files = [f for f in cpp_files if any(expected in f for expected in expected_files)]
        
        if len(found_files) >= 5:
            print("\n✅ Test PASSED: Found expected C++/H files")
        else:
            print(f"\n❌ Test FAILED: Expected 5 files, found {len(found_files)}")
        
        if len(classes) >= 2:
            print("✅ Test PASSED: Found expected classes")
        else:
            print(f"❌ Test FAILED: Expected at least 2 classes, found {len(classes)}")
        
        if len(functions) + len(methods) >= 5:
            print("✅ Test PASSED: Found expected functions/methods")
        else:
            print(f"❌ Test FAILED: Expected at least 5 functions/methods, found {len(functions) + len(methods)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test FAILED with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        print(f"\nCleaning up test directory: {temp_dir}")
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    print("Testing C++ dependency graph builder...")
    success = test_cpp_graph_building()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
        exit(1)
