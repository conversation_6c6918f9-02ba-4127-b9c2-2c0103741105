#!/usr/bin/env python3
"""
Test script for build_graph_go.py
"""

import os
import tempfile
import shutil
from build_graph_go import build_graph, NODE_TYPE_STRUCT, NODE_TYPE_FUNCTION, NODE_TYPE_FILE, EDGE_TYPE_CONTAINS, EDGE_TYPE_IMPORTS


def create_test_go_project():
    """Create a temporary Go project for testing"""
    temp_dir = tempfile.mkdtemp()
    
    # Create directory structure
    main_dir = os.path.join(temp_dir, "cmd", "myapp")
    pkg_dir = os.path.join(temp_dir, "pkg", "models")
    os.makedirs(main_dir, exist_ok=True)
    os.makedirs(pkg_dir, exist_ok=True)
    
    # Create go.mod
    go_mod = """module github.com/example/myapp

go 1.19

require (
    github.com/gorilla/mux v1.8.0
)
"""
    
    # Create main.go
    main_go = """package main

import (
    "fmt"
    "log"
    "net/http"
    
    "github.com/example/myapp/pkg/models"
    "github.com/gorilla/mux"
)

func main() {
    router := mux.NewRouter()
    
    user := models.NewUser("Alice", 25)
    fmt.Printf("Created user: %s\\n", user.GetName())
    
    router.HandleFunc("/users", handleUsers).Methods("GET")
    
    log.Println("Server starting on :8080")
    log.Fatal(http.ListenAndServe(":8080", router))
}

func handleUsers(w http.ResponseWriter, r *http.Request) {
    users := models.GetAllUsers()
    for _, user := range users {
        fmt.Fprintf(w, "User: %s, Age: %d\\n", user.GetName(), user.GetAge())
    }
}
"""
    
    # Create user.go
    user_go = """package models

import "fmt"

type User struct {
    Name string
    Age  int
}

func NewUser(name string, age int) *User {
    return &User{
        Name: name,
        Age:  age,
    }
}

func (u *User) GetName() string {
    return u.Name
}

func (u *User) SetName(name string) {
    u.Name = name
}

func (u *User) GetAge() int {
    return u.Age
}

func (u *User) SetAge(age int) {
    u.Age = age
}

func (u *User) String() string {
    return fmt.Sprintf("User{Name: %s, Age: %d}", u.Name, u.Age)
}
"""
    
    # Create service.go
    service_go = """package models

var users []*User

func init() {
    users = []*User{
        NewUser("Bob", 30),
        NewUser("Charlie", 35),
    }
}

func GetAllUsers() []*User {
    result := make([]*User, len(users))
    copy(result, users)
    return result
}

func AddUser(user *User) {
    users = append(users, user)
}

func FindUserByName(name string) *User {
    for _, user := range users {
        if user.GetName() == name {
            return user
        }
    }
    return nil
}
"""
    
    # Write files
    with open(os.path.join(temp_dir, "go.mod"), "w") as f:
        f.write(go_mod)
    
    with open(os.path.join(main_dir, "main.go"), "w") as f:
        f.write(main_go)
    
    with open(os.path.join(pkg_dir, "user.go"), "w") as f:
        f.write(user_go)
    
    with open(os.path.join(pkg_dir, "service.go"), "w") as f:
        f.write(service_go)
    
    return temp_dir


def test_go_graph_building():
    """Test the Go graph building functionality"""
    print("Creating test Go project...")
    temp_dir = create_test_go_project()
    
    try:
        print(f"Test project created at: {temp_dir}")
        
        # Build the graph
        print("Building Go dependency graph...")
        graph, _ = build_graph(temp_dir)
        
        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")
        
        # Check for expected nodes
        go_files = []
        structs = []
        functions = []
        
        for node, data in graph.nodes(data=True):
            node_type = data.get('type')
            if node_type == NODE_TYPE_FILE and node.endswith('.go'):
                go_files.append(node)
            elif node_type == NODE_TYPE_STRUCT:
                structs.append(node)
            elif node_type == NODE_TYPE_FUNCTION:
                functions.append(node)
        
        print(f"\nFound {len(go_files)} Go files:")
        for file in go_files:
            print(f"  - {file}")
        
        print(f"\nFound {len(structs)} structs:")
        for struct in structs:
            print(f"  - {struct}")
        
        print(f"\nFound {len(functions)} functions:")
        for function in functions[:10]:  # Show first 10 functions
            print(f"  - {function}")
        if len(functions) > 10:
            print(f"  ... and {len(functions) - 10} more")
        
        # Check for expected edges
        contains_edges = []
        import_edges = []
        
        for u, v, data in graph.edges(data=True):
            edge_type = data.get('type')
            if edge_type == EDGE_TYPE_CONTAINS:
                contains_edges.append((u, v))
            elif edge_type == EDGE_TYPE_IMPORTS:
                import_edges.append((u, v))
        
        print(f"\nFound {len(contains_edges)} containment edges")
        print(f"Found {len(import_edges)} import edges")
        
        # Verify expected structure
        expected_files = ['main.go', 'user.go', 'service.go']
        found_files = [f for f in go_files if any(expected in f for expected in expected_files)]
        
        if len(found_files) >= 3:
            print("\n✅ Test PASSED: Found expected Go files")
        else:
            print(f"\n❌ Test FAILED: Expected 3 files, found {len(found_files)}")
        
        if len(structs) >= 1:
            print("✅ Test PASSED: Found expected structs")
        else:
            print(f"❌ Test FAILED: Expected at least 1 struct, found {len(structs)}")
        
        if len(functions) >= 5:
            print("✅ Test PASSED: Found expected functions")
        else:
            print(f"❌ Test FAILED: Expected at least 5 functions, found {len(functions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test FAILED with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        print(f"\nCleaning up test directory: {temp_dir}")
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    print("Testing Go dependency graph builder...")
    success = test_go_graph_building()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
        exit(1)
