#!/usr/bin/env python3
"""
Test script for build_graph_c.py
"""

import os
import tempfile
import shutil
from build_graph_c import build_graph, NODE_TYPE_STRUCT, NODE_TYPE_FUNCTION, NODE_TYPE_FILE, EDGE_TYPE_CONTAINS, EDGE_TYPE_INCLUDES


def create_test_c_project():
    """Create a temporary C project for testing"""
    temp_dir = tempfile.mkdtemp()
    
    # Create directory structure
    src_dir = os.path.join(temp_dir, "src")
    include_dir = os.path.join(temp_dir, "include")
    os.makedirs(src_dir, exist_ok=True)
    os.makedirs(include_dir, exist_ok=True)
    
    # Create user.h
    user_h = """#ifndef USER_H
#define USER_H

typedef struct {
    char name[50];
    int age;
} User;

// Function declarations
User* create_user(const char* name, int age);
void destroy_user(User* user);
const char* get_user_name(User* user);
int get_user_age(User* user);
void set_user_age(User* user, int age);

#endif // USER_H
"""
    
    # Create main.c
    main_c = """#include <stdio.h>
#include <stdlib.h>
#include "user.h"

int main() {
    printf("User Management System\\n");
    
    User* user1 = create_user("Alice", 25);
    User* user2 = create_user("Bob", 30);
    
    printf("User 1: %s, Age: %d\\n", get_user_name(user1), get_user_age(user1));
    printf("User 2: %s, Age: %d\\n", get_user_name(user2), get_user_age(user2));
    
    set_user_age(user1, 26);
    printf("Updated User 1 age: %d\\n", get_user_age(user1));
    
    destroy_user(user1);
    destroy_user(user2);
    
    return 0;
}
"""
    
    # Create user.c
    user_c = """#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "user.h"

User* create_user(const char* name, int age) {
    User* user = (User*)malloc(sizeof(User));
    if (user == NULL) {
        return NULL;
    }
    
    strncpy(user->name, name, sizeof(user->name) - 1);
    user->name[sizeof(user->name) - 1] = '\\0';
    user->age = age;
    
    return user;
}

void destroy_user(User* user) {
    if (user != NULL) {
        free(user);
    }
}

const char* get_user_name(User* user) {
    if (user == NULL) {
        return NULL;
    }
    return user->name;
}

int get_user_age(User* user) {
    if (user == NULL) {
        return -1;
    }
    return user->age;
}

void set_user_age(User* user, int age) {
    if (user != NULL) {
        user->age = age;
    }
}
"""
    
    # Create utils.h
    utils_h = """#ifndef UTILS_H
#define UTILS_H

#define MAX_BUFFER_SIZE 1024
#define SUCCESS 0
#define ERROR -1

// Utility functions
void print_separator(void);
int string_length(const char* str);

#endif // UTILS_H
"""
    
    # Create utils.c
    utils_c = """#include <stdio.h>
#include "utils.h"

void print_separator(void) {
    printf("================================\\n");
}

int string_length(const char* str) {
    if (str == NULL) {
        return 0;
    }
    
    int length = 0;
    while (str[length] != '\\0') {
        length++;
    }
    return length;
}
"""
    
    # Write files
    with open(os.path.join(include_dir, "user.h"), "w") as f:
        f.write(user_h)
    
    with open(os.path.join(include_dir, "utils.h"), "w") as f:
        f.write(utils_h)
    
    with open(os.path.join(src_dir, "main.c"), "w") as f:
        f.write(main_c)
    
    with open(os.path.join(src_dir, "user.c"), "w") as f:
        f.write(user_c)
    
    with open(os.path.join(src_dir, "utils.c"), "w") as f:
        f.write(utils_c)
    
    return temp_dir


def test_c_graph_building():
    """Test the C graph building functionality"""
    print("Creating test C project...")
    temp_dir = create_test_c_project()
    
    try:
        print(f"Test project created at: {temp_dir}")
        
        # Build the graph
        print("Building C dependency graph...")
        graph, _ = build_graph(temp_dir)
        
        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")
        
        # Check for expected nodes
        c_files = []
        structs = []
        functions = []
        
        for node, data in graph.nodes(data=True):
            node_type = data.get('type')
            if node_type == NODE_TYPE_FILE and (node.endswith('.c') or node.endswith('.h')):
                c_files.append(node)
            elif node_type == NODE_TYPE_STRUCT:
                structs.append(node)
            elif node_type == NODE_TYPE_FUNCTION:
                functions.append(node)
        
        print(f"\nFound {len(c_files)} C/H files:")
        for file in c_files:
            print(f"  - {file}")
        
        print(f"\nFound {len(structs)} structs:")
        for struct in structs:
            print(f"  - {struct}")
        
        print(f"\nFound {len(functions)} functions:")
        for function in functions[:10]:  # Show first 10 functions
            print(f"  - {function}")
        if len(functions) > 10:
            print(f"  ... and {len(functions) - 10} more")
        
        # Check for expected edges
        contains_edges = []
        include_edges = []
        
        for u, v, data in graph.edges(data=True):
            edge_type = data.get('type')
            if edge_type == EDGE_TYPE_CONTAINS:
                contains_edges.append((u, v))
            elif edge_type == EDGE_TYPE_INCLUDES:
                include_edges.append((u, v))
        
        print(f"\nFound {len(contains_edges)} containment edges")
        print(f"Found {len(include_edges)} include edges")
        
        # Verify expected structure
        expected_files = ['main.c', 'user.c', 'user.h', 'utils.c', 'utils.h']
        found_files = [f for f in c_files if any(expected in f for expected in expected_files)]
        
        if len(found_files) >= 5:
            print("\n✅ Test PASSED: Found expected C/H files")
        else:
            print(f"\n❌ Test FAILED: Expected 5 files, found {len(found_files)}")
        
        if len(structs) >= 1:
            print("✅ Test PASSED: Found expected structs")
        else:
            print(f"❌ Test FAILED: Expected at least 1 struct, found {len(structs)}")
        
        if len(functions) >= 5:
            print("✅ Test PASSED: Found expected functions")
        else:
            print(f"❌ Test FAILED: Expected at least 5 functions, found {len(functions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test FAILED with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        print(f"\nCleaning up test directory: {temp_dir}")
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    print("Testing C dependency graph builder...")
    success = test_c_graph_building()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
        exit(1)
