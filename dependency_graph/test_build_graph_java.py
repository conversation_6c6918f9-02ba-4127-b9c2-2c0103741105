#!/usr/bin/env python3
"""
Test script for build_graph_java.py
"""

import os
import tempfile
import shutil
from build_graph_java import build_graph, NODE_TYPE_CLASS, NODE_TYPE_FUNCTION, NODE_TYPE_FILE, EDGE_TYPE_CONTAINS, EDGE_TYPE_IMPORTS


def create_test_java_project():
    """Create a temporary Java project for testing"""
    temp_dir = tempfile.mkdtemp()
    
    # Create directory structure
    src_dir = os.path.join(temp_dir, "src", "main", "java", "com", "example")
    os.makedirs(src_dir, exist_ok=True)
    
    # Create Main.java
    main_java = """package com.example;

import java.util.List;
import java.util.ArrayList;

public class Main {
    private UserService userService;
    
    public Main() {
        this.userService = new UserService();
    }
    
    public static void main(String[] args) {
        Main app = new Main();
        app.run();
    }
    
    public void run() {
        List<User> users = userService.getAllUsers();
        for (User user : users) {
            System.out.println(user.getName());
        }
    }
}
"""
    
    # Create User.java
    user_java = """package com.example;

public class User {
    private String name;
    private int age;
    
    public User(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
}
"""
    
    # Create UserService.java
    service_java = """package com.example;

import java.util.List;
import java.util.ArrayList;

public class UserService {
    private List<User> users;
    
    public UserService() {
        this.users = new ArrayList<>();
        initializeUsers();
    }
    
    private void initializeUsers() {
        users.add(new User("Alice", 25));
        users.add(new User("Bob", 30));
    }
    
    public List<User> getAllUsers() {
        return new ArrayList<>(users);
    }
    
    public User getUserByName(String name) {
        for (User user : users) {
            if (user.getName().equals(name)) {
                return user;
            }
        }
        return null;
    }
}
"""
    
    # Write files
    with open(os.path.join(src_dir, "Main.java"), "w") as f:
        f.write(main_java)
    
    with open(os.path.join(src_dir, "User.java"), "w") as f:
        f.write(user_java)
    
    with open(os.path.join(src_dir, "UserService.java"), "w") as f:
        f.write(service_java)
    
    return temp_dir


def test_java_graph_building():
    """Test the Java graph building functionality"""
    print("Creating test Java project...")
    temp_dir = create_test_java_project()
    
    try:
        print(f"Test project created at: {temp_dir}")
        
        # Build the graph
        print("Building Java dependency graph...")
        graph = build_graph(temp_dir)
        
        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")
        
        # Check for expected nodes
        java_files = []
        classes = []
        methods = []
        
        for node, data in graph.nodes(data=True):
            node_type = data.get('type')
            if node_type == NODE_TYPE_FILE and node.endswith('.java'):
                java_files.append(node)
            elif node_type == NODE_TYPE_CLASS:
                classes.append(node)
            elif node_type == NODE_TYPE_FUNCTION:
                methods.append(node)
        
        print(f"\nFound {len(java_files)} Java files:")
        for file in java_files:
            print(f"  - {file}")
        
        print(f"\nFound {len(classes)} classes:")
        for cls in classes:
            print(f"  - {cls}")
        
        print(f"\nFound {len(methods)} methods:")
        for method in methods[:10]:  # Show first 10 methods
            print(f"  - {method}")
        if len(methods) > 10:
            print(f"  ... and {len(methods) - 10} more")
        
        # Check for expected edges
        contains_edges = []
        import_edges = []
        
        for u, v, data in graph.edges(data=True):
            edge_type = data.get('type')
            if edge_type == EDGE_TYPE_CONTAINS:
                contains_edges.append((u, v))
            elif edge_type == EDGE_TYPE_IMPORTS:
                import_edges.append((u, v))
        
        print(f"\nFound {len(contains_edges)} containment edges")
        print(f"Found {len(import_edges)} import edges")
        
        # Verify expected structure
        expected_files = ['Main.java', 'User.java', 'UserService.java']
        found_files = [f for f in java_files if any(expected in f for expected in expected_files)]
        
        if len(found_files) >= 3:
            print("\n✅ Test PASSED: Found expected Java files")
        else:
            print(f"\n❌ Test FAILED: Expected 3 files, found {len(found_files)}")
        
        if len(classes) >= 3:
            print("✅ Test PASSED: Found expected classes")
        else:
            print(f"❌ Test FAILED: Expected at least 3 classes, found {len(classes)}")
        
        if len(methods) >= 5:
            print("✅ Test PASSED: Found expected methods")
        else:
            print(f"❌ Test FAILED: Expected at least 5 methods, found {len(methods)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test FAILED with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        print(f"\nCleaning up test directory: {temp_dir}")
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    print("Testing Java dependency graph builder...")
    success = test_java_graph_building()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
        exit(1)
