"""
Common utilities for C/C++ dependency graph building
"""

import os
import re
import chardet
from typing import List, Dict, Set


def read_file_safely(filepath: str) -> str:
    """Safely read file with encoding detection"""
    with open(filepath, "rb") as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result["encoding"] or "utf-8"
    
    with open(filepath, "r", encoding=encoding) as f:
        return f.read()


def is_c_file(filename: str) -> bool:
    """Check if file is a C source or header file"""
    return filename.endswith(('.c', '.h'))


def is_cpp_file(filename: str) -> bool:
    """Check if file is a C++ source or header file"""
    return filename.endswith(('.cpp', '.cc', '.cxx', '.c++', '.hpp', '.hh', '.hxx', '.h++'))


def is_header_file(filename: str) -> bool:
    """Check if file is a header file"""
    return filename.endswith(('.h', '.hpp', '.hh', '.hxx', '.h++'))


def is_source_file(filename: str) -> bool:
    """Check if file is a source file"""
    return filename.endswith(('.c', '.cpp', '.cc', '.cxx', '.c++'))


def extract_includes_from_content(content: str) -> List[Dict[str, str]]:
    """Extract #include statements from file content"""
    includes = []
    
    # Match #include "file.h" and #include <file.h>
    include_pattern = r'#include\s*([<"])([^>"]+)[>"]'
    
    for match in re.finditer(include_pattern, content):
        bracket_type = match.group(1)
        include_path = match.group(2)
        
        includes.append({
            'path': include_path,
            'is_system': bracket_type == '<',
            'is_local': bracket_type == '"'
        })
    
    return includes


def resolve_include_path(include_path: str, current_file_dir: str, repo_path: str, 
                        include_dirs: List[str] = None) -> str:
    """
    Resolve an include path to an actual file path
    
    Args:
        include_path: The path from #include statement
        current_file_dir: Directory of the file containing the #include
        repo_path: Root directory of the repository
        include_dirs: Additional include directories to search
    
    Returns:
        Resolved file path if found, None otherwise
    """
    if include_dirs is None:
        include_dirs = []
    
    # Try relative to current file directory first
    candidate_path = os.path.join(current_file_dir, include_path)
    if os.path.isfile(candidate_path):
        return os.path.relpath(candidate_path, repo_path)
    
    # Try relative to repo root
    candidate_path = os.path.join(repo_path, include_path)
    if os.path.isfile(candidate_path):
        return os.path.relpath(candidate_path, repo_path)
    
    # Try in additional include directories
    for include_dir in include_dirs:
        candidate_path = os.path.join(include_dir, include_path)
        if os.path.isfile(candidate_path):
            return os.path.relpath(candidate_path, repo_path)
    
    # Try to find the file anywhere in the repo (fuzzy search)
    filename = os.path.basename(include_path)
    for root, dirs, files in os.walk(repo_path):
        if filename in files:
            candidate_path = os.path.join(root, filename)
            return os.path.relpath(candidate_path, repo_path)
    
    return None


def extract_function_calls_from_content(content: str) -> List[str]:
    """Extract function calls from C/C++ content using regex"""
    function_calls = []
    
    # Simple regex to match function calls: identifier followed by (
    # This is not perfect but works for most cases
    call_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    
    for match in re.finditer(call_pattern, content):
        func_name = match.group(1)
        
        # Filter out common keywords that aren't function calls
        if func_name not in ['if', 'while', 'for', 'switch', 'sizeof', 'return', 'case']:
            function_calls.append(func_name)
    
    return function_calls


def find_common_include_directories(repo_path: str) -> List[str]:
    """Find common include directories in the repository"""
    include_dirs = []
    
    # Common include directory names
    common_names = ['include', 'inc', 'headers', 'src']
    
    for root, dirs, files in os.walk(repo_path):
        for dir_name in dirs:
            if dir_name in common_names:
                dir_path = os.path.join(root, dir_name)
                # Check if it contains header files
                for file in os.listdir(dir_path):
                    if is_header_file(file):
                        include_dirs.append(dir_path)
                        break
    
    return include_dirs


def extract_macros_from_content(content: str) -> List[Dict[str, str]]:
    """Extract macro definitions from content"""
    macros = []
    
    # Match #define MACRO_NAME value
    define_pattern = r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*(.*?)(?:\n|$)'
    
    for match in re.finditer(define_pattern, content, re.MULTILINE):
        macro_name = match.group(1)
        macro_value = match.group(2).strip()
        
        macros.append({
            'name': macro_name,
            'value': macro_value,
            'is_function_like': '(' in macro_value
        })
    
    return macros


def get_skip_directories() -> List[str]:
    """Get list of directories to skip during analysis"""
    return [
        '.git', '.github', '.svn', '.hg',  # Version control
        'build', 'builds', 'Build', 'BUILD',  # Build directories
        'cmake-build-debug', 'cmake-build-release',  # CMake build dirs
        'Debug', 'Release', 'RelWithDebInfo', 'MinSizeRel',  # MSVC build configs
        'bin', 'obj', 'out',  # Output directories
        'node_modules', 'vendor',  # Dependencies
        '.idea', '.vscode', '.vs',  # IDE directories
        '__pycache__', '.pytest_cache',  # Python cache
        'test', 'tests', 'Test', 'Tests',  # Test directories (optional)
        'doc', 'docs', 'documentation',  # Documentation
        'examples', 'samples', 'demo'  # Example code
    ]


def is_skip_directory(dirname: str) -> bool:
    """Check if directory should be skipped"""
    skip_dirs = get_skip_directories()
    
    for skip_dir in skip_dirs:
        if skip_dir in dirname:
            return True
    
    return False


def normalize_path(path: str) -> str:
    """Normalize file path for consistent representation"""
    return os.path.normpath(path).replace('\\', '/')


def extract_struct_usage_from_content(content: str) -> List[str]:
    """Extract struct type usage from content"""
    struct_usage = []
    
    # Match struct type usage: struct StructName or StructName
    struct_pattern = r'\b(?:struct\s+)?([A-Z][a-zA-Z0-9_]*)\s+[a-zA-Z_][a-zA-Z0-9_]*'
    
    for match in re.finditer(struct_pattern, content):
        struct_name = match.group(1)
        struct_usage.append(struct_name)
    
    return struct_usage
