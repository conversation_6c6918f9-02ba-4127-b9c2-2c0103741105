# !/bin/sh

COLOR_RED="\e[1;31m"
COLOR_BLUE="\e[1;34m"
COLOR_GREEN="\e[1;32m"
COLOR_YELLOW="\e[1;33m"
COLOR_DEFAULT="\e[0m"
COLOR_BOLD="\e[1m"

IPV4_REGEX='^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
# PORT_REGEX='^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$'
PORT_REGEX='^(6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{3}|10[2-9][0-9]|102[5-9])$'

MODEL_NAME="openai/Qwen3-235B-A22B"
MODEL_API_KEY="8612e7bd0533452f9e88382cf3112ba4"
MODEL_API_BASE="https://maas-apigateway.dt.zte.com.cn/STREAM/qwen3-235b-a22b/v1"

AGENT_MCP_HOST=''
AGENT_MCP_PORT=''
AGENT_REPO_DIR=''
AGENT_TARGET='mcp'

USE_VECTOR_RETRIEVE=''

AGENT_ROOT_DIR="$(cd "$(dirname "${0}")" && pwd)/.."

function set_agent_env() {
    export LOCAGENT_MODEL_NAME="${MODEL_NAME}"

    if [[ ${AGENT_MCP_HOST} == '' ]]; then
        echo "host option is not received, use the default value."
    else
        export LOCAGENT_MCP_HOST="${AGENT_MCP_HOST}"
    fi

    if [[ ${AGENT_MCP_PORT} == '' ]]; then
        echo "host option is not received, use the default value."
    else
        export LOCAGENT_MCP_PORT="${AGENT_MCP_PORT}"
    fi

    export OPENAI_API_KEY="8612e7bd0533452f9e88382cf3112ba4"
    export OPENAI_API_BASE="https://maas-apigateway.dt.zte.com.cn/STREAM/qwen3-235b-a22b/v1"

    export all_proxy="http://proxyhk.zte.com.cn:80"
    export https_proxy="http://proxyhk.zte.com.cn:80"

    if [[ -n "${PYTHONPATH}" ]]; then
        export PYTHONPATH="${PYTHONPATH}:${AGENT_ROOT_DIR}"
    else
        export PYTHONPATH="${AGENT_ROOT_DIR}"
    fi

    export LOCAGENT_ROOT_DIR="${AGENT_ROOT_DIR}"
    export LOCAGENT_REPO_DIR="${AGENT_REPO_DIR}"

    export GRAPH_INDEX_DIR="${HOME}/.zeroAgent/index/graph_index_v2.3"
    export BM25_INDEX_DIR="${HOME}/.zeroAgent/index/BM25_index"
    export VECTOR_INDEX_DIR="${HOME}/.zeroAgent/index/VECTOR_index"

    export USE_VECTOR_RETRIEVE="${USE_VECTOR_RETRIEVE}"
}

function log_error() {
    echo -e "${COLOR_RED}[ERROR]${COLOR_DEFAULT}: $1" >&2
    exit 1
}

function show_options_env() {
    echo -e "${COLOR_BLUE} Options: ${COLOR_DEFAULT}"
    echo -e "${COLOR_BLUE} - target =${COLOR_DEFAULT} ${AGENT_TARGET}"
    echo -e "${COLOR_BLUE} - repo-path =${COLOR_DEFAULT} ${AGENT_REPO_DIR}"
    echo -e "${COLOR_BLUE} - host-ip =${COLOR_DEFAULT} ${AGENT_MCP_HOST}"
    echo -e "${COLOR_BLUE} - port =${COLOR_DEFAULT} ${AGENT_MCP_PORT}"

    echo -e "${COLOR_BLUE} Environmental variable: ${COLOR_DEFAULT}"
    echo -e "${COLOR_BLUE} - LOCAGENT_MODEL_NAME =${COLOR_DEFAULT} ${LOCAGENT_MODEL_NAME}"
    echo -e "${COLOR_BLUE} - OPENAI_API_KEY =${COLOR_DEFAULT} ${OPENAI_API_KEY}"
    echo -e "${COLOR_BLUE} - OPENAI_API_BASE =${COLOR_DEFAULT} ${OPENAI_API_BASE}"
    echo -e "${COLOR_BLUE} - LOCAGENT_MCP_HOST =${COLOR_DEFAULT} ${LOCAGENT_MCP_HOST}"
    echo -e "${COLOR_BLUE} - LOCAGENT_MCP_PORT =${COLOR_DEFAULT} ${LOCAGENT_MCP_PORT}"
    echo -e "${COLOR_BLUE} - LOCAGENT_ROOT_DIR =${COLOR_DEFAULT} ${LOCAGENT_ROOT_DIR}"
    echo -e "${COLOR_BLUE} - LOCAGENT_REPO_DIR =${COLOR_DEFAULT} ${LOCAGENT_REPO_DIR}"
    echo -e "${COLOR_BLUE} - GRAPH_INDEX_DIR =${COLOR_DEFAULT} ${GRAPH_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - BM25_INDEX_DIR =${COLOR_DEFAULT} ${BM25_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - VECTOR_INDEX_DIR =${COLOR_DEFAULT} ${VECTOR_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - PYTHONPATH =${COLOR_DEFAULT} ${PYTHONPATH}"
    echo -e "${COLOR_BLUE} - all_proxy =${COLOR_DEFAULT} ${all_proxy}"
    echo -e "${COLOR_BLUE} - https_proxy =${COLOR_DEFAULT} ${https_proxy}"
    echo -e "${COLOR_BLUE} - USE_VECTOR_RETRIEVE =${COLOR_DEFAULT} ${USE_VECTOR_RETRIEVE}"
}

function show_help() {
    local script_name=$(basename "$0")
    local help_text="\

${COLOR_GREEN}${script_name} - LocAgent MCP Service${COLOR_DEFAULT}

${COLOR_BOLD}Usage:${COLOR_DEFAULT}
  ${script_name} [Options]

${COLOR_BOLD}Required options:${COLOR_DEFAULT}
  --target=<TARGET>      Specify the running target (mcp, mcp_dev, agent, etc.)
  --repo=<REPO_PATH>     Specify the code repository path

${COLOR_BOLD}Optional options:${COLOR_DEFAULT}
  --host=<IP>          Specified service IP (default: 127.0.0.1)
  --port=<PORT>        Specified port (default: 8080)
  --help               Display this help information
  --use_vector_retrieve   Use vector indexing and retrieval
${COLOR_BOLD}Option format:${COLOR_DEFAULT}
  Support --host *********** or --host=*********** Two ways of writing

${COLOR_BOLD}Example:${COLOR_DEFAULT}
  ${script_name} --target=mcp --repo=/data/repo
  ${script_name} --target=agent --repo=/git/repo --host=******** --port=9000
"
    echo -e "${help_text}"
}

function parse_options() {
    local TEMP=$(getopt -o '' --long help,target:,host:,port:,repo:,use_vector_retrieve -n "$0" -- "$@")

    if [[ $? != 0 ]]; then
        log_error "Error: Parameter parsing failed and the program terminated..."
    fi

    eval set -- "${TEMP}"

    while true; do
        case "$1" in
            --help)
                show_help
                exit 0
                ;;
            --target)
                AGENT_TARGET=$2
                shift 2
                ;;
            --host)
                if [[ $2 =~ ${IPV4_REGEX} ]]; then
                    AGENT_MCP_HOST=$2
                else
                    log_error "Invalid IPv4 address format $2"
                fi
                shift 2
                ;;
            --port)
                if [[ $2 =~ ${PORT_REGEX} ]]; then
                    AGENT_MCP_PORT=$2
                else
                    log_error "Invalid port number: $2 (must be between 1025 and 65535)"
                fi
                shift 2
                ;;
            --repo)
                if [[ -d "$2" ]]; then
                    AGENT_REPO_DIR=$2
                else
                    log_error "The directory does not exist: $2"
                fi
                shift 2
                ;;
            --disable_vector_retrieve)
                USE_VECTOR_RETRIEVE='True'
                shift
                ;;
            --)
                shift
                break
                ;;
            *)
                log_error "Unknown option parsing situation."
                ;;
        esac
    done
}

function run_mcp_service() {
    set_agent_env
    show_options_env
    python mcp_service/locagent_mcp.py
}

function run_locagent_main() {
    set_agent_env
    show_options_env
    python mcp_service/locagent_main.py
}

function run_locagent_config() {
    set_agent_env
    show_options_env
    python mcp_service/locagent_config.py
}

function run_mcp_dev() {
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    set_agent_env
    show_options_env
    mcp dev mcp_service/locagent_mcp.py
}

function main() {
    if [[ $# -eq 0 ]];then
        log_error "The parameter cannot be null, Try using --help to view the usage."
    fi

    parse_options "$@"

    if [[ $AGENT_REPO_DIR == '' ]]; then
        log_error "repo option is not received, and the configuration is missing."
    fi

    if [ "${AGENT_TARGET}" == "mcp" ];then
        run_mcp_service
    elif [ "${AGENT_TARGET}" == "mcp_dev" ];then
        run_mcp_dev
    elif [ "${AGENT_TARGET}" == "agent" ];then
        run_locagent_main
    elif [ "${AGENT_TARGET}" == "config" ];then
        run_locagent_config
    else
        log_error "Please enter the correct target."
    fi
}

main "$@"