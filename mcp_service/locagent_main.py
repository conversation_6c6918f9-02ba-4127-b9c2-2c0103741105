import os
import time
import copy
import uuid
from typing import List, Optional, Union

from util.runtime import function_calling
from util.runtime.execute_ipython import execute_ipython
from util.runtime.fn_call_converter import (
    convert_fncall_messages_to_non_fncall_messages,
    convert_non_fncall_messages_to_fncall_messages,
    STOP_WORDS as NON_FNCALL_STOP_WORDS,
)

from util.actions.action import ActionType
from util.actions.action_parser import ResponseParser

from util.prompts.prompt import PromptManager
from util.prompts.pipelines import (
    auto_search_prompt as auto_search,
)

from util.utils import *
from util.cost_analysis import calc_cost
from util.process_output import (
    get_loc_results_from_raw_outputs,
)

from plugins import LocationToolsRequirement
from plugins.location_tools.repo_ops.repo_ops import (
    Index,
    RepoOps,
    get_current_issue_id,
    set_current_instance,
)

import litellm
from litellm import Message as LiteLLMMessage
from litellm import ChatCompletionToolParam

from openai import APITimeoutError

from locagent_config import ConfigManager
from auto_search_main import get_task_instruction


class LocAgentManager:
    def __init__(self, mcp_service: bool = False) -> None:
        self.config_manager = ConfigManager(mcp_service)
        self.config = self.config_manager.config
        self.logger = self.config_manager.logger

        self.logger.info(f"Started LocAgent.")
        self.instance_id = self.config.repo_dir
        self.logger.info(f"Set instance id: {self.instance_id}")

        self.logger.info(f"Build repo index")

        if os.getenv("USE_VECTOR_RETRIEVE") == "True":
            index = Index.build(repo_dir=self.config.repo_dir, use_vector_retriever=True)
        else:
            index = Index.build(repo_dir=self.config.repo_dir)

        set_current_instance(repo_ops=RepoOps(index))

        self.data = {
            "instance_id": self.instance_id.split("/")[-1],
            "repo": self.instance_id.split("/")[-1],
            "branch": "master",
            "base_commit": "HEAD",
            "problem_statement": "",
        }

    def __append_to_json(self, data, file_path) -> None:
        with open(file_path, "a", encoding="utf-8") as file:
            json.dump(data, file, indent=4)

    def __agent_search_process(
        self,
        model_name: str,
        messages: list[dict],
        fake_user_msg: str,
        tools: Optional[list[ChatCompletionToolParam]] = None,
        temperature: float = 1.0,
        max_iteration_num: int = 20,
        use_function_calling: bool = True,
    ) -> tuple[dict]:
        if tools and (
            "hosted_vllm" in model_name
            # or "qwen" in model_name.lower()
            # or model_name=='azure/gpt-4o'
            # or model_name == 'litellm_proxy/o3-mini-2025-01-31'
        ):
            use_function_calling = False

        # for LLM which do not support function calling
        if not use_function_calling:
            # convert message
            messages = convert_fncall_messages_to_non_fncall_messages(
                messages, tools, add_in_context_learning_example=False
            )

        parser = ResponseParser()
        # UPDATE 可以优化
        traj_msgs = messages.copy()
        prompt_tokens = 0
        completion_tokens = 0
        cur_interation_num = 0
        last_message = None
        finish = False
        final_output = ""

        while not finish:
            cur_interation_num += 1
            if cur_interation_num == max_iteration_num:
                messages.append(
                    {
                        "role": "user",
                        "content": "The Maximum number of interation has been reached, please generate your final output with required format and use <finish></finish> to exit.",
                    }
                )
                traj_msgs.append(
                    {
                        "role": "user",
                        "content": "The Maximum number of interation has been reached, please generate your final output with required format and use <finish></finish> to exit.",
                    }
                )

            try:
                # new conversation
                if tools and (
                    "hosted_vllm" in model_name or "qwen" in model_name.lower()
                ):
                    messages = convert_fncall_messages_to_non_fncall_messages(
                        messages, tools, add_in_context_learning_example=False
                    )
                    response = litellm.completion(
                        model=model_name,
                        temperature=temperature,
                        top_p=0.8,
                        repetition_penalty=1.05,
                        messages=messages,
                        num_retries=15,
                        stop=NON_FNCALL_STOP_WORDS,
                    )
                elif tools:
                    response = litellm.completion(
                        model=model_name,
                        tools=tools,
                        messages=messages,
                        num_retries=15,
                        temperature=temperature,
                        # stop=['</execute_ipython>'], #</finish>',
                    )
                else:
                    response = litellm.completion(
                        model=model_name,
                        messages=messages,
                        num_retries=15,
                        temperature=temperature,
                        stop=["</execute_ipython>"],  # </finish>',
                    )
            except litellm.BadRequestError as e:
                # If there's an error, send the error info back to the parent process
                return (
                    final_output,
                    messages,
                    {
                        "messages": traj_msgs,
                        "tools": tools,
                        "usage": {
                            "prompt_tokens": prompt_tokens,
                            "completion_tokens": completion_tokens,
                        },
                        "error": str(e),
                    },
                )

            if last_message and response.choices[0].message.content == last_message:
                messages.append(
                    {
                        "role": "user",
                        "content": "OBSERVATION:\n"
                        + "Don't repeat your response.\n"
                        + fake_user_msg,
                    }
                )
                traj_msgs.append(
                    {
                        "role": "user",
                        "content": "OBSERVATION:\n"
                        + "Don't repeat your response.\n"
                        + fake_user_msg,
                    }
                )
                continue

            raw_response = copy.deepcopy(response)
            # self.logger.info('response.choices[0].message')
            if tools and (
                "hosted_vllm" in model_name
                or "qwen" in model_name.lower()
                or "deepseek" in model_name
            ):
                try:
                    non_fncall_response_message = response.choices[0].message
                    fn_call_messages_with_response = (
                        convert_non_fncall_messages_to_fncall_messages(
                            [non_fncall_response_message], tools  # messages +
                        )
                    )
                    fn_call_response_message = fn_call_messages_with_response[-1]
                    if not isinstance(fn_call_response_message, LiteLLMMessage):
                        fn_call_response_message = LiteLLMMessage(
                            **fn_call_response_message
                        )
                    response.choices[0].message = fn_call_response_message
                except:
                    self.logger.info("convert none fncall messages failed.")
                    continue

            last_message = response.choices[0].message.content
            messages.append(convert_to_json(raw_response.choices[0].message))
            traj_msgs.append(convert_to_json(raw_response.choices[0].message))
            prompt_tokens += response.usage.prompt_tokens
            completion_tokens += response.usage.completion_tokens

            actions = parser.parse(response)
            if not isinstance(actions, List):
                actions = [actions]
            for action in actions:
                self.logger.debug(action.action_type)
                if action.action_type == ActionType.FINISH:
                    # final_output = action.thought
                    final_output = final_output + action.thought
                    self.logger.info("\nFinal Response:\n" + final_output)
                    finish = True  # break
                elif action.action_type == ActionType.MESSAGE:
                    self.logger.debug("thought:\n" + action.content)
                    final_output = final_output + action.content
                    # check if enough
                    messages.append({"role": "user", "content": fake_user_msg})
                    traj_msgs.append({"role": "user", "content": fake_user_msg})
                    # continue
                elif action.action_type == ActionType.RUN_IPYTHON:
                    ipython_code = action.code.strip("`")
                    self.logger.info(f"Executing code:\n```\n{ipython_code}\n```")
                    function_response = execute_ipython(ipython_code)
                    try:
                        function_response = eval(function_response)
                    except SyntaxError:
                        function_response = function_response
                    if not isinstance(function_response, str):
                        function_response = str(function_response)

                    self.logger.info("OBSERVATION:\n" + function_response[0:200])
                    if not tools:
                        messages.append(
                            {
                                "role": "user",
                                "content": "OBSERVATION:\n" + function_response,
                            }
                        )
                        traj_msgs.append(
                            {
                                "role": "user",
                                "content": "OBSERVATION:\n" + function_response,
                            }
                        )
                    else:
                        messages.append(
                            {
                                "role": "tool",
                                "tool_call_id": action.tool_call_id,
                                "name": action.function_name,
                                "content": "OBSERVATION:\n" + function_response,
                            }
                        )
                        traj_msgs.append(
                            {
                                "role": "tool",
                                "tool_call_id": action.tool_call_id,
                                "name": action.function_name,
                                "content": "OBSERVATION:\n" + function_response,
                            }
                        )
                else:
                    self.logger.warning("Error Action!")
                    # return

        # save traj
        traj_data = {
            "messages": traj_msgs,
            "tools": tools,
            "usage": {
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
            },
        }

        return (final_output, messages, traj_data)

    def run_agent_process(self, raw_data: dict) -> str:
        self.config_manager.refresh_config()
        self.data.update(raw_data)
        self.logger.info(f"Load Data:\n{self.data}")

        prompt_manager = PromptManager(
            prompt_dir=os.path.abspath("util/prompts"),
            agent_skills_docs=LocationToolsRequirement.documentation,
        )

        # loc result
        raw_output_loc = []
        loc_trajs = {"trajs": []}
        total_prompt_tokens, total_completion_tokens = 0, 0
        loc_start_time = time.time()

        if self.config.use_function_calling:
            system_prompt = function_calling.SYSTEM_PROMPT
        else:
            system_prompt = prompt_manager.system_message

        messages: list[dict] = [{"role": "system", "content": system_prompt}]

        if self.config.use_example:
            messages.append(
                {"role": "user", "content": prompt_manager.initial_user_message}
            )

        messages.append(
            {
                "role": "user",
                "content": get_task_instruction(
                    self.data, include_pr=True, include_hint=True
                ),
            }
        )

        tools = None
        if self.config.use_function_calling:
            tools = function_calling.get_tools(
                codeact_enable_search_keyword=True,
                codeact_enable_search_entity=True,
                codeact_enable_tree_structure_traverser=True,
                simple_desc=self.config.simple_description,
            )

        try:
            result = self.__agent_search_process(
                model_name=self.config.model_name,
                messages=messages,
                fake_user_msg=auto_search.FAKE_USER_MSG_FOR_LOC,
                temperature=1,
                tools=tools,
                use_function_calling=self.config.use_function_calling,
            )
        except litellm.BadRequestError as e:
            self.logger.warning(f"{e}. Try again.")
        except APITimeoutError:
            self.logger.warning(f"{e}. Try again.")
        except litellm.exceptions.ContextWindowExceededError as e:
            self.logger.warning(f"{e}. Try again.")
        except Exception as e:
            self.logger.warning(f"{e}, try again.")

        loc_result, messages, traj_data = result

        if "error" in traj_data:
            self.logger.warning(f"{self.config.model_name}\n{traj_data['error']}")

        loc_end_time = time.time()
        self.logger.info(f"Reques total time {loc_end_time - loc_start_time:.3f}s")
        total_prompt_tokens += traj_data["usage"]["prompt_tokens"]
        total_completion_tokens += traj_data["usage"]["completion_tokens"]
        traj_data["time"] = loc_end_time - loc_start_time
        loc_trajs["trajs"].append(traj_data)
        raw_output_loc.append(loc_result)

        if not raw_output_loc:
            # loc generalization failed
            loc_res = {
                "instance_id": self.data["instance_id"],
                "found_files": [[]],
                "found_modules": [[]],
                "found_entities": [[]],
                "raw_output_loc": raw_output_loc,
                "meta_data": {
                    "repo": self.data["repo"],
                    "base_commit": self.data["base_commit"],
                    "problem_statement": self.data["problem_statement"],
                    # 'gt_file_changes': gt_file_changes
                },
            }

            self.__append_to_json(
                loc_res,
                os.path.join(self.config.result_folder, self.config.output_file),
            )
        else:
            # process multiple loc outputs

            # all_valid_files = get_all_valid_files()
            all_found_files, all_found_modules, all_found_entities = (
                get_loc_results_from_raw_outputs(get_current_issue_id(), raw_output_loc)
            )

            loc_res = {
                "instance_id": self.data["instance_id"],
                "found_files": all_found_files,
                "found_modules": all_found_modules,
                "found_entities": all_found_entities,
                "raw_output_loc": raw_output_loc,
                "meta_data": {
                    "repo": self.data["repo"],
                    "base_commit": self.data["base_commit"],
                    "problem_statement": self.data["problem_statement"],
                    # 'gt_file_changes': gt_file_changes
                },
            }

            self.__append_to_json(
                loc_res,
                os.path.join(self.config.result_folder, self.config.output_file),
            )

            cost = calc_cost(
                self.config.model_name, total_prompt_tokens, total_completion_tokens
            )
            loc_res["usage"] = {
                "cost($)": f"{round(cost, 5)}",
                "prompt_tokens": total_prompt_tokens,
                "completion_tokens": total_completion_tokens,
            }
            loc_res["loc_trajs"] = traj_data

            self.__append_to_json(
                loc_res,
                os.path.join(self.config.result_folder, self.config.trajectory_file),
            )

        return loc_result


if __name__ == "__main__":

    if "LOCAGENT_REPO_DIR" not in os.environ:
        os.environ["LOCAGENT_REPO_DIR"] = os.path.abspath(
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
        )

    repo = "ivamp/zxoms/mmvsops"
    branch = "master"
    commit_id = "b1bc3a6372c624606af4eb214f7718ec2c0ca06f"
    problem_statement = "代码库理解/文件函数级\n我需要理解如何将ps查询数据转成chatbot后端支持的echart图形数据格式 特别是饼图和折线图的差异"

    raw_data = {
        "repo": repo,
        "instance_id": f"{repo}_001",
        "base_commit": commit_id,
        "problem_statement": problem_statement,
    }

    start_time = time.time()
    manager = LocAgentManager()
    manager.run_agent_process(raw_data)
    manager.logger.info(f"Total time: {(time.time() - start_time):.4f}s")
