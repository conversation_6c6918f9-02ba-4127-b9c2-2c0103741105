from mcp.server.fastmcp import FastMCP
import requests
import json
import os
import getpass
from git import Repo, InvalidGitRepositoryError, GitCommandError

def get_username_from_workspace(workspace_path):
    """
    从workspace路径获取用户名
    
    参数:
        workspace_path (str): 工作空间的绝对路径
        
    返回:
        str: 用户名 (Git配置中的用户名或系统用户名)
    """
    # 首先尝试获取Git用户名
    git_username = get_git_username(workspace_path)
    if git_username:
        return git_username
    
    # 如果无法获取Git用户名，则获取系统用户名
    system_username = get_system_username()
    if system_username and '@' in system_username:
        return system_username.split('@')[0]
    else:
        return system_username

def get_git_username(workspace_path):
    """
    尝试从Git配置中获取用户名
    
    参数:
        workspace_path (str): 工作空间的绝对路径
        
    返回:
        str: Git用户名，如果无法获取则返回None
    """
    try:
        # 尝试查找.git目录所在的根目录
        repo = Repo(workspace_path, search_parent_directories=True)
        
        # 获取Git配置中的用户名
        try:
            # 先尝试获取local配置
            username = repo.config_reader().get_value('user', 'name')
            if username:
                return username
        except:
            pass
        
        try:
            # 如果local配置没有，尝试获取global配置
            with repo.config_reader(config_level='global') as git_config:
                username = git_config.get_value('user', 'name')
                if username:
                    return username
        except:
            pass
        
    except (InvalidGitRepositoryError, GitCommandError):
        # 不是Git仓库或Git命令出错
        pass
    except Exception as e:
        # 其他异常
        print(f"Warning: Error while getting Git username: {str(e)}")
    
    return None

def get_system_username():
    """
    获取系统用户名
    
    返回:
        str: 当前系统用户名
    """
    try:
        # 跨平台获取用户名
        return getpass.getuser()
    except Exception as e:
        print(f"Warning: Error while getting system username: {str(e)}")
        # 如果getpass失败，尝试其他方法
        try:
            # Windows环境变量
            if os.name == 'nt':
                return os.environ.get('USERNAME', 'unknown')
            # Unix-like环境变量
            else:
                return os.environ.get('USER', os.environ.get('LOGNAME', 'unknown'))
        except:
            return 'unknown'


mcp = FastMCP(name="LocAgent")


url = "http://*************:5000/api/LocAgent"

headers = {"Content-Type": "application/json"}


@mcp.tool(
    name="retrieve_code",
    description="根据用户指令，获取完成指令所需的相关代码上下文，返回代码路径和代码行号。",
)
def retrieve_code(user_instruction: str, workspace: str) -> str:
    payload = {
        "user_name": get_username_from_workspace(workspace),
        "root_path": workspace,
        "problem_statement": user_instruction,
    }

    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    return json.dumps(json.loads(response.text.encode("utf8")), indent=2)


if __name__ == "__main__":
    mcp.run(transport="stdio")
