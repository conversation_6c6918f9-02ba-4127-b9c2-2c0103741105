import os
from typing import Op<PERSON>
from locagent_main import LocAgentManager
from mcp.server.fastmcp import FastMCP

from util.runtime.content_tools import SearchRepoTool, SearchEntityTool
from util.runtime.structure_tools import (
    ExploreTreeStructure,
    ExploreTreeStructure_simple,
)

from plugins.location_tools.repo_ops.repo_ops import (
    search_code_snippets,
    get_entity_contents,
    explore_graph_structure,
    explore_tree_structure,
)


mcp = FastMCP(
    name="LocAgent MCP",
    instructions="Call this tool to use the LocAgent tool when searching for code snippets in an existing library.",
    host=os.environ.get("LOCAGENT_MCP_HOST", "127.0.0.1"),
    port=int(os.environ.get("LOCAGENT_MCP_PORT", "8000")),
)

locagent_manager = LocAgentManager(mcp_service=True)


@mcp.tool(
    name="retriever_code",
    description="earches the codebase to retrieve relevant code snippets based on given queries(terms or line numbers). This function supports retrieving the complete content of a code entity, searching for code entities such as classes or functions by keywords, or locating specific lines within a file. It also supports filtering searches based on a file path or file pattern.",
    # annotations,
)
def retriever_code(problem_statement: str) -> str:
    data = {
        "problem_statement": problem_statement,
    }
    return locagent_manager.run_agent_process(data)


@mcp.tool(
    name=SearchRepoTool["function"]["name"],
    description=SearchRepoTool["function"]["description"],
)
def __search_code_snippets__(
    search_terms: Optional[list[str]] = None,
    line_nums: Optional[list] = None,
    file_path_or_pattern: Optional[str] = "**/*.py",
) -> str:
    return search_code_snippets(
        search_terms=search_terms,
        line_nums=line_nums,
        file_path_or_pattern=file_path_or_pattern,
    )


@mcp.tool(
    name=SearchEntityTool["function"]["name"],
    description=SearchEntityTool["function"]["description"],
)
def __get_entity_contents__(entity_names: list[str]) -> str:
    return get_entity_contents(entity_names=entity_names)


@mcp.tool(
    name=ExploreTreeStructure_simple["function"]["name"],
    description=ExploreTreeStructure_simple["function"]["description"],
)
def __explore_tree_structure__(
    start_entities: list[str],
    direction: str = "downstream",
    traversal_depth: int = 2,
    entity_type_filter: Optional[list[str]] = None,
    dependency_type_filter: Optional[list[str]] = None,
) -> str:
    return explore_tree_structure(
        start_entities=start_entities,
        direction=direction,
        traversal_depth=traversal_depth,
        entity_type_filter=entity_type_filter,
        dependency_type_filter=dependency_type_filter,
    )


if __name__ == "__main__":

    mcp.run(transport="sse")
