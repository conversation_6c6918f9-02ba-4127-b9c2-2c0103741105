import os
import sys
import uuid
import json

from loguru import logger
from loguru._logger import Logger

from typing import Optional
from datetime import datetime
from dataclasses import dataclass


@dataclass
class LoaAgentConfig:
    model_name: str = "openai/Qwen3-235B-A22B"
    repo_dir: str = "/temp/locagent/repo"
    output_folder: str = "mcp_service/results"
    result_folder: str = ""
    output_file: str = "loc_outputs.json"
    trajectory_file: str = "loc_trajectory.json"
    log_file: str = "locagent.log"
    log_level: str = "INFO"
    use_example: bool = False
    simple_description: bool = True
    use_function_calling: bool = True


class LoggerManager:
    def __init__(self) -> None:
        self.logger: Logger = logger.bind(module="locagent")
        logger.remove()
        self.logger_file: Optional[int] = None
        self.logger_term: Optional[int] = self.logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> [<level>{level:<8}</level>] <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
            colorize=True,
        )

    def refresh_logger_term(
        self, level: str = "INFO", mcp_service: bool = False
    ) -> Logger:
        self.logger.remove(self.logger_term)
        if mcp_service:
            log_level = level
        else:
            log_level = "INFO"

        self.logger_term = self.logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> [<level>{level:<8}</level>] <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=log_level,
            colorize=True,
        )
        return self.logger

    def refresh_logger_file(self, config: LoaAgentConfig) -> Logger:
        if self.logger_file:
            self.logger.remove(self.logger_file)
        self.logger_file = self.logger.add(
            os.path.join(config.result_folder, config.log_file),
            format="{time:YYYY-MM-DD HH:mm:ss} [{level:<8}] {name}:{function}:{line} - {message}",
            level="INFO",
            rotation="10 MB",
            retention="7 days",
            encoding="utf-8",
        )
        return self.logger


class ConfigManager:
    def __init__(self, mcp_service: bool = False):
        self.config = LoaAgentConfig()
        self.config_path: str = self.__get_config_path()
        self.logger_manager = LoggerManager()
        self.logger = self.logger_manager.logger
        self.__load_config()
        self.logger_manager.refresh_logger_term(self.config.log_level, mcp_service)

    def __get_config_path(self) -> str:
        if "LOCAGENT_ROOT_DIR" in os.environ:
            return os.path.abspath(
                os.path.join(
                    os.environ.get("LOCAGENT_ROOT_DIR"),
                    "mcp_service/locagent_config.json",
                )
            )
        else:
            return os.path.abspath(
                os.path.join(
                    os.path.dirname(os.path.abspath(__file__)),
                    "locagent_config.json",
                )
            )

    def __load_config(self):
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                raw_config = json.load(f)

        except FileNotFoundError as e:
            self.logger.warning(
                f"The configuration file {self.config_path} was not found."
            )
            return

        except json.JSONDecodeError as e:
            self.logger.warning(f"{self.config_path} is not a valid JSON format")
            self.logger.warning(f"{e}")
            return

        self.config.model_name = os.environ.get("LOCAGENT_MODEL_NAME", "Unknown model")
        self.config.repo_dir = os.environ.get("LOCAGENT_REPO_DIR", "")
        self.__check_repo_dir()

        self.config.output_folder = raw_config.get(
            "output_folder", "mcp_service/results"
        ).strip()
        self.config.output_folder = os.path.abspath(self.config.output_folder)

        self.config.output_file = raw_config.get(
            "output_file", "loc_outputs.json"
        ).strip()

        self.config.trajectory_file = raw_config.get(
            "trajectory_file", "loc_trajectory.json"
        ).strip()

        self.config.log_file = raw_config.get("log_file", "locagent.log").strip()

        log_level = raw_config.get("log_level", "INFO").strip().upper()
        if log_level in [
            "TRACE",
            "DEBUG",
            "INFO",
            "SUCCESS",
            "WARNING",
            "ERROR",
            "CRITICAL",
        ]:
            self.config.log_level = log_level

        self.config.use_example = raw_config.get("use_example", False)
        self.config.simple_description = raw_config.get("simple_description", True)
        self.config.use_function_calling = raw_config.get("use_function_calling", True)

    def __check_repo_dir(self) -> None:
        if not os.path.exists(self.config.repo_dir):
            raise FileNotFoundError(f"{self.config.repo_dir} Directory does not exist!")
        with os.scandir(self.config.repo_dir) as entries:
            if not any(entries):
                raise FileNotFoundError(f"{self.config.repo_dir} Directory is empty!")

    def __create_result_dir(self) -> None:
        self.config.result_folder = os.path.join(
            self.config.output_folder,
            datetime.now().strftime("%Y%m%d%H%M%S") + "_" + str(uuid.uuid4().hex[:8]),
        )
        os.makedirs(self.config.result_folder, exist_ok=True)

    def refresh_config(self) -> LoaAgentConfig:
        self.__create_result_dir()
        self.logger_manager.refresh_logger_file(self.config)
        return self.config


if __name__ == "__main__":

    if "LOCAGENT_REPO_DIR" not in os.environ:
        os.environ["LOCAGENT_REPO_DIR"] = os.path.abspath(
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
        )

    manager = ConfigManager()
    print(f"Load Config:\n{manager.config}")
