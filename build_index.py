import argparse
import os
import pickle
import time
import logging
from pathvalidate import sanitize_filename

from llama_index.core.schema import QueryBundle

from plugins.location_tools.retriever.bm25_retriever import (
    build_code_retriever_from_repo as build_bm25_code_retriever,
    build_module_retriever_from_graph as build_bm25_module_retriever,
    build_retriever_from_persist_dir as load_bm25_retriever,
)
from plugins.location_tools.retriever.vector_retriever import (
    build_code_retriever_from_repo as build_vector_code_retriever,
    build_code_retriever_from_graph as build_vector_graph_retriever,
    build_retriever_from_persist_dir as load_vector_retriever,
)
from plugins.location_tools.retriever.fuzzy_retriever import (
    fuzzy_retrieve_from_graph_nodes as fuzzy_retrieve
)
from dependency_graph.build_graph_java import build_graph
from dependency_graph import RepoEntitySearcher

def search_by_graph(graph_searcher, keyword):
    print("=========================")
    print("search by graph...")
    if graph_searcher.has_node(keyword):
        print("search nids:\n")
        print(keyword)

def search_by_bm25_module(graph_searcher, keyword):
    print("=========================")
    print("search by bm25 module...")
    retriever = build_bm25_module_retriever(entity_searcher=graph_searcher)
    try:
        retrieved_nodes = retriever.retrieve(keyword)
    except IndexError as e:
        logging.warning(f'{e}. Probably because the query `{keyword}` is too short.')
        return []

    all_nodes = []
    nids = []
    for node in retrieved_nodes:
        # if node.score <= 0:
        #     continue
        nid = node.text.split(':')[0]
        if nid not in nids:
            all_nodes.append(node)
            nids.append(nid)
    print("search nids:\n")
    for node in all_nodes:
        print("score: ", node.score)
        print(node.text.split(':')[0])

def search_by_fuzzy(G, keyword):
    print("=========================")
    print("search by fuzzy...")
    nids = fuzzy_retrieve(keyword, graph=G, similarity_top_k=3)
    print("search nids:\n")
    for nid in nids: print(nid)

def search_by_bm25_content(retriever, keyword):
    print("=========================")
    print("search by bm25 content...")
    try:
        retrieved_nodes = retriever.retrieve(keyword)
    except IndexError as e:
        logging.warning(f'{e}. Probably because the query `{keyword}` is too short.')
        return []

    all_nodes = []
    nids = []
    for node in retrieved_nodes:
        # if node.score <= 0:
        #     continue
        nid = node.metadata['file_path'].split(':')[0]
        if nid not in nids:
            all_nodes.append(node)
            nids.append(nid)
    print("search nids:\n")
    for node in all_nodes:
        print("score: ", node.score)
        print(node.metadata['file_path'].split(':')[0])

def search_by_vector_epic(retriever, keyword):
    print("=========================")
    print("search by vector epic...")
    query_bundle = QueryBundle(query_str=keyword)
    try:
        retrieved_nodes = retriever._retrieve(query_bundle)
    except IndexError as e:
        logging.warning(f'{e}. Probably because the query `{keyword}` is too short.')
        return []

    all_nodes = []
    nids = []
    for node in retrieved_nodes:
        # if node.score <= 0:
        #     continue
        nid = node.metadata['file_path'].split(':')[0]
        if nid not in nids:
            all_nodes.append(node)
            nids.append(nid)
    print("search nids:\n")
    for node in all_nodes:
        print("score: ", node.score)
        print(node.metadata['file_path'].split(':')[0])

def search_by_vector_graph(retriever, keyword):
    print("=========================")
    print("search by vector graph...")
    query_bundle = QueryBundle(query_str=keyword)
    try:
        retrieved_nodes = retriever._retrieve(query_bundle)
    except IndexError as e:
        logging.warning(f'{e}. Probably because the query `{keyword}` is too short.')
        return []

    all_nodes = []
    nids = []
    for node in retrieved_nodes:
        # if node.score <= 0:
        #     continue
        nid = node.metadata.get("nid", "").split(':')[0]
        if nid not in nids:
            all_nodes.append(node)
            nids.append(nid)
    print("search nids:\n")
    for node in all_nodes:
        print("score: ", node.score)
        print(node.metadata.get("nid", "").split(':')[0])

def build_index(repo_path):
    graph_index_dir = f'{os.path.expanduser("~")}/.zeroAgent/index/graph_index_v2.3'
    bm25_index_dir = f'{os.path.expanduser("~")}/.zeroAgent/index/BM25_index'
    vector_index_dir = f'{os.path.expanduser("~")}/.zeroAgent/index/VECTOR_index'

    absolute_repo_path = os.path.abspath(repo_path)
    index_file_name = sanitize_filename(absolute_repo_path, replacement_text="_")
    # index_dir = f'{os.path.expanduser("~")}/.zeroAgent/LocAgent/index_data'

    start_time = time.time()

    print(f'Start process {repo_path}')
    try:
        print("Building BM25 index...")
        bm25_persist_path = f"{bm25_index_dir}/{index_file_name}"
        if os.path.exists(f'{bm25_persist_path}/corpus.jsonl'):
            bm25_retriever = load_bm25_retriever(bm25_persist_path)
        else:
            bm25_retriever = build_bm25_code_retriever(absolute_repo_path, persist_path=bm25_persist_path,
                                        similarity_top_k=10)
            end_time = time.time()
            print("build bm25 content time: ", end_time - start_time)
            start_time = end_time

        print("Building graph index...")
        output_file = f"{graph_index_dir}/{index_file_name}.pkl"
        if os.path.exists(output_file):
            with open(output_file, 'rb') as f:
                G = pickle.load(f)
            print(f"graph index exists: {output_file}, skip!")
        else:
            G = build_graph(absolute_repo_path, global_import=True)
            end_time = time.time()
            print("build graph time: ", end_time - start_time)
            start_time = end_time
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'wb') as f:
                pickle.dump(G, f)

        print("Building vector index from repo...")
        vector_persist_path = f"{vector_index_dir}/{index_file_name}"
        if os.path.exists(f'{vector_persist_path}/nodes.pkl'):
            epic_vector_retriever = load_vector_retriever(vector_persist_path)
        else:
            epic_vector_retriever = build_vector_code_retriever(absolute_repo_path, persist_path=vector_persist_path,
                                        similarity_top_k=10)
            end_time = time.time()
            print("build vector content time: ", end_time - start_time)
            start_time = end_time

        # print("Building vector index from graph...")
        # entity_searcher = RepoEntitySearcher(G)
        # graph_vector_retriever = build_vector_graph_retriever(entity_searcher=entity_searcher, db_path=vector_persist_path)

        print(f'Processed {args.repo_path}\nBM25 index: {bm25_persist_path}\nvector index: {vector_persist_path}')
    except Exception as e:
        print(f'Error processing {args.repo_path}: {e}')

    end_time = time.time()
    print(f'Total Execution time = {end_time - start_time:.3f}s')

    #keyword = "daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatcherUpdateController.java"
    keyword = "PatchInfo"
    print(f'Start search {keyword}')
    # search_by_graph(RepoEntitySearcher(G), keyword)
    # search_by_bm25_module(RepoEntitySearcher(G), keyword)
    # search_by_fuzzy(G, keyword)
    search_by_bm25_content(bm25_retriever, keyword)
    search_by_vector_epic(epic_vector_retriever, keyword)
    # search_by_vector_graph(graph_vector_retriever, keyword)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--repo_path', type=str, default='/home/<USER>/workspace/thread_1/code/real_case/PATCHER',
                        help='The directory where you have already pulled the codebase.')
    args = parser.parse_args()
    build_index(args.repo_path)