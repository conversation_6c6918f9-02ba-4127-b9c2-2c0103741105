#!/usr/bin/env python3
"""
测试脚本：验证torch.multiprocessing替换为原生multiprocessing是否成功
"""

import sys
import importlib.util

def test_import_replacement():
    """测试导入替换是否成功"""
    files_to_test = [
        'auto_search_main.py',
        'dependency_graph/batch_build_graph.py', 
        'build_bm25_index.py',
        'util/benchmark/gen_oracle_locations.py'
    ]
    
    print("测试torch.multiprocessing替换为原生multiprocessing...")
    
    for file_path in files_to_test:
        print(f"\n检查文件: {file_path}")
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有torch.multiprocessing的导入
            if 'import torch.multiprocessing' in content:
                print(f"❌ 错误: {file_path} 仍然包含 torch.multiprocessing 导入")
                return False
            
            # 检查是否有原生multiprocessing的导入
            if 'import multiprocessing as mp' in content:
                print(f"✅ 正确: {file_path} 已使用原生 multiprocessing")
            else:
                print(f"❌ 错误: {file_path} 没有找到原生 multiprocessing 导入")
                return False
                
            # 检查是否还有mp.spawn的使用
            if 'mp.spawn(' in content:
                print(f"❌ 错误: {file_path} 仍然使用 mp.spawn()")
                return False
            else:
                print(f"✅ 正确: {file_path} 已移除 mp.spawn() 调用")
                
        except FileNotFoundError:
            print(f"❌ 错误: 文件 {file_path} 不存在")
            return False
        except Exception as e:
            print(f"❌ 错误: 检查文件 {file_path} 时出现异常: {e}")
            return False
    
    print("\n🎉 所有文件的torch.multiprocessing替换检查通过!")
    return True

def test_syntax():
    """测试语法是否正确"""
    files_to_test = [
        'auto_search_main.py',
        'dependency_graph/batch_build_graph.py', 
        'build_bm25_index.py',
        'util/benchmark/gen_oracle_locations.py'
    ]
    
    print("\n测试Python语法...")
    
    for file_path in files_to_test:
        print(f"检查语法: {file_path}")
        
        try:
            # 尝试编译文件以检查语法
            with open(file_path, 'r', encoding='utf-8') as f:
                source = f.read()
            
            compile(source, file_path, 'exec')
            print(f"✅ 语法正确: {file_path}")
            
        except SyntaxError as e:
            print(f"❌ 语法错误: {file_path} - {e}")
            return False
        except Exception as e:
            print(f"❌ 其他错误: {file_path} - {e}")
            return False
    
    print("\n🎉 所有文件语法检查通过!")
    return True

if __name__ == "__main__":
    print("开始验证torch.multiprocessing替换...")
    
    # 测试导入替换
    import_test_passed = test_import_replacement()
    
    # 测试语法
    syntax_test_passed = test_syntax()
    
    if import_test_passed and syntax_test_passed:
        print("\n🎉 所有测试通过! torch.multiprocessing已成功替换为原生multiprocessing")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查上述错误")
        sys.exit(1)
