/*
 * Copyright (c) 2024 Mockito contributors
 * This program is made available under the terms of the MIT License.
 */
package org.mockito.mock;

/**
 * The type of mock being created
 */
public enum MockType {

    /**
     * <PERSON><PERSON> created as an instance of the mocked type
     */
    INSTANCE,

    /**
     * <PERSON><PERSON> replaces the mocked type through static mocking
     */
    STATIC,
}
