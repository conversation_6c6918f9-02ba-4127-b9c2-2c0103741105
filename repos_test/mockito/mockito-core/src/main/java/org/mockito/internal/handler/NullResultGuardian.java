/*
 * Copyright (c) 2007 Mockito contributors
 * This program is made available under the terms of the MIT License.
 */
package org.mockito.internal.handler;

import static org.mockito.internal.util.Primitives.defaultValue;

import org.mockito.invocation.Invocation;
import org.mockito.invocation.InvocationContainer;
import org.mockito.invocation.MockHandler;
import org.mockito.mock.MockCreationSettings;

/**
 * Protects the results from delegate <PERSON><PERSON><PERSON><PERSON><PERSON>. Makes sure the results are valid.
 *
 * by <PERSON><PERSON><PERSON><PERSON><PERSON>, created at: 5/22/12
 */
class NullResultGuardian<T> implements <PERSON>ckHandler<T> {

    private final MockHandler<T> delegate;

    public NullResultGuardian(<PERSON>ck<PERSON><PERSON><PERSON><T> delegate) {
        this.delegate = delegate;
    }

    @Override
    public Object handle(Invocation invocation) throws Throwable {
        Object result = delegate.handle(invocation);
        Class<?> returnType = invocation.getMethod().getReturnType();
        if (result == null && returnType.isPrimitive()) {
            // primitive values cannot be null
            return defaultValue(returnType);
        }
        return result;
    }

    @Override
    public MockCreationSettings<T> getMockSettings() {
        return delegate.getMockSettings();
    }

    @Override
    public InvocationContainer getInvocationContainer() {
        return delegate.getInvocationContainer();
    }
}
