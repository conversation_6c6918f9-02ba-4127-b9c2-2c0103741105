/*
 * Copyright (c) 2016 Mockito contributors
 * This program is made available under the terms of the MIT License.
 */
package org.mockito.plugins;

import org.mockito.exceptions.stacktrace.StackTraceCleaner;

/**
 * An extension point to register custom {@link StackTraceCleaner}.
 * You can replace <PERSON><PERSON><PERSON>'s default StackTraceCleaner.
 * You can also 'enhance' <PERSON><PERSON><PERSON>'s default behavior
 * because the default cleaner is passed as parameter to the method.
 * <p>
 * Registering custom StackTraceCleaner is done in similar manner as the {@link MockMaker} implementation.
 * <p>
 * See the default implementation: {@link org.mockito.internal.exceptions.stacktrace.DefaultStackTraceCleanerProvider}
 */
public interface StackTraceCleanerProvider {

    /**
     * Allows configuring custom StackTraceCleaner.
     *
     * @param defaultCleaner - <PERSON><PERSON><PERSON>'s default StackTraceCleaner
     * @return StackTraceCleaner to use
     */
    StackTraceCleaner getStackTraceCleaner(StackTraceCleaner defaultCleaner);
}
