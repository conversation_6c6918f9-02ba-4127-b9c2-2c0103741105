/*
 * Copyright (c) 2007 Mockito contributors
 * This program is made available under the terms of the MIT License.
 */
package org.mockito.internal.matchers;

import java.io.Serializable;

import org.mockito.ArgumentMatcher;

public class Contains implements ArgumentMatcher<String>, Serializable {

    private final String substring;

    public Contains(String substring) {
        this.substring = substring;
    }

    @Override
    public boolean matches(String actual) {
        return actual != null && actual.contains(substring);
    }

    @Override
    public String toString() {
        return "contains(\"" + substring + "\")";
    }
}
