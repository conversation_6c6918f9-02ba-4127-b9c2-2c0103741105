/*
 * Copyright (c) 2007 Mockito contributors
 * This program is made available under the terms of the MIT License.
 */
package org.mockito.internal.matchers.apachecommons;

import java.io.Serializable;

import org.mockito.ArgumentMatcher;

public class ReflectionEquals implements ArgumentMatcher<Object>, Serializable {

    private final Object wanted;
    private final String[] excludeFields;

    public ReflectionEquals(Object wanted, String... excludeFields) {
        this.wanted = wanted;
        this.excludeFields = excludeFields;
    }

    @Override
    public boolean matches(Object actual) {
        return EqualsBuilder.reflectionEquals(wanted, actual, excludeFields);
    }

    @Override
    public String toString() {
        return "refEq(" + wanted + ")";
    }
}
