#!/bin/bash

# 参数解析函数
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    --dataset DATASET           Dataset path (required)
    --locagent_loc_file FILE    Path to localization output file (required)
    --metrics METRICS           Metrics to calculate (default: recall precision)
                               Available: acc, ndcg, precision, recall, map
                               Multiple metrics: --metrics "recall precision acc"

    Flags:
    --verbose                   Print detailed results for each test case
    --help, -h                  Show this help message

Examples:
    $0 --dataset "czlll/SWE-bench_Lite" --locagent_loc_file "path/to/loc_outputs.jsonl"
    $0 --dataset "czlll/SWE-bench_Lite" --locagent_loc_file "path/to/loc_outputs.jsonl" --metrics "recall precision acc"
    $0 --dataset "czlll/SWE-bench_Lite" --locagent_loc_file "path/to/loc_outputs.jsonl" --verbose
    $0 --dataset "czlll/SWE-bench_Lite" --locagent_loc_file "path/to/loc_outputs.jsonl" --metrics "acc ndcg precision recall map" --verbose
EOF
}

# 默认参数值
dataset=""
locagent_loc_file=""
metrics="recall precision"
verbose_flag=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dataset)
            dataset="$2"
            shift 2
            ;;
        --locagent_loc_file)
            locagent_loc_file="$2"
            shift 2
            ;;
        --metrics)
            metrics="$2"
            shift 2
            ;;
        --verbose)
            verbose_flag="--verbose"
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

# 检查必需参数
if [[ -z "$dataset" ]]; then
    echo "Error: --dataset is required"
    echo "Use --help for usage information."
    exit 1
fi

if [[ -z "$locagent_loc_file" ]]; then
    echo "Error: --locagent_loc_file is required"
    echo "Use --help for usage information."
    exit 1
fi

# 获取脚本所在目录
code_dir="$(cd "$(dirname "$0")" && pwd)/.."

export PYTHONPATH=$PYTHONPATH:${code_dir}

# 构建 Python 命令参数
python_args=(
    "evaluation/eval_metric.py"
    "--dataset" "$dataset"
    "--locagent_loc_file" "$locagent_loc_file"
    "--metrics" $metrics
)

# 添加标志参数（只有非空时才添加）
[[ -n "$verbose_flag" ]] && python_args+=("$verbose_flag")

# 执行 Python 脚本
echo "Executing: python ${python_args[*]}"
python "${python_args[@]}"