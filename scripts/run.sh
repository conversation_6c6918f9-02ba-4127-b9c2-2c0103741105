#!/bin/bash

# 参数解析函数
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    --dataset DATASET           Dataset path (default: czlll/SWE-bench_Lite)
    --split SPLIT               Dataset split (default: test)
    --model MODEL               Model name (default: openai/DeepSeek-V3)
    --eval_n_limit N            Evaluation limit (default: 1)
    --num_processes N           Number of processes (default: 1)
    --num_samples N             Number of samples (default: 1)
    --clone_dir DIR             Clone directory (default: repos)
    --timeout SECONDS           Timeout in seconds (default: 900)
    --max_attempt_num N         Max attempt number (default: 1)
    --ranking_method METHOD     Ranking method: mrr|majority (default: mrr)
    --used_list LIST            Used list name (default: selected_ids)
    --log_level LEVEL           Log level (default: INFO)
    --output_folder FOLDER      Output folder (if not specified, auto-generated with timestamp)
    --output_file FILE          Output file name (default: loc_outputs.jsonl)
    --merge_file FILE           Merge file name (default: merged_loc_outputs.jsonl)

    Flags:
    --localize                  Enable localization
    --merge                     Enable merging
    --use_example               Use examples
    --use_function_calling      Enable function calling
    --simple_desc               Use simple descriptions
    --rerun_empty_location      Rerun empty locations
    --qwen3_disable_thinking    Disable Qwen3 thinking
    --use_dspy_react            Use DSPy ReAct
    --use_vscode_fuzz           Use VSCode fuzz
    --use_vector_retrieve       Use vector retrieval
    --disable_module_search     Disable module search by bm25 and fuzzy
    --skip_git_check            Skip Git repository status check

    --help, -h                  Show this help message

Examples:
    $0 --localize --merge --model "openai/Qwen3-235B-A22B" --eval_n_limit 5
    $0 --dataset "czlll/SWE-bench_Lite" --model "openai/Qwen3-235B-A22B" --eval_n_limit 5 --num_processes 1 --use_function_calling
EOF
}

# 默认参数值
dataset="datasets/czlll_SWE-bench_Lite_test.jsonl"
split="test"
model_name="openai/DeepSeek-V3"
eval_n_limit=1
num_processes=1
num_samples=1
clone_dir="repos"
timeout=900
max_attempt_num=1
ranking_method="mrr"
used_list="selected_ids"
log_level="INFO"
output_folder=""
output_file="loc_outputs.jsonl"
merge_file="merged_loc_outputs.jsonl"

# 标志参数（保持与原脚本相同的默认值）
localize_flag="--localize"
merge_flag="--merge"
use_example_flag=""
use_function_calling_flag="--use_function_calling"
simple_desc_flag="--simple_desc"
rerun_empty_location_flag=""
qwen3_disable_thinking_flag=""
use_dspy_react_flag=""
use_vscode_fuzz_flag=""
use_vector_retrieve=""
disable_module_search=""
skip_git_check=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dataset)
            dataset="$2"
            shift 2
            ;;
        --split)
            split="$2"
            shift 2
            ;;
        --model)
            model_name="$2"
            shift 2
            ;;
        --eval_n_limit)
            eval_n_limit="$2"
            shift 2
            ;;
        --num_processes)
            num_processes="$2"
            shift 2
            ;;
        --num_samples)
            num_samples="$2"
            shift 2
            ;;
        --clone_dir)
            clone_dir="$2"
            shift 2
            ;;
        --timeout)
            timeout="$2"
            shift 2
            ;;
        --max_attempt_num)
            max_attempt_num="$2"
            shift 2
            ;;
        --ranking_method)
            ranking_method="$2"
            shift 2
            ;;
        --used_list)
            used_list="$2"
            shift 2
            ;;
        --log_level)
            log_level="$2"
            shift 2
            ;;
        --output_folder)
            output_folder="$2"
            shift 2
            ;;
        --output_file)
            output_file="$2"
            shift 2
            ;;
        --merge_file)
            merge_file="$2"
            shift 2
            ;;
        --localize)
            localize_flag="--localize"
            shift
            ;;
        --merge)
            merge_flag="--merge"
            shift
            ;;
        --use_example)
            use_example_flag="--use_example"
            shift
            ;;
        --use_function_calling)
            use_function_calling_flag="--use_function_calling"
            shift
            ;;
        --simple_desc)
            simple_desc_flag="--simple_desc"
            shift
            ;;
        --rerun_empty_location)
            rerun_empty_location_flag="--rerun_empty_location"
            shift
            ;;
        --qwen3_disable_thinking)
            qwen3_disable_thinking_flag="--qwen3_disable_thinking"
            shift
            ;;
        --use_dspy_react)
            use_dspy_react_flag="--use_dspy_react"
            shift
            ;;
        --use_vscode_fuzz)
            use_vscode_fuzz_flag="--use_vscode_fuzz"
            shift
            ;;
        --use_vector_retrieve)
            use_vector_retrieve_flag="--use_vector_retrieve"
            shift
            ;;
        --disable_module_search)
            disable_module_search_flag="--disable_module_search"
            shift
            ;;
        --skip_git_check)
            skip_git_check=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

# set api key
# or set api key in `scripts/env/set_env.sh`
# . scripts/env/set_env.sh


export PYTHONPATH=$PYTHONPATH:$(pwd)
export GRAPH_INDEX_DIR='index/graph_index_v2.3'
export BM25_INDEX_DIR='index/BM25_index'
export VECTOR_INDEX_DIR='index/VECTOR_INDEX_DIR'

if [[ $model_name == "openai/DeepSeek-V3" ]]; then
    export OPENAI_API_KEY="7d6c438d2b0441bd96c79d885fb06503"
    export OPENAI_API_BASE="https://maas-apigateway.dt.zte.com.cn/STREAM/deepseek-v3-api/v1"
elif [[ $model_name == "openai/Qwen3-235B-A22B" ]]; then
    export OPENAI_API_KEY="8612e7bd0533452f9e88382cf3112ba4"
    export OPENAI_API_BASE="https://maas-apigateway.dt.zte.com.cn/STREAM/qwen3-235b-a22b/v1"
fi

no_proxy=maas-apigateway.dt.zte.com.cn,***********

# 获取脚本所在目录
code_dir="$(cd "$(dirname "$0")" && pwd)/.."

# Git 状态检查函数
check_git_status() {
    echo "=== Git Repository Status Check ==="

    # 检查是否在 Git 仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo "Warning: Not in a Git repository. Skipping Git status check."
        return 0
    fi

    # 打印当前 commit 信息
    echo "Current commit:"
    git log -1
    echo "Current branch: $(git branch --show-current)"
    echo ""

    # 检查工作区状态
    if ! git diff-index --quiet HEAD --; then
        echo "⚠️  WARNING: Local workspace has uncommitted changes!"
        echo ""
        echo "Uncommitted changes:"
        echo "==================="

        # 显示未暂存的更改
        if ! git diff --quiet; then
            echo "Modified files (not staged):"
            # git diff --name-only
            # echo ""
            # echo "Detailed changes:"
            git diff --stat
            echo ""
        fi

        # 显示已暂存但未提交的更改
        if ! git diff --cached --quiet; then
            echo "Staged files (ready to commit):"
            git diff --cached --name-only
            echo ""
            echo "Detailed staged changes:"
            git diff --cached --stat
            echo ""
        fi

        # 显示未跟踪的文件
        untracked_files=$(git ls-files --others --exclude-standard)
        if [[ -n "$untracked_files" ]]; then
            echo "Untracked files:"
            # 将未跟踪文件转换为数组
            readarray -t untracked_array <<< "$untracked_files"
            total_untracked=${#untracked_array[@]}

            if [[ $total_untracked -le 3 ]]; then
                # 如果文件数量不超过3个，显示所有文件
                echo "$untracked_files"
            else
                # 如果文件数量超过3个，只显示前3个并省略其余
                for i in {0..2}; do
                    echo "${untracked_array[$i]}"
                done
                echo "... and $((total_untracked - 3)) more untracked files"
            fi
            echo ""
        fi

        # 询问用户是否继续
        echo "The workspace is not clean. This may affect reproducibility of results."
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo ""

        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Execution cancelled. Please commit or stash your changes first."
            echo ""
            echo "Suggested commands:"
            echo "  git add -A && git commit -m 'Your commit message'"
            echo "  or"
            echo "  git stash push -m 'Temporary stash before running script'"
            exit 1
        fi

        echo "Continuing with uncommitted changes..."
        echo ""
    else
        echo "✅ Workspace is clean (no uncommitted changes)"
        echo ""
    fi

    echo "=================================="
    echo ""
}

# 执行 Git 状态检查（除非用户选择跳过）
if [[ "$skip_git_check" != true ]]; then
    check_git_status
else
    echo "Skipping Git status check as requested."
    echo ""
fi

download_repo() {
    mkdir -p "$clone_dir"
    local repo_name="$1"
    echo "Downloading ${repo_name}.tar.gz to $clone_dir ..."
    wget -qO- "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/benchmarks/repos/${repo_name}.tar.gz" | tar -xz -C "$clone_dir/"
}

# 根据dataset确定要下载的压缩包
case "$dataset" in
    *mockito__mockito*)
        download_repo "mockito"
        ;;
    *fasterxml__jackson-databind*)
        download_repo "jackson-databind"
        ;;
    *fasterxml__jackson-core*)
        ;;
    *alibaba__fastjson2*)
        ;;
    *apache__dubbo*)
        ;;
    *elastic__logstash*)
        ;;
    *google__gson*)
        ;;
    *googlecontainertools__jib*)
        ;;
    *)
        echo "Warning: Unknown --dataset '$dataset'. Aborting."
        exit -1
        ;;
esac

# 获取当前commit id
current_commit_id=""
if git rev-parse --git-dir > /dev/null 2>&1; then
    current_commit_id=$(git rev-parse HEAD)
else
    current_commit_id="unknown"
fi

# 如果没有指定输出文件夹，则自动生成带时间戳的文件夹
if [[ -z "$output_folder" ]]; then
    result_path="${code_dir}/results/"$(date +%Y%m%d%H%M%S)
else
    result_path="$output_folder"
fi

echo "Output path: ${result_path}"
mkdir -p ${result_path}

# 构建 Python 命令参数
python_args=(
    "auto_search_main.py"
    "--dataset" "$dataset"
    "--split" "$split"
    "--model" "$model_name"
    "--output_folder" "$result_path"
    "--eval_n_limit" "$eval_n_limit"
    "--num_processes" "$num_processes"
    "--num_samples" "$num_samples"
    "--clone_dir" "$clone_dir"
    "--timeout" "$timeout"
    "--max_attempt_num" "$max_attempt_num"
    "--ranking_method" "$ranking_method"
    "--used_list" "$used_list"
    "--log_level" "$log_level"
    "--output_file" "$output_file"
    "--merge_file" "$merge_file"
    "--commit_id" "$current_commit_id"
)

# 添加标志参数（只有非空时才添加）
[[ -n "$localize_flag" ]] && python_args+=("$localize_flag")
[[ -n "$merge_flag" ]] && python_args+=("$merge_flag")
[[ -n "$use_example_flag" ]] && python_args+=("$use_example_flag")
[[ -n "$use_function_calling_flag" ]] && python_args+=("$use_function_calling_flag")
[[ -n "$simple_desc_flag" ]] && python_args+=("$simple_desc_flag")
[[ -n "$rerun_empty_location_flag" ]] && python_args+=("$rerun_empty_location_flag")
[[ -n "$qwen3_disable_thinking_flag" ]] && python_args+=("$qwen3_disable_thinking_flag")
[[ -n "$use_dspy_react_flag" ]] && python_args+=("$use_dspy_react_flag")
[[ -n "$use_vscode_fuzz_flag" ]] && python_args+=("$use_vscode_fuzz_flag")
[[ -n "$use_vector_retrieve_flag" ]] && python_args+=("$use_vector_retrieve_flag")
[[ -n "$disable_module_search_flag" ]] && python_args+=("$disable_module_search_flag")

# 执行 Python 脚本
echo "Executing: python ${python_args[*]}"
python "${python_args[@]}"

echo "Results dir: ${result_path}/"
echo "Results saved to: ${result_path}/${output_file}"
