import argparse
import os
import pickle
import time
from pathvalidate import sanitize_filename
from plugins.location_tools.retriever.bm25_retriever import (
    build_code_retriever_from_repo as build_code_retriever
)
from dependency_graph.build_graph_java import build_graph


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--repo_path', type=str, default='playground/build_graph', 
                        help='The directory where you have already pulled the codebase.')
    parser.add_argument('--index_dir', type=str, default=f'{os.path.expanduser("~")}/.zeroAgent/index', 
                        help='The base directory where the generated graph index will be saved.')
    args = parser.parse_args()

    
    absolute_repo_path = os.path.abspath(args.repo_path)
    index_file_name = sanitize_filename(absolute_repo_path, replacement_text="_")
    index_dir = args.index_dir

    start_time = time.time()

    print(f'Start process {args.repo_path}')
    try:
        print("Building BM25 index...")
        persist_path = f"{index_dir}/BM25_index/{index_file_name}"
        # retriever = build_code_retriever(absolute_repo_path, persist_path=persist_path,
        #                                 similarity_top_k=10)
        print("Building graph index...")
        output_file = f"{index_dir}/graph_index_v2.3/{index_file_name}.pkl"
        if not os.path.exists(output_file):
            print(f"graph index exists: {output_file}, skip!")
        else:
            G = build_graph(absolute_repo_path, global_import=True)
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'wb') as f:
                pickle.dump(G, f)
        print(f'Processed {args.repo_path}\nBM25 index: {persist_path}\ngraph index: {output_file}')
    except Exception as e:
        print(f'Error processing {args.repo_path}: {e}')

    end_time = time.time()
    print(f'Total Execution time = {end_time - start_time:.3f}s')