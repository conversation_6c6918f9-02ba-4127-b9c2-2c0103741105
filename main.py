import threading
from flask import Flask, request, jsonify

from util.server.retriever import retrieve_code as _retrieve_code
from util.server.file_sync import (
    process_queued,
    process_update_request,
    process_delete_index_request,
    process_get_config_request,
)
from util.server.build_worker import (
    produce_scheduler,
    consume_thread_pool,
)

from starlette.applications import Starlette
from starlette.responses import JSONResponse
from starlette.routing import Route
from starlette.routing import Mount
from mcp.server.fastmcp import FastMCP


mcp_agent = FastMCP("Agent")

def handle_update_request():
    """
    更新索引API端点
    """
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400

    data = request.get_json()
    result, status_code = process_update_request(data)
    return jsonify(result), status_code


def handle_delete_index_request():
    """
    删除索引API端点
    """
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400

    data = request.get_json()
    result, status_code = process_delete_index_request(data)

    return jsonify(result), status_code


async def handle_get_config_request(request):
    """
    获取白名单和支持的文件类型API端点
    """

    try:
        data = await request.json()
    except Exception as e:
        print(type(e))
        return JSONResponse({"status": "error", "message": f"请求必须是JSON格式，错误信息: {str(e)}"}, status_code=500)
    try:
        return JSONResponse(process_get_config_request(data))
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)

def handle_locagent_request():
    if not request.is_json:
        return (
            jsonify(
                {"status": "error", "message": "The request must be in JSON format."}
            ),
            400,
        )

    data = request.get_json()
    required_fields = {"user_name", "root_path", "problem_statement"}
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        return {
            "status": "error",
            "message": f"Missing necessary fields: {', '.join(missing_fields)}",
        }, 400

    user_name = data["user_name"]
    root_path = data["root_path"]
    problem_statement = data["problem_statement"]

    result, status_code = retrieve_code(problem_statement, user_name, root_path)

    return jsonify(result), status_code


@mcp_agent.tool(
    name="retrieve_code",
    description="根据用户指令，获取完成指令所需的相关代码上下文，返回代码路径和代码行号。",
)
def retrieve_code(user_instruction: str, user_name: str, workspace: str) -> str:
    try:
        pred = _retrieve_code(user_instruction, user_name, workspace)
        return {"reasoning": pred.reasoning, "locations": pred.locations}, 200
    except Exception as e:
        return {"status": "error", "message": "Index not found", "error": str(e)}, 400
    

app = Starlette(
    debug=True,
    routes=[
        Route("/api/CodeBaseIndex/indexSingleFile", handle_update_request, methods=['POST']),
        Route("/api/CodeBaseIndex/deleteAllIndex", handle_delete_index_request, methods=['POST']),
        Route("/api/CodeBaseIndex/getConfig", handle_get_config_request, methods=['POST']),
        Route("/api/LocAgent", handle_locagent_request, methods=['POST']),
        Mount("/mcp/agent", app=mcp_agent.sse_app()),
    ]
)


if __name__ == "__main__":
    # 启动文件同步线程
    threading.Thread(target=process_queued, daemon=True).start()
    produce_scheduler()
    consume_thread_pool()
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5000)
