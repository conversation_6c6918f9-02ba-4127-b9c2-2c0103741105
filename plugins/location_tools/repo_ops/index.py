import os
import pickle
import logging
from pathvalidate import sanitize_filename
from typing import <PERSON>ple

from dependency_graph.build_graph_java import build_graph

from plugins.location_tools.retriever.bm25_retriever import (
    build_code_retriever_from_repo as build_code_retriever,
    build_module_retriever_from_graph as build_module_retriever,
    build_retriever_from_persist_dir as load_retriever,
    BM25Retriever,
)
from plugins.location_tools.retriever.vector_retriever import (
    build_code_retriever_from_repo as build_vector_code_retriever,
    build_code_retriever_from_graph as build_vector_code_retriever_from_graph,
    build_retriever_from_persist_dir as load_vector_retriever,
    VectorRetriever,
)

from plugins.location_tools.utils.util import (
    INDEX_DIR,
)

logger = logging.getLogger(__name__)


BM25_SIMILARITY_TOP_K: int = 10
VECTOR_SIMILARITY_TOP_K: int = 10


class Index:

    @classmethod
    def get_index_subdir(cls, index_dir: str):
        return (
            f"{index_dir}/graph_index_v2.3",
            f"{index_dir}/BM25_index",
            f"{index_dir}/VECTOR_index",
        )

    @classmethod
    def generate_instance_id(cls, repo_dir: str) -> str:
        return sanitize_filename(os.path.abspath(repo_dir), replacement_text="_")

    @classmethod
    def exists(
        cls,
        index_dir: str,
        instance_id: str | None = None,
        repo_dir: str | None = None,
    ) -> Tuple[bool, bool, bool]:
        instance_id = instance_id or Index.generate_instance_id(repo_dir)
        if not instance_id:
            raise Exception(f"instance_id or repo_dir must be specified")

        graph_index_subdir, bm25_index_subdir, vector_index_subdir = (
            Index.get_index_subdir(index_dir)
        )
        json_meta_file = os.path.join(graph_index_subdir, f"{instance_id}.json")
        graph_index_file = os.path.join(graph_index_subdir, f"{instance_id}.pkl")
        bm25_index_file = os.path.join(bm25_index_subdir, instance_id, "corpus.jsonl")
        vector_index_file = os.path.join(
            vector_index_subdir, instance_id, "config.jsonl"
        )

        return (
            not os.path.exists(graph_index_file) or not os.path.exists(json_meta_file),
            not os.path.exists(bm25_index_file),
            not os.path.exists(vector_index_file),
        )

    @classmethod
    def build(
        cls,
        repo_dir: str,
        index_dir: str = f"{os.path.expanduser('~')}/.zeroAgent/index",
        instance_id: str | None = None,
        bm25_similarity_top_k: int = BM25_SIMILARITY_TOP_K,
        vector_similarity_top_k: int = VECTOR_SIMILARITY_TOP_K,
        use_vector_retriever: bool = False,
    ) -> "Index":
        if repo_dir is None or not os.path.exists(repo_dir):
            raise Exception("repo_dir must be specified and exists")
        if instance_id is None:
            instance_id = Index.generate_instance_id(repo_dir)
        return cls(
            index_dir=index_dir,
            instance_id=instance_id,
            repo_dir=repo_dir,
            bm25_similarity_top_k=bm25_similarity_top_k,
            vector_similarity_top_k=vector_similarity_top_k,
            use_vector_retriever=use_vector_retriever,
        )

    @classmethod
    def get(
        cls,
        repo_dir: str | None = None,
        index_dir: str = f"{os.path.expanduser('~')}/.zeroAgent/index",
        instance_id: str | None = None,
        bm25_similarity_top_k: int = BM25_SIMILARITY_TOP_K,
        vector_similarity_top_k: int = VECTOR_SIMILARITY_TOP_K,
        use_vector_retriever: bool = False,
    ) -> "Index":
        instance_id = instance_id or Index.generate_instance_id(repo_dir)
        if not instance_id:
            raise Exception(f"instance_id or repo_dir must be specified")
        return cls(
            index_dir=index_dir,
            instance_id=instance_id,
            repo_dir=None,
            bm25_similarity_top_k=bm25_similarity_top_k,
            vector_similarity_top_k=vector_similarity_top_k,
            use_vector_retriever=use_vector_retriever,
        )

    def __init__(
        self,
        index_dir: str,
        instance_id: str,
        bm25_similarity_top_k: int,
        vector_similarity_top_k: int,
        use_vector_retriever: bool,
        repo_dir: str | None = None,
    ):
        self.__index_dir = index_dir
        self.__instance_id = instance_id
        self.__repo_dir = repo_dir
        self.use_vector_retriever = use_vector_retriever
        if self.__repo_dir:
            logger.info(f"Generate index for repo: {self.__repo_dir}")
            self.__build_index(bm25_similarity_top_k, vector_similarity_top_k)

    def __get_index_subdir(self):
        return Index.get_index_subdir(self.__index_dir)

    def __build_index(
        self,
        bm25_similarity_top_k: int,
        vector_similarity_top_k: int,
    ) -> None:
        self.__build_repo_graph_index()
        self.__build_repo_bm25_index(bm25_similarity_top_k)
        if self.use_vector_retriever:
            self.__build_repo_vector_index(vector_similarity_top_k)

    def __build_repo_graph_index(self):
        graph_index_subdir, _, _ = self.__get_index_subdir()
        graph_index_file = os.path.join(graph_index_subdir, f"{self.__instance_id}.pkl")
        logger.info(f"Building repo graph index {graph_index_file}...")
        print(f"Building repo graph index {graph_index_file}...")
        os.makedirs(graph_index_subdir, exist_ok=True)

        # 支持 Java 增量索引
        if os.path.exists(graph_index_file):
            with open(graph_index_file, "rb") as f:
                G = build_graph(pickle.load(f), self.__repo_dir, global_import=True)
        else:
            G = build_graph(None, self.__repo_dir, global_import=True)

        print(f"Graph built successfully!")
        print(f"Total nodes: {G.number_of_nodes()}")
        print(f"Total edges: {G.number_of_edges()}")

        with open(graph_index_file, "wb") as f:
            pickle.dump(G, f)

    def __build_repo_bm25_index(
        self,
        similarity_top_k: int = BM25_SIMILARITY_TOP_K,
    ) -> None:
        _, bm25_index_subdir, _ = self.__get_index_subdir()
        persist_path = os.path.join(bm25_index_subdir, self.__instance_id)
        logger.info(f"Building repo bm25 index {persist_path}...")
        print(f"Building repo bm25 index {persist_path}...")
        os.makedirs(bm25_index_subdir, exist_ok=True)
        build_code_retriever(
            os.path.abspath(self.__repo_dir),
            persist_path=persist_path,
            similarity_top_k=similarity_top_k,
        )

    def __build_repo_vector_index(
        self,
        similarity_top_k: int = VECTOR_SIMILARITY_TOP_K,
    ):
        _, _, vector_index_subdir = self.__get_index_subdir()
        persist_path = os.path.join(vector_index_subdir, self.__instance_id)
        logger.info(f"Building repo vector index {persist_path}...")
        print(f"Building repo vector index {persist_path}...")
        os.makedirs(vector_index_subdir, exist_ok=True)
        build_vector_code_retriever(
            repo_path=os.path.abspath(self.__repo_dir),
            persist_path=persist_path,
            similarity_top_k=similarity_top_k,
        )

    def load(self):
        return (
            self.__load_repo_graph_index(),
            self.__load_repo_bm25_index(),
            self.__load_repo_vector_index(),
        )

    def __load_repo_graph_index(self):
        graph_index_subdir, _, _ = self.__get_index_subdir()
        graph_index_file = os.path.join(graph_index_subdir, f"{self.__instance_id}.pkl")
        try:
            return pickle.load(open(graph_index_file, "rb"))
        except Exception as e:
            raise Exception(f"Failed to load graph index from {graph_index_file}: {e}")

    def __load_repo_bm25_index(self):
        _, bm25_index_subdir, _ = self.__get_index_subdir()
        persist_path = os.path.join(bm25_index_subdir, self.__instance_id)
        try:
            return load_retriever(persist_path)
        except Exception as e:
            raise Exception(f"Failed to load BM25 index from {persist_path}: {e}")

    def __load_repo_vector_index(self):
        if self.use_vector_retriever:
            _, _, vector_index_subdir = self.__get_index_subdir()
            persist_path = os.path.join(vector_index_subdir, self.__instance_id)
            try:
                return load_vector_retriever(persist_path)
            except Exception as e:
                raise Exception(f"Failed to load vector index from {persist_path}: {e}")
        else:
            return None

    def get_current_issue_id(self) -> str:
        return self.__instance_id
