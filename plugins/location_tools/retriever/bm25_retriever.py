import os
import pickle
import <PERSON><PERSON><PERSON>
import fnmatch
import mimetypes
from typing import Dict, List, Optional

from llama_index.core import Simple<PERSON>ire<PERSON><PERSON><PERSON>eader
from llama_index.core import Document
from llama_index.core.node_parser import Simple<PERSON>ileNodeParser
from llama_index.retrievers.bm25 import <PERSON><PERSON>25<PERSON><PERSON>riever
from repo_index.index.epic_split import Epic<PERSON><PERSON>litter
from llama_index.core.vector_stores.utils import (
    metadata_dict_to_node,
)
import bm25s

from dependency_graph import RepoEntitySearcher
from dependency_graph.traverse_graph import is_test_file
from dependency_graph.build_graph import (
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_FILE,
    NODE_TYPE_CLASS,
    NODE_TYPE_FUNCTION,
)
from plugins.location_tools.utils.util import is_delete_file, get_raw_name_of_delete_file
import warnings

warnings.simplefilter('ignore', FutureWarning)

NTYPES = [
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_FILE,
    NODE_TYPE_FUNCTION,
    NODE_TYPE_CLASS,
]
def get_delete_files(directory):
    delete_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if is_delete_file(file):
                file_path = os.path.join(root, get_raw_name_of_delete_file(file))
                relative_path = os.path.relpath(file_path, directory)
                delete_files.append(relative_path)

    return delete_files

def get_current_files(docs):
    files = set()
    for doc in docs:
        files.add(doc.metadata['file_path'])
    return list(files)

def get_nodes_from_persist_dir(removed_files, bm25_persist_path):
    if not os.path.exists(f'{bm25_persist_path}/corpus.jsonl'):
        return []
    bm25 = bm25s.BM25.load(bm25_persist_path, load_corpus=True)
    corpus = []
    if bm25 is not None:
        corpus = bm25.corpus
    nodes = [metadata_dict_to_node(node) for node in corpus]
    reserved_nodes = [node for node in nodes if node.metadata['file_path'] not in removed_files]
    return reserved_nodes

def build_code_retriever_from_repo(repo_path,
                                   similarity_top_k=10,
                                   min_chunk_size=100,
                                   chunk_size=500,
                                   max_chunk_size=2000,
                                   hard_token_limit=2000,
                                   max_chunks=200,
                                   persist_path=None,
                                   show_progress=False,
                                   ):
    # print(repo_path)
    # Only extract file name and type to not trigger unnecessary embedding jobs
    def file_metadata_func(file_path: str) -> Dict:
        # print(file_path)
        file_path = file_path.replace(repo_path, '')
        if file_path.startswith('/'):
            file_path = file_path[1:]

        test_patterns = [
            '**/test/**',
            '**/tests/**',
            '**/test_*.py',
            '**/*_test.py',
        ]
        category = (
            'test'
            if any(fnmatch.fnmatch(file_path, pattern) for pattern in test_patterns)
            else 'implementation'
        )

        return {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_type': mimetypes.guess_type(file_path)[0],
            'category': category,
        }
    docs = []
    try:
        reader = SimpleDirectoryReader(
            input_dir=repo_path,
            exclude=[
                '**/test/**',
                '**/tests/**',
                '**/test_*.java',
                '**/*_test.java',
            ],
            file_metadata=file_metadata_func,
            filename_as_id=True,
            required_exts=['.java'],  # TODO: Shouldn't be hardcoded and filtered
            recursive=True,
        )
        docs = reader.load_data()
    except ValueError as e:
        print(f"Error: {str(e)}")
    prepared_nodes = []
    current_files = []
    if docs:
        splitter = EpicSplitter(
            language='java',
            min_chunk_size=min_chunk_size,
            chunk_size=chunk_size,
            max_chunk_size=max_chunk_size,
            hard_token_limit=hard_token_limit,
            max_chunks=max_chunks,
            repo_path=repo_path,
        )
        prepared_nodes = splitter.get_nodes_from_documents(docs, show_progress=show_progress)
        current_files = get_current_files(docs)
    delete_files = get_delete_files(repo_path)
    current_files.extend(delete_files)
    reserved_nodes = get_nodes_from_persist_dir(current_files, persist_path)
    prepared_nodes.extend(reserved_nodes)

    # We can pass in the index, docstore, or list of nodes to create the retriever
    retriever = BM25Retriever.from_defaults(
        nodes=prepared_nodes,
        similarity_top_k=similarity_top_k,
        stemmer=Stemmer.Stemmer("english"),
        language="english",
    )
    if persist_path:
        retriever.persist(persist_path)
    return retriever


def build_retriever_from_persist_dir(path: str):
    retriever = BM25Retriever.from_persist_dir(path)
    return retriever


def build_module_retriever_from_graph(graph_path: Optional[str] = None,
                                      entity_searcher: Optional[RepoEntitySearcher] = None,
                                      search_scope: str = 'all',
                                      # enum = {'function', 'class', 'file', 'all'}
                                      similarity_top_k: int = 10,

                                      ):
    assert search_scope in NTYPES or search_scope == 'all'
    assert graph_path or isinstance(entity_searcher, RepoEntitySearcher)

    if graph_path:
        G = pickle.load(open(graph_path, "rb"))
        entity_searcher = RepoEntitySearcher(G)
    else:
        G = entity_searcher.G

    selected_nodes = list()
    for nid in G:
        if is_test_file(nid): continue

        ndata = entity_searcher.get_node_data([nid])[0]
        ndata['nid'] = nid  # add `nid` property
        if search_scope == 'all':  # and ndata['type'] in NTYPES[2:]
            selected_nodes.append(ndata)
        elif ndata['type'] == search_scope:
            selected_nodes.append(ndata)

    # initialize node parser
    splitter = SimpleFileNodeParser()
    documents = [Document(text=t['nid']) for t in selected_nodes]
    nodes = splitter.get_nodes_from_documents(documents)

    # We can pass in the index, docstore, or list of nodes to create the retriever
    retriever = BM25Retriever.from_defaults(
        nodes=nodes,
        similarity_top_k=similarity_top_k,
        stemmer=Stemmer.Stemmer("english"),
        language="english",
    )

    return retriever
