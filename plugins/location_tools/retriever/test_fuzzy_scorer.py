"""
Python test cases for fuzzy_scorer module.
Translated from TypeScript test cases to ensure strict alignment with original behavior.
"""

import os
import unittest
from typing import Optional
from dataclasses import dataclass

from plugins.location_tools.retriever.fuzzy_score import prepare_query, piece_to_query, SEP, IS_WINDOWS, score_fuzzy, FuzzyScore
from plugins.location_tools.retriever.fuzzy_score_item import IItemAccessor, score_item_fuzzy, FuzzyScorerCache
from plugins.location_tools.retriever.fuzzy_compare_items import compare_items_by_fuzzy_score


# Schemas constants - equivalent to Schemas in TypeScript
class Schemas:
    vscodeRemote = 'vscode-remote'


@dataclass
class URI:
    """
    Simple URI class to represent file paths.
    Equivalent to URI class in TypeScript.
    """
    scheme: str
    authority: str
    path: str
    query: str
    fragment: str

    @property
    def fs_path(self) -> str:
        """
        Get the file system path representation.
        Equivalent to fsPath property in TypeScript URI.
        """
        if IS_WINDOWS:
            # On Windows, convert forward slashes to backslashes
            # and handle drive letters properly
            path = self.path.replace('/', '\\')
            if path.startswith('\\') and len(path) > 1 and path[2] == ':':
                # Remove leading slash for drive letters like /C:/path
                path = path[1:]
            return path
        else:
            return self.path

    @classmethod
    def file(cls, path: str) -> 'URI':
        """
        Create a file URI from a path.
        Equivalent to URI.file() in TypeScript.
        """
        # Normalize path separators
        if IS_WINDOWS:
            path = path.replace('\\', '/')
            # Add leading slash for absolute paths on Windows
            if len(path) > 1 and path[1] == ':':
                path = '/' + path

        return cls(
            scheme='file',
            authority='',
            path=path,
            query='',
            fragment=''
        )

    @classmethod
    def from_dict(cls, components: dict) -> 'URI':
        """
        Create a URI from components dictionary.
        Equivalent to URI.from() in TypeScript.
        """
        return cls(
            scheme=components.get('scheme', ''),
            authority=components.get('authority', ''),
            path=components.get('path', ''),
            query=components.get('query', ''),
            fragment=components.get('fragment', '')
        )


def basename(path: str) -> str:
    """
    Get the basename of a path.
    Equivalent to basename function in TypeScript path module.
    """
    return os.path.basename(path)


def dirname(path: str) -> str:
    """
    Get the directory name of a path.
    Equivalent to dirname function in TypeScript path module.
    """
    return os.path.dirname(path)


def posix_normalize(path: str) -> str:
    """
    Normalize a path using POSIX conventions (forward slashes).
    Equivalent to posix.normalize in TypeScript.
    """
    return os.path.normpath(path).replace('\\', '/')


def win32_normalize(path: str) -> str:
    """
    Normalize a path using Windows conventions (backslashes).
    Equivalent to win32.normalize in TypeScript.
    """
    return os.path.normpath(path).replace('/', '\\')


class ResourceAccessorClass(IItemAccessor[URI]):
    """
    Accessor for URI resources using file system paths.
    Translated from ResourceAccessorClass in fuzzyScorer.test.ts (lines 14-27)
    """

    def get_item_label(self, resource: URI) -> str:
        return basename(resource.fs_path)

    def get_item_description(self, resource: URI) -> str:
        return dirname(resource.fs_path)

    def get_item_path(self, resource: URI) -> str:
        return resource.fs_path


class ResourceWithSlashAccessorClass(IItemAccessor[URI]):
    """
    Accessor for URI resources using POSIX-style paths (forward slashes).
    Translated from ResourceWithSlashAccessorClass in fuzzyScorer.test.ts (lines 31-44)
    """

    def get_item_label(self, resource: URI) -> str:
        return basename(resource.fs_path)

    def get_item_description(self, resource: URI) -> str:
        return posix_normalize(dirname(resource.path))

    def get_item_path(self, resource: URI) -> str:
        return posix_normalize(resource.path)


class ResourceWithBackslashAccessorClass(IItemAccessor[URI]):
    """
    Accessor for URI resources using Windows-style paths (backslashes).
    Translated from ResourceWithBackslashAccessorClass in fuzzyScorer.test.ts (lines 48-61)
    """

    def get_item_label(self, resource: URI) -> str:
        return basename(resource.fs_path)

    def get_item_description(self, resource: URI) -> str:
        return win32_normalize(dirname(resource.path))

    def get_item_path(self, resource: URI) -> str:
        return win32_normalize(resource.path)


class NullAccessorClass(IItemAccessor[URI]):
    """
    Null accessor that returns None for all methods.
    Translated from NullAccessorClass in fuzzyScorer.test.ts (lines 65-78)
    """

    def get_item_label(self, resource: URI) -> Optional[str]:
        _ = resource  # Suppress unused parameter warning
        return None  # Equivalent to undefined! in TypeScript

    def get_item_description(self, resource: URI) -> Optional[str]:
        _ = resource  # Suppress unused parameter warning
        return None  # Equivalent to undefined! in TypeScript

    def get_item_path(self, resource: URI) -> Optional[str]:
        _ = resource  # Suppress unused parameter warning
        return None  # Equivalent to undefined! in TypeScript


# Create instances - equivalent to const declarations in TypeScript
ResourceAccessor = ResourceAccessorClass()
ResourceWithSlashAccessor = ResourceWithSlashAccessorClass()
ResourceWithBackslashAccessor = ResourceWithBackslashAccessorClass()
NullAccessor = NullAccessorClass()


def score_item(item: URI, query: str, allow_non_contiguous_matches: bool,
               accessor: IItemAccessor[URI], cache: Optional[FuzzyScorerCache] = None):
    """
    Helper function to score an item against a query string.
    Translated from scoreItem function in fuzzyScorer.test.ts (lines 92-94)

    Args:
        item: The item to score (generic type T, here URI)
        query: The query string to match against
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches
        accessor: Accessor to get label, description, and path from the item
        cache: Optional cache dictionary to store and retrieve scoring results

    Returns:
        ItemScore containing the score and match positions
    """
    if cache is None:
        cache = {}  # Equivalent to Object.create(null) in TypeScript
    print(prepare_query(query))
    return score_item_fuzzy(item, prepare_query(query), allow_non_contiguous_matches, accessor, cache)


def compare_items_by_score(item_a: URI, item_b: URI, query: str, allow_non_contiguous_matches: bool,
                          accessor: IItemAccessor[URI]) -> int:
    """
    Helper function to compare two items by their fuzzy scores.
    Translated from compareItemsByScore function in fuzzyScorer.test.ts (lines 96-98)

    Args:
        item_a: First item to compare
        item_b: Second item to compare
        query: The query string to match against
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches
        accessor: Accessor to get label, description, and path from items

    Returns:
        Comparison result: -1 if item_a should come before item_b,
                          1 if item_b should come before item_a,
                          0 if they are equal
    """
    cache = {}  # Equivalent to Object.create(null) in TypeScript
    return compare_items_by_fuzzy_score(item_a, item_b, prepare_query(query), allow_non_contiguous_matches, accessor, cache)


def _do_score(target: str, query: str, allow_non_contiguous_matches: bool = None) -> FuzzyScore:
    """
    Helper function to score a target against a query.
    Translated from _do_score function in fuzzyScorer.test.ts

    Args:
        target: The target string to match against
        query: The query string to match
        allow_non_contiguous_matches: Whether to allow non-contiguous matches

    Returns:
        FuzzyScore tuple containing (score, match_positions)
    """
    prepared_query = prepare_query(query)

    if allow_non_contiguous_matches is None:
        allow_non_contiguous_matches = not prepared_query.expectContiguousMatch

    return score_fuzzy(target, prepared_query.normalized, prepared_query.normalizedLowercase, allow_non_contiguous_matches)


class TestFuzzyScorer(unittest.TestCase):
    """Test cases for fuzzy scorer functionality."""

    def test_score_item_matches_are_proper(self):
        """
        Test scoreItem - matches are proper.
        Translated from 'scoreItem - matches are proper' test in fuzzyScorer.test.ts (lines 150-227)
        """
        # Test with null item
        res = score_item(None, 'something', True, ResourceAccessor)
        self.assertFalse(res.score)

        # Test with valid resource
        resource = URI.file('/xyz/some/path/someFile123.txt')

        # Test with null accessor
        res = score_item(resource, 'something', True, NullAccessor)
        self.assertFalse(res.score)

        # Test path identity
        identity_res = score_item(resource, ResourceAccessor.get_item_path(resource), True, ResourceAccessor)
        self.assertTrue(identity_res.score)
        self.assertEqual(len(identity_res.description_match), 1)
        self.assertEqual(len(identity_res.label_match), 1)
        self.assertEqual(identity_res.description_match[0]['start'], 0)
        self.assertEqual(identity_res.description_match[0]['end'], len(ResourceAccessor.get_item_description(resource)))
        self.assertEqual(identity_res.label_match[0]['start'], 0)
        self.assertEqual(identity_res.label_match[0]['end'], len(ResourceAccessor.get_item_label(resource)))

        # Test basename prefix
        basename_prefix_res = score_item(resource, 'som', True, ResourceAccessor)
        self.assertTrue(basename_prefix_res.score)
        self.assertIsNone(basename_prefix_res.description_match)
        self.assertEqual(len(basename_prefix_res.label_match), 1)
        self.assertEqual(basename_prefix_res.label_match[0]['start'], 0)
        self.assertEqual(basename_prefix_res.label_match[0]['end'], len('som'))

        # Test basename camelcase
        basename_camelcase_res = score_item(resource, 'sF', True, ResourceAccessor)
        self.assertTrue(basename_camelcase_res.score)
        self.assertIsNone(basename_camelcase_res.description_match)
        self.assertEqual(len(basename_camelcase_res.label_match), 2)
        self.assertEqual(basename_camelcase_res.label_match[0]['start'], 0)
        self.assertEqual(basename_camelcase_res.label_match[0]['end'], 1)
        self.assertEqual(basename_camelcase_res.label_match[1]['start'], 4)
        self.assertEqual(basename_camelcase_res.label_match[1]['end'], 5)

        # Test basename match
        basename_res = score_item(resource, 'of', True, ResourceAccessor)
        self.assertTrue(basename_res.score)
        self.assertIsNone(basename_res.description_match)
        self.assertEqual(len(basename_res.label_match), 2)
        self.assertEqual(basename_res.label_match[0]['start'], 1)
        self.assertEqual(basename_res.label_match[0]['end'], 2)
        self.assertEqual(basename_res.label_match[1]['start'], 4)
        self.assertEqual(basename_res.label_match[1]['end'], 5)

        # Test path match
        path_res = score_item(resource, 'xyz123', True, ResourceAccessor)
        self.assertTrue(path_res.score)
        self.assertTrue(path_res.description_match)
        self.assertTrue(path_res.label_match)
        self.assertEqual(len(path_res.label_match), 1)
        self.assertEqual(path_res.label_match[0]['start'], 8)
        self.assertEqual(path_res.label_match[0]['end'], 11)
        self.assertEqual(len(path_res.description_match), 1)
        self.assertEqual(path_res.description_match[0]['start'], 1)
        self.assertEqual(path_res.description_match[0]['end'], 4)

        # Test no match
        no_res = score_item(resource, '987', True, ResourceAccessor)
        self.assertFalse(no_res.score)
        self.assertIsNone(no_res.label_match)
        self.assertIsNone(no_res.description_match)

        # Test no exact match
        no_exact_res = score_item(resource, '"sF"', True, ResourceAccessor)
        self.assertFalse(no_exact_res.score)
        self.assertIsNone(no_exact_res.label_match)
        self.assertIsNone(no_exact_res.description_match)
        self.assertEqual(no_res.score, no_exact_res.score)

        # Verify scores
        self.assertTrue(identity_res.score > basename_prefix_res.score)
        self.assertTrue(basename_prefix_res.score > basename_res.score)
        self.assertTrue(basename_res.score > path_res.score)
        self.assertTrue(path_res.score > no_res.score)

    def test_score_item_multiple(self):
        """
        Test scoreItem function with multiple query pieces.
        Translated from scoreItem multiple tests in fuzzyScorer.test.ts (lines 229-270)
        """
        resource = URI.file('/xyz/some/path/someFile123.txt')

        res1 = score_item(resource, 'xyz some', True, ResourceAccessor)
        self.assertTrue(res1.score)
        self.assertEqual(len(res1.label_match), 1)
        self.assertEqual(res1.label_match[0]['start'], 0)
        self.assertEqual(res1.label_match[0]['end'], 4)
        self.assertEqual(len(res1.description_match), 1)
        self.assertEqual(res1.description_match[0]['start'], 1)
        self.assertEqual(res1.description_match[0]['end'], 4)

        res2 = score_item(resource, 'some xyz', True, ResourceAccessor)
        self.assertTrue(res2.score)
        self.assertEqual(res1.score, res2.score)
        self.assertEqual(len(res2.label_match), 1)
        self.assertEqual(res2.label_match[0]['start'], 0)
        self.assertEqual(res2.label_match[0]['end'], 4)
        self.assertEqual(len(res2.description_match), 1)
        self.assertEqual(res2.description_match[0]['start'], 1)
        self.assertEqual(res2.description_match[0]['end'], 4)

        res3 = score_item(resource, 'some xyz file file123', True, ResourceAccessor)
        self.assertTrue(res3.score)
        self.assertTrue(res3.score > res2.score)
        self.assertEqual(len(res3.label_match), 1)
        self.assertEqual(res3.label_match[0]['start'], 0)
        self.assertEqual(res3.label_match[0]['end'], 11)
        self.assertEqual(len(res3.description_match), 1)
        self.assertEqual(res3.description_match[0]['start'], 1)
        self.assertEqual(res3.description_match[0]['end'], 4)

        res4 = score_item(resource, 'path z y', True, ResourceAccessor)
        self.assertTrue(res4.score)
        self.assertTrue(res4.score < res2.score)
        self.assertEqual(len(res4.label_match), 0)
        self.assertEqual(len(res4.description_match), 2)
        self.assertEqual(res4.description_match[0]['start'], 2)
        self.assertEqual(res4.description_match[0]['end'], 4)
        self.assertEqual(res4.description_match[1]['start'], 10)
        self.assertEqual(res4.description_match[1]['end'], 14)

    def test_score_item_multiple_with_cache_yields_different_results(self):
        """
        Test scoreItem - multiple with cache yields different results.
        Translated from 'scoreItem - multiple with cache yields different results' test in fuzzyScorer.test.ts (lines 272-281)
        """
        resource = URI.file('/xyz/some/path/someFile123.txt')
        cache = {}
        res1 = score_item(resource, 'xyz sm', True, ResourceAccessor, cache)
        self.assertTrue(res1.score)

        # from the cache's perspective this should be a totally different query
        res2 = score_item(resource, 'xyz "sm"', True, ResourceAccessor, cache)
        self.assertFalse(res2.score)

    def test_score_item_invalid_input(self):
        """
        Test scoreItem - invalid input.
        Translated from 'scoreItem - invalid input' test in fuzzyScorer.test.ts (lines 283-290)
        """
        res = score_item(None, None, True, ResourceAccessor)
        self.assertEqual(res.score, 0)

        res = score_item(None, 'null', True, ResourceAccessor)
        self.assertEqual(res.score, 0)

    def test_score_item_optimize_for_file_paths(self):
        """
        Test scoreItem - optimize for file paths.
        Translated from 'scoreItem - optimize for file paths' test in fuzzyScorer.test.ts (lines 292-308)
        """
        resource = URI.file('/xyz/others/spath/some/xsp/file123.txt')

        # xsp is more relevant to the end of the file path even though it matches
        # fuzzy also in the beginning. we verify the more relevant match at the
        # end gets returned.
        path_res = score_item(resource, 'xspfile123', True, ResourceAccessor)
        self.assertTrue(path_res.score)
        self.assertTrue(path_res.description_match)
        self.assertTrue(path_res.label_match)
        self.assertEqual(len(path_res.label_match), 1)
        self.assertEqual(path_res.label_match[0]['start'], 0)
        self.assertEqual(path_res.label_match[0]['end'], 7)
        self.assertEqual(len(path_res.description_match), 1)
        self.assertEqual(path_res.description_match[0]['start'], 23)
        self.assertEqual(path_res.description_match[0]['end'], 26)

    def test_score_item_avoid_match_scattering_bug_36119(self):
        """
        Test scoreItem - avoid match scattering (bug #36119).
        Translated from 'scoreItem - avoid match scattering (bug #36119)' test in fuzzyScorer.test.ts (lines 310-320)
        """
        resource = URI.file('projects/ui/cula/ats/target.mk')

        path_res = score_item(resource, 'tcltarget.mk', True, ResourceAccessor)
        self.assertTrue(path_res.score)
        self.assertTrue(path_res.description_match)
        self.assertTrue(path_res.label_match)
        self.assertEqual(len(path_res.label_match), 1)
        self.assertEqual(path_res.label_match[0]['start'], 0)
        self.assertEqual(path_res.label_match[0]['end'], 9)

    def test_score_item_prefers_more_compact_matches(self):
        """
        Test scoreItem - prefers more compact matches.
        Translated from 'scoreItem - prefers more compact matches' test in fuzzyScorer.test.ts (lines 322-336)
        """
        resource = URI.file('/1a111d1/11a1d1/something.txt')

        # expect "ad" to be matched towards the end of the file because the
        # match is more compact
        res = score_item(resource, 'ad', True, ResourceAccessor)
        self.assertTrue(res.score)
        self.assertTrue(res.description_match)
        self.assertFalse(len(res.label_match))  # Equivalent to assert.ok(!res.labelMatch!.length)
        self.assertEqual(len(res.description_match), 2)
        self.assertEqual(res.description_match[0]['start'], 11)
        self.assertEqual(res.description_match[0]['end'], 12)
        self.assertEqual(res.description_match[1]['start'], 13)
        self.assertEqual(res.description_match[1]['end'], 14)

    def test_score_item_proper_target_offset(self):
        """
        Test scoreItem - proper target offset.
        Translated from 'scoreItem - proper target offset' test in fuzzyScorer.test.ts (lines 338-343)
        """
        resource = URI.file('etem')

        res = score_item(resource, 'teem', True, ResourceAccessor)
        self.assertFalse(res.score)

    def test_score_item_proper_target_offset_2(self):
        """
        Test scoreItem - proper target offset #2.
        Translated from 'scoreItem - proper target offset #2' test in fuzzyScorer.test.ts (lines 345-353)
        """
        resource = URI.file('ede')

        res = score_item(resource, 'de', True, ResourceAccessor)

        self.assertEqual(len(res.label_match), 1)
        self.assertEqual(res.label_match[0]['start'], 1)
        self.assertEqual(res.label_match[0]['end'], 3)

    def test_score_item_proper_target_offset_3(self):
        """
        Test scoreItem - proper target offset #3.
        Translated from 'scoreItem - proper target offset #3' test in fuzzyScorer.test.ts (lines 355-373)
        """
        resource = URI.file('/src/vs/editor/browser/viewParts/lineNumbers/flipped-cursor-2x.svg')

        res = score_item(resource, 'debug', True, ResourceAccessor)

        self.assertEqual(len(res.description_match), 3)
        self.assertEqual(res.description_match[0]['start'], 9)
        self.assertEqual(res.description_match[0]['end'], 10)
        self.assertEqual(res.description_match[1]['start'], 36)
        self.assertEqual(res.description_match[1]['end'], 37)
        self.assertEqual(res.description_match[2]['start'], 40)
        self.assertEqual(res.description_match[2]['end'], 41)

        self.assertEqual(len(res.label_match), 2)
        self.assertEqual(res.label_match[0]['start'], 9)
        self.assertEqual(res.label_match[0]['end'], 10)
        self.assertEqual(res.label_match[1]['start'], 20)
        self.assertEqual(res.label_match[1]['end'], 21)

    def test_score_item_no_match_unless_query_contained_in_sequence(self):
        """
        Test scoreItem - no match unless query contained in sequence.
        Translated from 'scoreItem - no match unless query contained in sequence' test in fuzzyScorer.test.ts (lines 375-380)
        """
        resource = URI.file('abcde')

        res = score_item(resource, 'edcda', True, ResourceAccessor)
        self.assertFalse(res.score)

    def test_score_item_match_if_using_slash_or_backslash_local_remote_resource(self):
        """
        Test scoreItem - match if using slash or backslash (local, remote resource).
        Translated from 'scoreItem - match if using slash or backslash (local, remote resource)' test in fuzzyScorer.test.ts (lines 382-405)
        """
        local_resource = URI.file('abcde/super/duper')
        remote_resource = URI.from_dict({'scheme': Schemas.vscodeRemote, 'path': 'abcde/super/duper'})

        for resource in [local_resource, remote_resource]:
            res = score_item(resource, 'abcde\\super\\duper', True, ResourceAccessor)
            self.assertTrue(res.score)

            res = score_item(resource, 'abcde\\super\\duper', True, ResourceWithSlashAccessor)
            self.assertTrue(res.score)

            res = score_item(resource, 'abcde\\super\\duper', True, ResourceWithBackslashAccessor)
            self.assertTrue(res.score)

            res = score_item(resource, 'abcde/super/duper', True, ResourceAccessor)
            self.assertTrue(res.score)

            res = score_item(resource, 'abcde/super/duper', True, ResourceWithSlashAccessor)
            self.assertTrue(res.score)

            res = score_item(resource, 'abcde/super/duper', True, ResourceWithBackslashAccessor)
            self.assertTrue(res.score)

    def test_score_item_ensure_upper_case_bonus_only_applies_on_non_consecutive_matches_bug_134723(self):
        """
        Test scoreItem - ensure upper case bonus only applies on non-consecutive matches (bug #134723).
        Translated from 'scoreItem - ensure upper case bonus only applies on non-consecutive matches (bug #134723)' test in fuzzyScorer.test.ts (lines 407-412)
        """
        resource_with_upper = URI.file('ASDFasdfasdf')
        resource_all_lower = URI.file('asdfasdfasdf')

        score_all_lower = score_item(resource_all_lower, 'asdf', True, ResourceAccessor).score
        score_with_upper = score_item(resource_with_upper, 'asdf', True, ResourceAccessor).score

        self.assertTrue(score_all_lower > score_with_upper)

    def test_compare_items_by_score_identity(self):
        """
        Test compareItemsByScore - identity.
        Translated from 'compareItemsByScore - identity' test in fuzzyScorer.test.ts (lines 414-444)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        # Full resource A path
        query = ResourceAccessor.get_item_path(resource_a)

        from functools import cmp_to_key
        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        # Full resource B path
        query = ResourceAccessor.get_item_path(resource_b)

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_basename_prefix(self):
        """
        Test compareFilesByScore - basename prefix.
        Translated from 'compareFilesByScore - basename prefix' test in fuzzyScorer.test.ts (lines 446-476)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        from functools import cmp_to_key

        # Full resource A basename
        query = ResourceAccessor.get_item_label(resource_a)

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        # Full resource B basename
        query = ResourceAccessor.get_item_label(resource_b)

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_basename_camelcase(self):
        """
        Test compareFilesByScore - basename camelcase.
        Translated from 'compareFilesByScore - basename camelcase' test in fuzzyScorer.test.ts (lines 478-508)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        from functools import cmp_to_key

        # resource A camelcase
        query = 'fA'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        # resource B camelcase
        query = 'fB'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_basename_scores(self):
        """
        Test compareFilesByScore - basename scores.
        Translated from 'compareFilesByScore - basename scores' test in fuzzyScorer.test.ts (lines 510-540)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        from functools import cmp_to_key

        # Resource A part of basename
        query = 'fileA'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        # Resource B part of basename
        query = 'fileB'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_path_scores(self):
        """
        Test compareFilesByScore - path scores.
        Translated from 'compareFilesByScore - path scores' test in fuzzyScorer.test.ts (lines 542-572)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        from functools import cmp_to_key

        # Resource A part of path
        query = 'pathfileA'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        # Resource B part of path
        query = 'pathfileB'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_prefer_shorter_basenames(self):
        """
        Test compareFilesByScore - prefer shorter basenames.
        Translated from 'compareFilesByScore - prefer shorter basenames' test in fuzzyScorer.test.ts (lines 574-591)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileBLonger.txt')
        resource_c = URI.file('/unrelated/the/path/other/fileC.txt')

        from functools import cmp_to_key

        # Resource A part of path
        query = 'somepath'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_prefer_shorter_basenames_match_on_basename(self):
        """
        Test compareFilesByScore - prefer shorter basenames (match on basename).
        Translated from 'compareFilesByScore - prefer shorter basenames (match on basename)' test in fuzzyScorer.test.ts (lines 593-610)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileBLonger.txt')
        resource_c = URI.file('/unrelated/the/path/other/fileC.txt')

        from functools import cmp_to_key

        # Resource A part of path
        query = 'file'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_c)
        self.assertEqual(res[2], resource_b)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_c)
        self.assertEqual(res[2], resource_b)

    def test_compare_files_by_score_prefer_shorter_paths(self):
        """
        Test compareFilesByScore - prefer shorter paths.
        Translated from 'compareFilesByScore - prefer shorter paths' test in fuzzyScorer.test.ts (lines 612-629)
        """
        resource_a = URI.file('/some/path/fileA.txt')
        resource_b = URI.file('/some/path/other/fileB.txt')
        resource_c = URI.file('/unrelated/some/path/other/fileC.txt')

        from functools import cmp_to_key

        # Resource A part of path
        query = 'somepath'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_prefer_shorter_paths_bug_17443(self):
        """
        Test compareFilesByScore - prefer shorter paths (bug #17443).
        Translated from 'compareFilesByScore - prefer shorter paths (bug #17443)' test in fuzzyScorer.test.ts (lines 631-642)
        """
        resource_a = URI.file('config/test/t1.js')
        resource_b = URI.file('config/test.js')
        resource_c = URI.file('config/test/t2.js')

        from functools import cmp_to_key

        query = 'co/te'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)
        self.assertEqual(res[2], resource_c)

    def test_compare_files_by_score_prefer_matches_in_label_over_description_if_scores_are_otherwise_equal(self):
        """
        Test compareFilesByScore - prefer matches in label over description if scores are otherwise equal.
        Translated from 'compareFilesByScore - prefer matches in label over description if scores are otherwise equal' test in fuzzyScorer.test.ts (lines 644-653)
        """
        resource_a = URI.file('parts/quick/arrow-left-dark.svg')
        resource_b = URI.file('parts/quickopen/quickopen.ts')

        from functools import cmp_to_key

        query = 'partsquick'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_prefer_camel_case_matches(self):
        """
        Test compareFilesByScore - prefer camel case matches.
        Translated from 'compareFilesByScore - prefer camel case matches' test in fuzzyScorer.test.ts (lines 655-668)
        """
        resource_a = URI.file('config/test/NullPointerException.java')
        resource_b = URI.file('config/test/nopointerexception.java')

        from functools import cmp_to_key

        for query in ['npe', 'NPE']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_prefer_more_compact_camel_case_matches(self):
        """
        Test compareFilesByScore - prefer more compact camel case matches.
        Translated from 'compareFilesByScore - prefer more compact camel case matches' test in fuzzyScorer.test.ts (lines 670-683)
        """
        resource_a = URI.file('config/test/openthisAnythingHandler.js')
        resource_b = URI.file('config/test/openthisisnotsorelevantforthequeryAnyHand.js')

        from functools import cmp_to_key

        query = 'AH'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_prefer_more_compact_matches_label(self):
        """
        Test compareFilesByScore - prefer more compact matches (label).
        Translated from 'compareFilesByScore - prefer more compact matches (label)' test in fuzzyScorer.test.ts (lines 685-698)
        """
        resource_a = URI.file('config/test/examasdaple.js')
        resource_b = URI.file('config/test/exampleasdaasd.ts')

        from functools import cmp_to_key

        query = 'xp'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_prefer_more_compact_matches_path(self):
        """
        Test compareFilesByScore - prefer more compact matches (path).
        Translated from 'compareFilesByScore - prefer more compact matches (path)' test in fuzzyScorer.test.ts (lines 700-713)
        """
        resource_a = URI.file('config/test/examasdaple/file.js')
        resource_b = URI.file('config/test/exampleasdaasd/file.ts')

        from functools import cmp_to_key

        query = 'xp'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_prefer_more_compact_matches_label_and_path(self):
        """
        Test compareFilesByScore - prefer more compact matches (label and path).
        Translated from 'compareFilesByScore - prefer more compact matches (label and path)' test in fuzzyScorer.test.ts (lines 715-728)
        """
        resource_a = URI.file('config/example/thisfile.ts')
        resource_b = URI.file('config/24234243244/example/file.js')

        from functools import cmp_to_key

        query = 'exfile'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_avoid_match_scattering_bug_34210(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #34210).
        Translated from 'compareFilesByScore - avoid match scattering (bug #34210)' test in fuzzyScorer.test.ts (lines 730-751)
        """
        resource_a = URI.file('node_modules1/bundle/lib/model/modules/ot1/index.js')
        resource_b = URI.file('node_modules1/bundle/lib/model/modules/un1/index.js')
        resource_c = URI.file('node_modules1/bundle/lib/model/modules/modu1/index.js')
        resource_d = URI.file('node_modules1/bundle/lib/model/modules/oddl1/index.js')

        from functools import cmp_to_key

        query = 'modu1\\index.js' if IS_WINDOWS else 'modu1/index.js'

        res = sorted([resource_a, resource_b, resource_c, resource_d],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

        res = sorted([resource_c, resource_b, resource_a, resource_d],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

        query = 'un1\\index.js' if IS_WINDOWS else 'un1/index.js'

        res = sorted([resource_a, resource_b, resource_c, resource_d],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_c, resource_b, resource_a, resource_d],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_21019_1(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #21019 1.).
        Translated from 'compareFilesByScore - avoid match scattering (bug #21019 1.)' test in fuzzyScorer.test.ts (lines 753-765)
        """
        resource_a = URI.file('app/containers/Services/NetworkData/ServiceDetails/ServiceLoad/index.js')
        resource_b = URI.file('app/containers/Services/NetworkData/ServiceDetails/ServiceDistribution/index.js')
        resource_c = URI.file('app/containers/Services/NetworkData/ServiceDetailTabs/ServiceTabs/StatVideo/index.js')

        from functools import cmp_to_key

        query = 'StatVideoindex'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

    def test_compare_files_by_score_avoid_match_scattering_bug_21019_2(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #21019 2.).
        Translated from 'compareFilesByScore - avoid match scattering (bug #21019 2.)' test in fuzzyScorer.test.ts (lines 767-778)
        """
        resource_a = URI.file('src/build-helper/store/redux.ts')
        resource_b = URI.file('src/repository/store/redux.ts')

        from functools import cmp_to_key

        query = 'reproreduxts'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_26649(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #26649).
        Translated from 'compareFilesByScore - avoid match scattering (bug #26649)' test in fuzzyScorer.test.ts (lines 780-792)
        """
        resource_a = URI.file('photobook/src/components/AddPagesButton/index.js')
        resource_b = URI.file('photobook/src/components/ApprovalPageHeader/index.js')
        resource_c = URI.file('photobook/src/canvasComponents/BookPage/index.js')

        from functools import cmp_to_key

        query = 'bookpageIndex'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_c)

    def test_compare_files_by_score_avoid_match_scattering_bug_33247(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #33247).
        Translated from 'compareFilesByScore - avoid match scattering (bug #33247)' test in fuzzyScorer.test.ts (lines 794-805)
        """
        resource_a = URI.file('ui/src/utils/constants.js')
        resource_b = URI.file('ui/src/ui/Icons/index.js')

        from functools import cmp_to_key

        query = 'ui\\icons' if IS_WINDOWS else 'ui/icons'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_33247_comment(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #33247 comment).
        Translated from 'compareFilesByScore - avoid match scattering (bug #33247 comment)' test in fuzzyScorer.test.ts (lines 807-818)
        """
        resource_a = URI.file('ui/src/components/IDInput/index.js')
        resource_b = URI.file('ui/src/ui/Input/index.js')

        from functools import cmp_to_key

        query = 'ui\\input\\index' if IS_WINDOWS else 'ui/input/index'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_36166(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #36166).
        Translated from 'compareFilesByScore - avoid match scattering (bug #36166)' test in fuzzyScorer.test.ts (lines 820-831)
        """
        resource_a = URI.file('django/contrib/sites/locale/ga/LC_MESSAGES/django.mo')
        resource_b = URI.file('django/core/signals.py')

        from functools import cmp_to_key

        query = 'djancosig'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_32918(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #32918).
        Translated from 'compareFilesByScore - avoid match scattering (bug #32918)' test in fuzzyScorer.test.ts (lines 833-849)
        """
        resource_a = URI.file('adsys/protected/config.php')
        resource_b = URI.file('adsys/protected/framework/smarty/sysplugins/smarty_internal_config.php')
        resource_c = URI.file('duowanVideo/wap/protected/config.php')

        from functools import cmp_to_key

        query = 'protectedconfig.php'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_c)
        self.assertEqual(res[2], resource_b)

        res = sorted([resource_c, resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_c)
        self.assertEqual(res[2], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_14879(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #14879).
        Translated from 'compareFilesByScore - avoid match scattering (bug #14879)' test in fuzzyScorer.test.ts (lines 851-862)
        """
        resource_a = URI.file('pkg/search/gradient/testdata/constraint_attrMatchString.yml')
        resource_b = URI.file('cmd/gradient/main.go')

        from functools import cmp_to_key

        query = 'gradientmain'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_14727_1(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #14727 1).
        Translated from 'compareFilesByScore - avoid match scattering (bug #14727 1)' test in fuzzyScorer.test.ts (lines 864-875)
        """
        resource_a = URI.file('alpha-beta-cappa.txt')
        resource_b = URI.file('abc.txt')

        from functools import cmp_to_key

        query = 'abc'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_14727_2(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #14727 2).
        Translated from 'compareFilesByScore - avoid match scattering (bug #14727 2)' test in fuzzyScorer.test.ts (lines 877-888)
        """
        resource_a = URI.file('xerxes-yak-zubba/index.js')
        resource_b = URI.file('xyz/index.js')

        from functools import cmp_to_key

        query = 'xyz'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_18381(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #18381).
        Translated from 'compareFilesByScore - avoid match scattering (bug #18381)' test in fuzzyScorer.test.ts (lines 890-901)
        """
        resource_a = URI.file('AssymblyInfo.cs')
        resource_b = URI.file('IAsynchronousTask.java')

        from functools import cmp_to_key

        query = 'async'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_35572(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #35572).
        Translated from 'compareFilesByScore - avoid match scattering (bug #35572)' test in fuzzyScorer.test.ts (lines 903-914)
        """
        resource_a = URI.file('static/app/source/angluar/-admin/-organization/-settings/layout/layout.js')
        resource_b = URI.file('static/app/source/angular/-admin/-project/-settings/_settings/settings.js')

        from functools import cmp_to_key

        query = 'partisettings'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_36810(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #36810).
        Translated from 'compareFilesByScore - avoid match scattering (bug #36810)' test in fuzzyScorer.test.ts (lines 916-927)
        """
        resource_a = URI.file('Trilby.TrilbyTV.Web.Portal/Views/Systems/Index.cshtml')
        resource_b = URI.file('Trilby.TrilbyTV.Web.Portal/Areas/Admins/Views/Tips/Index.cshtml')

        from functools import cmp_to_key

        query = 'tipsindex.cshtml'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_prefer_shorter_hit_bug_20546(self):
        """
        Test compareFilesByScore - prefer shorter hit (bug #20546).
        Translated from 'compareFilesByScore - prefer shorter hit (bug #20546)' test in fuzzyScorer.test.ts (lines 929-940)
        """
        resource_a = URI.file('editor/core/components/tests/list-view-spec.js')
        resource_b = URI.file('editor/core/components/list-view.js')

        from functools import cmp_to_key

        query = 'listview'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_avoid_match_scattering_bug_12095(self):
        """
        Test compareFilesByScore - avoid match scattering (bug #12095).
        Translated from 'compareFilesByScore - avoid match scattering (bug #12095)' test in fuzzyScorer.test.ts (lines 942-954)
        """
        resource_a = URI.file('src/vs/workbench/contrib/files/common/explorerViewModel.ts')
        resource_b = URI.file('src/vs/workbench/contrib/files/browser/views/explorerView.ts')
        resource_c = URI.file('src/vs/workbench/contrib/files/browser/views/explorerViewer.ts')

        from functools import cmp_to_key

        query = 'filesexplorerview.ts'

        res = sorted([resource_a, resource_b, resource_c],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_a, resource_c, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_prefer_case_match_bug_96122(self):
        """
        Test compareFilesByScore - prefer case match (bug #96122).
        Translated from 'compareFilesByScore - prefer case match (bug #96122)' test in fuzzyScorer.test.ts (lines 956-967)
        """
        resource_a = URI.file('lists.php')
        resource_b = URI.file('lib/Lists.php')

        from functools import cmp_to_key

        query = 'Lists.php'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)

    def test_compare_files_by_score_prefer_shorter_match_bug_103052_foo_bar(self):
        """
        Test compareFilesByScore - prefer shorter match (bug #103052) - foo bar.
        Translated from 'compareFilesByScore - prefer shorter match (bug #103052) - foo bar' test in fuzzyScorer.test.ts (lines 969-982)
        """
        resource_a = URI.file('app/emails/foo.bar.js')
        resource_b = URI.file('app/emails/other-footer.other-bar.js')

        from functools import cmp_to_key

        for query in ['foo bar', 'foobar']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_prefer_shorter_match_bug_103052_payment_model(self):
        """
        Test compareFilesByScore - prefer shorter match (bug #103052) - payment model.
        Translated from 'compareFilesByScore - prefer shorter match (bug #103052) - payment model' test in fuzzyScorer.test.ts (lines 984-997)
        """
        resource_a = URI.file('app/components/payment/payment.model.js')
        resource_b = URI.file('app/components/online-payments-history/online-payments-history.model.js')

        from functools import cmp_to_key

        for query in ['payment model', 'paymentmodel']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_prefer_shorter_match_bug_103052_color(self):
        """
        Test compareFilesByScore - prefer shorter match (bug #103052) - color.
        Translated from 'compareFilesByScore - prefer shorter match (bug #103052) - color' test in fuzzyScorer.test.ts (lines 999-1012)
        """
        resource_a = URI.file('app/constants/color.js')
        resource_b = URI.file('app/components/model/input/pick-avatar-color.js')

        from functools import cmp_to_key

        for query in ['color js', 'colorjs']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_a)
            self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_prefer_strict_case_prefix(self):
        """
        Test compareFilesByScore - prefer strict case prefix.
        Translated from 'compareFilesByScore - prefer strict case prefix' test in fuzzyScorer.test.ts (lines 1014-1037)
        """
        resource_a = URI.file('app/constants/color.js')
        resource_b = URI.file('app/components/model/input/Color.js')

        from functools import cmp_to_key

        query = 'Color'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        query = 'color'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_prefer_prefix_bug_103052(self):
        """
        Test compareFilesByScore - prefer prefix (bug #103052).
        Translated from 'compareFilesByScore - prefer prefix (bug #103052)' test in fuzzyScorer.test.ts (lines 1039-1052)
        """
        resource_a = URI.file('test/smoke/src/main.ts')
        resource_b = URI.file('src/vs/editor/common/services/semantikTokensProviderStyling.ts')

        from functools import cmp_to_key

        query = 'smoke main.ts'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_a)
        self.assertEqual(res[1], resource_b)

    def test_compare_files_by_score_boost_better_prefix_match_if_multiple_queries_are_used(self):
        """
        Test compareFilesByScore - boost better prefix match if multiple queries are used.
        Translated from 'compareFilesByScore - boost better prefix match if multiple queries are used' test in fuzzyScorer.test.ts (lines 1054-1067)
        """
        resource_a = URI.file('src/vs/workbench/services/host/browser/browserHostService.ts')
        resource_b = URI.file('src/vs/workbench/browser/workbench.ts')

        from functools import cmp_to_key

        for query in ['workbench.ts browser', 'browser workbench.ts', 'browser workbench', 'workbench browser']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_b)
            self.assertEqual(res[1], resource_a)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_b)
            self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_boost_shorter_prefix_match_if_multiple_queries_are_used(self):
        """
        Test compareFilesByScore - boost shorter prefix match if multiple queries are used.
        Translated from 'compareFilesByScore - boost shorter prefix match if multiple queries are used' test in fuzzyScorer.test.ts (lines 1069-1082)
        """
        resource_a = URI.file('src/vs/workbench/node/actions/windowActions.ts')
        resource_b = URI.file('src/vs/workbench/electron-node/window.ts')

        from functools import cmp_to_key

        for query in ['window node', 'window.ts node']:
            res = sorted([resource_a, resource_b],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_b)
            self.assertEqual(res[1], resource_a)

            res = sorted([resource_b, resource_a],
                        key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
            self.assertEqual(res[0], resource_b)
            self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_skip_preference_on_label_match_when_using_path_sep(self):
        """
        Test compareFilesByScore - skip preference on label match when using path sep.
        Translated from 'compareFilesByScore - skip preference on label match when using path sep' test in fuzzyScorer.test.ts (lines 1084-1097)
        """
        resource_a = URI.file('djangosite/ufrela/def.py')
        resource_b = URI.file('djangosite/urls/default.py')

        from functools import cmp_to_key

        query = 'url/def'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_boost_shorter_prefix_match_if_multiple_queries_are_used_99171(self):
        """
        Test compareFilesByScore - boost shorter prefix match if multiple queries are used (#99171).
        Translated from 'compareFilesByScore - boost shorter prefix match if multiple queries are used (#99171)' test in fuzzyScorer.test.ts (lines 1099-1112)
        """
        resource_a = URI.file('mesh_editor_lifetime_job.h')
        resource_b = URI.file('lifetime_job.h')

        from functools import cmp_to_key

        query = 'm life, life m'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_compare_files_by_score_boost_consecutive_matches_in_the_beginning_over_end(self):
        """
        Test compareFilesByScore - boost consecutive matches in the beginning over end.
        Translated from 'compareFilesByScore - boost consecutive matches in the beginning over end' test in fuzzyScorer.test.ts (lines 1114-1127)
        """
        resource_a = URI.file('src/vs/server/node/extensionHostStatusService.ts')
        resource_b = URI.file('src/vs/workbench/browser/parts/notifications/notificationsStatus.ts')

        from functools import cmp_to_key

        query = 'notStatus'

        res = sorted([resource_a, resource_b],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

        res = sorted([resource_b, resource_a],
                    key=cmp_to_key(lambda r1, r2: compare_items_by_score(r1, r2, query, True, ResourceAccessor)))
        self.assertEqual(res[0], resource_b)
        self.assertEqual(res[1], resource_a)

    def test_using_quotes_should_expect_contiguous_matches_match(self):
        """
        Test Using quotes should expect contiguous matches match.
        Translated from 'Using quotes should expect contiguous matches match' test in fuzzyScorer.test.ts (lines 1268-1274)
        """
        # missing the "i" in the query
        score = _do_score('contiguous', '"contguous"')
        self.assertEqual(score[0], 0)

        score = _do_score('contiguous', '"contiguous"')
        self.assertTrue(score[0] > 0)

    def test_using_quotes_should_highlight_contiguous_indexes(self):
        """
        Test Using quotes should highlight contiguous indexes.
        Translated from 'Using quotes should highlight contiguous indexes' test in fuzzyScorer.test.ts (lines 1276-1283)
        """
        score = _do_score('2021-7-26.md', '"26"')
        self.assertEqual(score[0], 14)

        # The indexes of the 2 and 6 of "26"
        self.assertEqual(score[1][0], 7)
        self.assertEqual(score[1][1], 8)
    def test_score_fuzzy(self):
        target = 'HelLo-World'

        scores = []
        scores.append(_do_score(target, 'HelLo-World', True))  # direct case match
        scores.append(_do_score(target, 'hello-world', True))   # direct mix-case match
        scores.append(_do_score(target, 'HW', True))            # direct case prefix (multiple)
        scores.append(_do_score(target, 'hw', True))            # direct mix-case prefix (multiple)
        scores.append(_do_score(target, 'H', True))             # direct case prefix
        scores.append(_do_score(target, 'h', True))             # direct mix-case prefix
        scores.append(_do_score(target, 'W', True))             # direct case word prefix
        scores.append(_do_score(target, 'Ld', True))            # in-string case match (multiple)
        scores.append(_do_score(target, 'ld', True))            # in-string mix-case match (consecutive, avoids scattered hit)
        scores.append(_do_score(target, 'w', True))             # direct mix-case word prefix
        scores.append(_do_score(target, 'L', True))             # in-string case match
        scores.append(_do_score(target, 'l', True))             # in-string mix-case match
        scores.append(_do_score(target, '4', True))             # no match

        # Assert scoring order
        sorted_scores = sorted(scores.copy(), key=lambda x: -x[0])
        assert scores == sorted_scores

        # Assert scoring positions
        # positions = scores[0][1]
        # assert len(positions) == len('HelLo-World')

        # positions = scores[2][1]
        # assert len(positions) == len('HW')
        # assert positions[0] == 0
        # assert positions[1] == 6
    def test_score_no_fuzzy(self):
        target = 'HelLo-World'

        assert _do_score(target, 'HelLo-World', False)[0] > 0
        assert len(_do_score(target, 'HelLo-World', False)[1]) == len('HelLo-World')

        assert _do_score(target, 'hello-world', False)[0] > 0
        assert _do_score(target, 'HW', False)[0] == 0
        assert _do_score(target, 'h', False)[0] > 0
        assert _do_score(target, 'ello', False)[0] > 0
        assert _do_score(target, 'ld', False)[0] > 0
        assert _do_score(target, 'eo', False)[0] == 0

    def test_prepare_query(self):
        """
        Test prepareQuery function with various inputs.
        Translated from TypeScript test case to ensure strict alignment.
        """
        # Basic normalization tests
        self.assertEqual(prepare_query(' f*a ').normalized, 'fa')
        self.assertEqual(prepare_query('model Tester.ts').original, 'model Tester.ts')
        self.assertEqual(prepare_query('model Tester.ts').originalLowercase, 'model Tester.ts'.lower())
        self.assertEqual(prepare_query('model Tester.ts').normalized, 'modelTester.ts')
        self.assertEqual(prepare_query('model Tester.ts').expectContiguousMatch, False)  # doesn't have quotes in it
        self.assertEqual(prepare_query('Model Tester.ts').normalizedLowercase, 'modeltester.ts')
        self.assertEqual(prepare_query('ModelTester.ts').containsPathSeparator, False)
        self.assertEqual(prepare_query('Model' + SEP + 'Tester.ts').containsPathSeparator, True)
        self.assertEqual(prepare_query('"hello"').expectContiguousMatch, True)
        self.assertEqual(prepare_query('"hello"').normalized, 'hello')

        # Test with spaces - multiple query pieces
        query = prepare_query('He*llo World')
        self.assertEqual(query.original, 'He*llo World')
        self.assertEqual(query.normalized, 'HelloWorld')
        self.assertEqual(query.normalizedLowercase, 'HelloWorld'.lower())
        self.assertEqual(len(query.values), 2)
        self.assertEqual(query.values[0].original, 'He*llo')
        self.assertEqual(query.values[0].normalized, 'Hello')
        self.assertEqual(query.values[0].normalizedLowercase, 'Hello'.lower())
        self.assertEqual(query.values[1].original, 'World')
        self.assertEqual(query.values[1].normalized, 'World')
        self.assertEqual(query.values[1].normalizedLowercase, 'World'.lower())

        # Test pieceToQuery restoration
        restored_query = piece_to_query(query.values)
        self.assertEqual(restored_query.original, query.original)
        self.assertEqual(len(restored_query.values), len(query.values))
        self.assertEqual(restored_query.containsPathSeparator, query.containsPathSeparator)

        # Test with spaces that are empty
        query = prepare_query(' Hello   World  	')
        self.assertEqual(query.original, ' Hello   World  \t')
        self.assertEqual(query.originalLowercase, ' Hello   World  \t'.lower())
        self.assertEqual(query.normalized, 'HelloWorld')
        self.assertEqual(query.normalizedLowercase, 'HelloWorld'.lower())
        self.assertEqual(len(query.values), 2)
        self.assertEqual(query.values[0].original, 'Hello')
        self.assertEqual(query.values[0].originalLowercase, 'Hello'.lower())
        self.assertEqual(query.values[0].normalized, 'Hello')
        self.assertEqual(query.values[0].normalizedLowercase, 'Hello'.lower())
        self.assertEqual(query.values[1].original, 'World')
        self.assertEqual(query.values[1].originalLowercase, 'World'.lower())
        self.assertEqual(query.values[1].normalized, 'World')
        self.assertEqual(query.values[1].normalizedLowercase, 'World'.lower())

        # Path related tests - platform specific
        if IS_WINDOWS:
            self.assertEqual(prepare_query('C:\\some\\path').pathNormalized, 'C:\\some\\path')
            self.assertEqual(prepare_query('C:\\some\\path').normalized, 'C:\\some\\path')
            self.assertEqual(prepare_query('C:\\some\\path').containsPathSeparator, True)
            self.assertEqual(prepare_query('C:/some/path').pathNormalized, 'C:\\some\\path')
            self.assertEqual(prepare_query('C:/some/path').normalized, 'C:\\some\\path')
            self.assertEqual(prepare_query('C:/some/path').containsPathSeparator, True)
        else:
            self.assertEqual(prepare_query('/some/path').pathNormalized, '/some/path')
            self.assertEqual(prepare_query('/some/path').normalized, '/some/path')
            self.assertEqual(prepare_query('/some/path').containsPathSeparator, True)
            self.assertEqual(prepare_query('\\some\\path').pathNormalized, '/some/path')
            self.assertEqual(prepare_query('\\some\\path').normalized, '/some/path')
            self.assertEqual(prepare_query('\\some\\path').containsPathSeparator, True)

    def test_prepare_query_edge_cases(self):
        """Test edge cases for prepareQuery function."""
        # Empty string
        query = prepare_query('')
        self.assertEqual(query.original, '')
        self.assertEqual(query.normalized, '')
        self.assertEqual(query.normalizedLowercase, '')
        self.assertEqual(query.expectContiguousMatch, False)
        self.assertEqual(query.containsPathSeparator, False)
        self.assertIsNone(query.values)

        # Only spaces
        query = prepare_query('   ')
        self.assertEqual(query.original, '   ')
        self.assertEqual(query.normalized, '')
        self.assertEqual(query.normalizedLowercase, '')
        self.assertIsNone(query.values)

        # Only wildcards
        query = prepare_query('***')
        self.assertEqual(query.original, '***')
        self.assertEqual(query.normalized, '')
        self.assertEqual(query.normalizedLowercase, '')

        # Quotes with spaces inside
        query = prepare_query('"hello world"')
        self.assertEqual(query.original, '"hello world"')
        self.assertEqual(query.normalized, 'helloworld')
        self.assertEqual(query.normalizedLowercase, 'helloworld')
        self.assertEqual(query.expectContiguousMatch, True)

        # Mixed quotes and non-quotes
        query = prepare_query('"exact" fuzzy')
        self.assertEqual(query.original, '"exact" fuzzy')
        self.assertEqual(query.normalized, 'exactfuzzy')
        self.assertEqual(len(query.values), 2)
        self.assertEqual(query.values[0].original, '"exact"')
        self.assertEqual(query.values[0].normalized, 'exact')
        self.assertEqual(query.values[0].expectContiguousMatch, True)
        self.assertEqual(query.values[1].original, 'fuzzy')
        self.assertEqual(query.values[1].normalized, 'fuzzy')
        self.assertEqual(query.values[1].expectContiguousMatch, False)

    def test_piece_to_query(self):
        """Test pieceToQuery function."""
        # Single piece
        original_query = prepare_query('hello')
        restored = piece_to_query(original_query)
        self.assertEqual(restored.original, 'hello')
        self.assertEqual(restored.normalized, 'hello')

        # Multiple pieces
        original_query = prepare_query('hello world')
        restored = piece_to_query(original_query.values)
        self.assertEqual(restored.original, 'hello world')
        self.assertEqual(len(restored.values), 2)
        self.assertEqual(restored.values[0].original, 'hello')
        self.assertEqual(restored.values[1].original, 'world')


if __name__ == '__main__':
    unittest.main()
