import os
import pickle
import fnmatch
import mimetypes
import numpy as np
from typing import Dict, List, Optional, Any
import lancedb
from pathlib import Path

from llama_index.core import SimpleDirectoryReader
from llama_index.core import Document
from llama_index.core.node_parser import SimpleFileNodeParser
from llama_index.core.schema import BaseNode, NodeWithScore, QueryBundle
from llama_index.core.base.base_retriever import BaseRetriever
from repo_index.index.epic_split import Epic<PERSON>plitter

from dependency_graph import RepoEntitySearcher
from dependency_graph.traverse_graph import is_test_file
from dependency_graph.build_graph import (
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_FILE,
    NODE_TYPE_CLASS,
    NODE_TYPE_FUNCTION,
)

from plugins.location_tools.retriever.embedding import ZeroAgentEmbedding

import warnings
warnings.simplefilter('ignore', FutureWarning)
import logging
logger = logging.getLogger(__name__)

NTYPES = [
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_FILE,
    NODE_TYPE_FUNCTION,
    NODE_TYPE_CLASS,
]


class VectorRetriever(BaseRetriever):
    """Vector-based retriever using LanceDB for storage and cosine similarity for search."""
    
    def __init__(
        self,
        nodes: List[BaseNode],
        similarity_top_k: int = 10,
        db_path: str = "./vector_db",
        table_name: str = "code_vectors",
    ):
        """Initialize the vector retriever.
        
        Args:
            nodes: List of nodes to index
            similarity_top_k: Number of top similar results to return
            db_path: Path to store the LanceDB database
            table_name: Name of the table in LanceDB
        """
        super().__init__()
        self._similarity_top_k = similarity_top_k
        self._embedding_model = ZeroAgentEmbedding()
        self._db_path = db_path
        self._table_name = table_name
        self._nodes = nodes
        self._db = None
        self._table = None
        
        # Initialize database and table
        self._init_database()
        
        # Index all nodes if provided
        if not self._table and nodes:
            logger.info(f"embedding for all nodes")
            self._index_nodes(nodes)

    def _init_database(self):
        """Initialize LanceDB database and table."""
        # Create database directory if it doesn't exist
        os.makedirs(self._db_path, exist_ok=True)
        # Connect to LanceDB
        self._db = lancedb.connect(self._db_path)
        # open table
        if self._table_name in self._db.table_names():
            self._table = self._db.open_table(self._table_name)
        else:
            logger.info(f"'{self._table_name}' is not exists!")

    def _index_nodes(self, nodes: List[BaseNode]):
        """Index nodes by computing embeddings and storing in LanceDB."""
        print(f"Indexing {len(nodes)} nodes...")
        
        # Prepare data for LanceDB
        data = []
        batch_size = 10  # Process in batches to avoid memory issues
        
        for i in range(0, len(nodes), batch_size):
            batch_nodes = nodes[i:i + batch_size]
            batch_texts = [node.get_content() for node in batch_nodes]
            
            # Get embeddings for the batch
            try:
                batch_embeddings = self._embedding_model._get_text_embeddings(batch_texts)
                
                # Handle the case where embeddings might be returned in different formats
                if isinstance(batch_embeddings, list) and len(batch_embeddings) > 0:
                    if isinstance(batch_embeddings[0], list):
                        # Already a list of embeddings
                        embeddings = batch_embeddings
                    else:
                        # Single embedding returned, wrap in list
                        embeddings = [batch_embeddings] * len(batch_texts)
                else:
                    # Fallback: get embeddings one by one
                    embeddings = []
                    for text in batch_texts:
                        emb = self._embedding_model._get_text_embedding(text)
                        embeddings.append(emb)
                
                # Add to data
                for j, (node, embedding) in enumerate(zip(batch_nodes, embeddings)):
                    data.append({
                        "id": node.node_id,
                        "text": node.get_content(),
                        "vector": embedding,
                        "metadata": node.metadata or {},
                        "file_path": node.metadata.get("file_path", "") if node.metadata else "",
                        "file_name": node.metadata.get("file_name", "") if node.metadata else "",
                    })
                    
            except Exception as e:
                print(f"Error processing batch {i//batch_size + 1}: {e}")
                # Skip this batch and continue
                continue
                
            print(f"Processed batch {i//batch_size + 1}/{(len(nodes) + batch_size - 1)//batch_size}")
        
        if not data:
            raise ValueError("No data was successfully processed for indexing")
            
        # Create or replace table in LanceDB
        try:
            self._table = self._db.create_table(self._table_name, data, mode="overwrite")
            print(f"Successfully indexed {len(data)} nodes in LanceDB")
        except Exception as e:
            print(f"Error creating table: {e}")
            raise
    
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes based on vector similarity."""
        if self._table is None:
            raise ValueError("Table not initialized. Please index nodes first.")
        
        query_text = query_bundle.query_str
        # print(f"Searching LanceDB for query: {query_text}")
        # Get query embedding
        try:
            query_embedding = self._embedding_model._get_text_embedding(query_text)
            # print("len(query_embedding):", len(query_embedding))
        except Exception as e:
            print(f"Error getting query embedding: {e}")
            return []
        
        # Search in LanceDB
        try:
            results = (
                self._table.search(query_embedding)
                .limit(self._similarity_top_k)
                .to_list()
            )
        except Exception as e:
            print(f"Error searching in LanceDB: {e}")
            return []
        
        # Convert results to NodeWithScore
        nodes_with_scores = []
        for result in results:
            # Find the original node
            node = None
            # for n in self._nodes:
            #     if n.node_id == result["id"]:
            #         node = n
            #         break
            
            if node is None:
                # Create a new node from stored data
                node = Document(
                    text=result["text"],
                    metadata=result["metadata"]
                )
                node.node_id = result["id"]
            # LanceDB returns distance, convert to similarity score
            # Assuming cosine distance, similarity = 1 - distance
            score = 1.0 - result.get("_distance", 0.0)
            
            nodes_with_scores.append(NodeWithScore(node=node, score=score))

        return nodes_with_scores
    
    @classmethod
    def from_defaults(
        cls,
        nodes: List[BaseNode],
        similarity_top_k: int = 10,
        db_path: str = "./vector_db",
        table_name: str = "code_vectors",
    ) -> "VectorRetriever":
        """Create VectorRetriever with default settings."""
        return cls(
            nodes=nodes,
            similarity_top_k=similarity_top_k,
            db_path=db_path,
            table_name=table_name,
        )

    def persist(self, persist_path: str):
        """Persist the retriever state."""
        persist_dir = Path(persist_path)
        persist_dir.mkdir(parents=True, exist_ok=True)

        # Save configuration
        config = {
            "similarity_top_k": self._similarity_top_k,
            "embedding_url": self._embedding_model.url,
            "db_path": self._db_path,
            "table_name": self._table_name,
        }

        with open(persist_dir / "config.pkl", "wb") as f:
            pickle.dump(config, f)

        # Save nodes
        with open(persist_dir / "nodes.pkl", "wb") as f:
            pickle.dump(self._nodes, f)

    @classmethod
    def from_persist_dir(cls, persist_path: str) -> "VectorRetriever":
        """Load retriever from persisted state."""
        persist_dir = Path(persist_path)

        # Load configuration
        with open(persist_dir / "config.pkl", "rb") as f:
            config = pickle.load(f)

        # Load nodes
        with open(persist_dir / "nodes.pkl", "rb") as f:
            nodes = pickle.load(f)

        # Create retriever (this will also initialize the database)
        retriever = cls(
            nodes=nodes,
            similarity_top_k=config["similarity_top_k"],
            db_path=persist_path,
            table_name=config["table_name"],
        )

        return retriever


def build_code_retriever_from_repo(repo_path,
                                   similarity_top_k=10,
                                   min_chunk_size=100,
                                   chunk_size=500,
                                   max_chunk_size=2000,
                                   hard_token_limit=2000,
                                   max_chunks=200,
                                   persist_path=None,
                                   show_progress=False,
                                   table_name="epic_vectors",
                                   ):
    """Build a vector retriever from a repository."""
    # Only extract file name and type to not trigger unnecessary embedding jobs
    def file_metadata_func(file_path: str) -> Dict:
        file_path = file_path.replace(repo_path, '')
        if file_path.startswith('/'):
            file_path = file_path[1:]

        test_patterns = [
            '**/test/**',
            '**/tests/**',
            '**/test_*.java',
            '**/*_test.java',
        ]
        category = (
            'test'
            if any(fnmatch.fnmatch(file_path, pattern) for pattern in test_patterns)
            else 'implementation'
        )

        return {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_type': mimetypes.guess_type(file_path)[0],
            'category': category,
        }

    reader = SimpleDirectoryReader(
        input_dir=repo_path,
        exclude=[
            '**/test/**',
            '**/tests/**',
            '**/test_*.java',
            '**/*_test.java',
        ],
        file_metadata=file_metadata_func,
        filename_as_id=True,
        required_exts=['.java'],  # TODO: Shouldn't be hardcoded and filtered
        recursive=True,
    )
    docs = reader.load_data()
    splitter = EpicSplitter(
        language='java',
        min_chunk_size=min_chunk_size,
        chunk_size=chunk_size,
        max_chunk_size=max_chunk_size,
        hard_token_limit=hard_token_limit,
        max_chunks=max_chunks,
        repo_path=repo_path,
    )
    prepared_nodes = splitter.get_nodes_from_documents(docs, show_progress=show_progress)
    logger.info(f"len(prepared_nodes): {len(prepared_nodes)}")
    for node in prepared_nodes:
        file_path = node.metadata['file_path']
        if file_path.startswith('/') or file_path.startswith('\\'):
            node.metadata['file_path'] = file_path[1:]

    # Create vector retriever
    table_name = "epic_vectors"
    retriever = VectorRetriever.from_defaults(
        nodes=prepared_nodes,
        similarity_top_k=similarity_top_k,
        db_path=persist_path,
        table_name=table_name,
    )

    if persist_path:
        retriever.persist(persist_path)
    return retriever


def build_retriever_from_persist_dir(path: str):
    """Build retriever from persisted directory."""
    retriever = VectorRetriever.from_persist_dir(path)
    return retriever


def build_code_retriever_from_graph(graph_path: Optional[str] = None,
                                      entity_searcher: Optional[RepoEntitySearcher] = None,
                                      search_scope: str = 'all',
                                      similarity_top_k: int = 10,
                                      db_path: str = "./vector_db",
                                      table_name: str = "code_vectors",
                                      ):
    """Build a vector retriever from dependency graph."""
    assert search_scope in NTYPES or search_scope == 'all'
    assert graph_path or isinstance(entity_searcher, RepoEntitySearcher)

    if graph_path:
        G = pickle.load(open(graph_path, "rb"))
        entity_searcher = RepoEntitySearcher(G)
    else:
        G = entity_searcher.G

    selected_nodes = list()
    count = 0
    for nid in G:
        if is_test_file(nid): continue
        ndata = entity_searcher.get_node_data([nid], True)[0]
        ndata['nid'] = nid
        if ndata['type'] not in ['class', 'method']: continue
        code_content = ndata['code_content']
        code_content = nid + '\n' + '\n'.join(code_content.split('\n')[:50])
        ndata['code_content'] = code_content
        count += 1
        selected_nodes.append(ndata)
    print(count)
    documents = []
    for node in selected_nodes:
        metadata={
            "nid": node['nid'],
            "type": node['type'],
            "start_line": node['start_line'],
            "end_line": node['end_line'],
        }
        documents.append(Document(text=node['code_content'], metadata=metadata))
    # Create vector retriever
    retriever = VectorRetriever.from_defaults(
        nodes=documents,
        similarity_top_k=similarity_top_k,
        db_path=db_path,
        table_name=table_name,
    )

    return retriever
