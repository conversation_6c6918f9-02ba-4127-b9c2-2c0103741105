from typing import Dict, List, Optional, Protocol, TypeVar
from collections import namedtuple
import platform

from plugins.location_tools.retriever.fuzzy_score import NO_MATCH, SEP, IPreparedQueryPiece, IPreparedQuery, matches_prefix, normalize_matches, score_fuzzy


# Platform detection - equivalent to isLinux from platform.ts
IS_LINUX = platform.system() == 'Linux'

# Constants for scoring thresholds - equivalent to TypeScript constants
PATH_IDENTITY_SCORE = 1 << 18           # 262144
LABEL_PREFIX_SCORE_THRESHOLD = 1 << 17  # 131072
LABEL_SCORE_THRESHOLD = 1 << 16         # 65536

T = TypeVar('T')

# 定义接口（Python 中使用 Protocol）
class IItemAccessor(Protocol[T]):
    def get_item_label(self, resource: T) -> str: ...
    def get_item_description(self, resource: T) -> str: ...
    def get_item_path(self, resource: T) -> str: ...


# @dataclass
# class IMatch:
#     start: int
#     end: int


# Create an immutable ItemScore type
ItemScore = namedtuple('ItemScore', ['score', 'label_match', 'description_match'])

FuzzyScorerCache = Dict[str, ItemScore]

# Define the constant with default values
NO_ITEM_SCORE = ItemScore(score=0, label_match=None, description_match=None)




def do_score_item_fuzzy_single(label: str, description: Optional[str], path: Optional[str],
                              query: IPreparedQueryPiece, prefer_label_matches: bool,
                              allow_non_contiguous_matches: bool) -> ItemScore:
    """
    Score a single item (with label, optional description, and optional path) against a single query piece.

    Translated from doScoreItemFuzzySingle function in fuzzyScorer.ts (lines 474-555)

    Args:
        label: The item's label to score against
        description: Optional description of the item
        path: Optional path of the item
        query: The prepared query piece to match
        prefer_label_matches: Whether to prefer matches in the label over description
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches

    Returns:
        ItemScore containing the score and match positions
    """
    # Prefer label matches if told so or we have no description
    if prefer_label_matches or not description:
        label_score, label_positions = score_fuzzy(
            label,
            query.normalized,
            query.normalizedLowercase,
            allow_non_contiguous_matches and not query.expectContiguousMatch
        )

        if label_score:
            # If we have a prefix match on the label, we give a much
            # higher baseScore to elevate these matches over others
            # This ensures that typing a file name wins over results
            # that are present somewhere in the label, but not the
            # beginning.
            label_prefix_match = matches_prefix(query.normalized, label)

            if label_prefix_match:
                base_score = LABEL_PREFIX_SCORE_THRESHOLD

                # We give another boost to labels that are short, e.g. given
                # files "window.ts" and "windowActions.ts" and a query of
                # "window", we want "window.ts" to receive a higher score.
                # As such we compute the percentage the query has within the
                # label and add that to the baseScore.
                prefix_length_boost = round((len(query.normalized) / len(label)) * 100)
                base_score += prefix_length_boost
            else:
                base_score = LABEL_SCORE_THRESHOLD
            return ItemScore(
                score=base_score + label_score,
                label_match=label_prefix_match or create_matches(label_positions),
                description_match=None
            )

    # Finally compute description + label scores if we have a description
    if description:
        description_prefix = description
        if path:
            description_prefix = f"{description}{SEP}"  # assume this is a file path

        description_prefix_length = len(description_prefix)
        description_and_label = f"{description_prefix}{label}"

        label_description_score, label_description_positions = score_fuzzy(
            description_and_label,
            query.normalized,
            query.normalizedLowercase,
            allow_non_contiguous_matches and not query.expectContiguousMatch
        )

        if label_description_score:
            label_description_matches = create_matches(label_description_positions)
            label_match = []
            description_match = []

            # We have to split the matches back onto the label and description portions
            for h in label_description_matches:
                # Match overlaps label and description part, we need to split it up
                if h["start"] < description_prefix_length and h["end"] > description_prefix_length:
                    label_match.append({"start": 0, "end": h["end"] - description_prefix_length})
                    description_match.append({"start": h["start"], "end": description_prefix_length})

                # Match on label part
                elif h["start"] >= description_prefix_length:
                    label_match.append({
                        "start": h["start"] - description_prefix_length,
                        "end": h["end"] - description_prefix_length
                    })

                # Match on description part
                else:
                    description_match.append(h)

            return ItemScore(
                score=label_description_score,
                label_match=label_match,
                description_match=description_match
            )

    return NO_ITEM_SCORE


def do_score_item_fuzzy_multiple(label: str, description: Optional[str], path: Optional[str],
                                query: List[IPreparedQueryPiece], prefer_label_matches: bool,
                                allow_non_contiguous_matches: bool) -> ItemScore:
    """
    Score a single item (with label, optional description, and optional path) against multiple query pieces.
    All query pieces must match for the item to receive a score.

    Translated from doScoreItemFuzzyMultiple function in fuzzyScorer.ts (lines 442-472)

    Args:
        label: The item's label to score against
        description: Optional description of the item
        path: Optional path of the item
        query: List of prepared query pieces that all must match
        prefer_label_matches: Whether to prefer matches in the label over description
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches

    Returns:
        ItemScore containing the total score and combined match positions
    """
    total_score = 0
    total_label_matches = []
    total_description_matches = []

    for query_piece in query:
        result = do_score_item_fuzzy_single(label, description, path, query_piece,
                                          prefer_label_matches, allow_non_contiguous_matches)

        if result.score == NO_MATCH:
            # if a single query value does not match, return with
            # no score entirely, we require all queries to match
            return NO_ITEM_SCORE

        total_score += result.score

        if result.label_match:
            total_label_matches.extend(result.label_match)

        if result.description_match:
            total_description_matches.extend(result.description_match)

    # if we have a score, ensure that the positions are
    # sorted in ascending order and distinct
    return ItemScore(
        score=total_score,
        label_match=normalize_matches(total_label_matches),
        description_match=normalize_matches(total_description_matches)
    )


def create_matches(offsets: Optional[List[int]]) -> List[dict]:
    """
    Create match objects from position offsets.

    Translated from createMatches function in fuzzyScorer.ts (lines 557-574)

    Args:
        offsets: List of character positions where matches occurred

    Returns:
        List of match objects with start/end positions
    """
    ret = []
    if not offsets:
        return ret

    last = None
    for pos in offsets:
        if last and last["end"] == pos:
            last["end"] += 1
        else:
            last = {"start": pos, "end": pos + 1}
            ret.append(last)

    return ret


def get_cache_hash(label: str, description: Optional[str], allow_non_contiguous_matches: bool, query: IPreparedQuery) -> str:
    """
    Generate a cache hash for scoring results based on input parameters.

    Translated from getCacheHash function in fuzzyScorer.ts (lines 383-394)

    Args:
        label: The item's label
        description: Optional description of the item
        allow_non_contiguous_matches: Whether non-contiguous matches are allowed
        query: The prepared query

    Returns:
        String hash for caching
    """
    values = query.values if query.values else [query]

    # Create a simple hash based on the key parameters
    # In Python, we'll use a simpler approach than the TypeScript hash function
    hash_data = {
        'normalized': query.normalized,
        'values': [{'value': v.normalized, 'expectContiguousMatch': v.expectContiguousMatch} for v in values],
        'label': label,
        'description': description,
        'allowNonContiguousMatches': allow_non_contiguous_matches
    }

    # Convert to string and use Python's built-in hash (note: this is not cryptographic)
    return str(hash(str(sorted(hash_data.items()))))


def score_item_fuzzy(item: T, query: IPreparedQuery, allow_non_contiguous_matches: bool,
                    accessor: IItemAccessor[T], cache: FuzzyScorerCache) -> ItemScore:
    """
    Score a single item using fuzzy matching with caching support.
    This is the main public entry point for fuzzy scoring of items.

    Translated from scoreItemFuzzy function in fuzzyScorer.ts (lines 396-423)

    Args:
        item: The item to score (generic type T)
        query: The prepared query to match against
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches
        accessor: Accessor to get label, description, and path from the item
        cache: Cache dictionary to store and retrieve scoring results

    Returns:
        ItemScore containing the score and match positions
    """
    if not item or not query.normalized:
        return NO_ITEM_SCORE  # we need an item and query to score on at least

    label = accessor.get_item_label(item)
    if not label:
        return NO_ITEM_SCORE  # we need a label at least

    description = accessor.get_item_description(item)

    # in order to speed up scoring, we cache the score with a unique hash based on:
    # - label
    # - description (if provided)
    # - whether non-contiguous matching is enabled or not
    # - hash of the query (normalized) values
    cache_hash = get_cache_hash(label, description, allow_non_contiguous_matches, query)
    cached = cache.get(cache_hash)
    if cached:
        return cached

    item_score = do_score_item_fuzzy(label, description, accessor.get_item_path(item), query, allow_non_contiguous_matches)
    cache[cache_hash] = item_score

    return item_score


def do_score_item_fuzzy(label: str, description: Optional[str], path: Optional[str],
                       query: IPreparedQuery, allow_non_contiguous_matches: bool) -> ItemScore:
    """
    Score a single item (with label, optional description, and optional path) against a prepared query.
    This is the main entry point for fuzzy scoring of items.

    Translated from doScoreItemFuzzy function in fuzzyScorer.ts (lines 425-440)

    Args:
        label: The item's label to score against
        description: Optional description of the item
        path: Optional path of the item
        query: The prepared query to match against
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches

    Returns:
        ItemScore containing the score and match positions
    """
    prefer_label_matches = not path or not query.containsPathSeparator

    # Treat identity matches on full path highest
    if path and (IS_LINUX and query.pathNormalized == path or
                 not IS_LINUX and equals_ignore_case(query.pathNormalized, path)):
        return ItemScore(
            score=PATH_IDENTITY_SCORE,
            label_match=[{"start": 0, "end": len(label)}],
            description_match=[{"start": 0, "end": len(description)}] if description else None
        )

    # Score: multiple inputs
    if query.values and len(query.values) > 1:
        return do_score_item_fuzzy_multiple(label, description, path, query.values,
                                          prefer_label_matches, allow_non_contiguous_matches)

    # Score: single input
    return do_score_item_fuzzy_single(label, description, path, query,
                                    prefer_label_matches, allow_non_contiguous_matches)


def equals_ignore_case(a: str, b: str) -> bool:
    """
    Check if two strings are equal ignoring case.
    Equivalent to equalsIgnoreCase function in strings.ts

    Args:
        a: First string to compare
        b: Second string to compare

    Returns:
        True if strings are equal ignoring case
    """
    return a.lower() == b.lower()
