"""
Compare items by fuzzy score functionality for VS Code-like search and matching.
Translated from TypeScript to Python while preserving original logic.
"""

from typing import List, Optional, TypeVar
import locale

from plugins.location_tools.retriever.fuzzy_score import IPreparedQuery
from plugins.location_tools.retriever.fuzzy_score_item import (
    IItemAccessor, ItemScore, FuzzyScorerCache, score_item_fuzzy,
    PATH_IDENTITY_SCORE, LABEL_PREFIX_SCORE_THRESHOLD, LABEL_SCORE_THRESHOLD
)

T = TypeVar('T')


def compare_items_by_fuzzy_score(item_a: T, item_b: T, query: IPreparedQuery,
                                allow_non_contiguous_matches: bool, accessor: IItemAccessor[T],
                                cache: FuzzyScorerCache) -> int:
    """
    Compare two items by their fuzzy scores for sorting purposes.
    This is the main comparison function for fuzzy matching results.

    Translated from compareItemsByFuzzyScore function in fuzzyScorer.ts (lines 623-683)

    Args:
        item_a: First item to compare
        item_b: Second item to compare
        query: The prepared query to match against
        allow_non_contiguous_matches: Whether to allow non-contiguous character matches
        accessor: Accessor to get label, description, and path from items
        cache: Cache dictionary to store and retrieve scoring results

    Returns:
        Comparison result: -1 if item_a should come before item_b,
                          1 if item_b should come before item_a,
                          0 if they are equal
    """
    item_score_a = score_item_fuzzy(item_a, query, allow_non_contiguous_matches, accessor, cache)
    item_score_b = score_item_fuzzy(item_b, query, allow_non_contiguous_matches, accessor, cache)

    score_a = item_score_a.score
    score_b = item_score_b.score

    # 1.) identity matches have highest score
    if score_a == PATH_IDENTITY_SCORE or score_b == PATH_IDENTITY_SCORE:
        if score_a != score_b:
            return -1 if score_a == PATH_IDENTITY_SCORE else 1

    # 2.) matches on label are considered higher compared to label+description matches
    if score_a > LABEL_SCORE_THRESHOLD or score_b > LABEL_SCORE_THRESHOLD:
        if score_a != score_b:
            return -1 if score_a > score_b else 1

        # prefer more compact matches over longer in label (unless this is a prefix match where
        # longer prefix matches are actually preferred)
        if score_a < LABEL_PREFIX_SCORE_THRESHOLD and score_b < LABEL_PREFIX_SCORE_THRESHOLD:
            compared_by_match_length = compare_by_match_length(item_score_a.label_match, item_score_b.label_match)
            if compared_by_match_length != 0:
                return compared_by_match_length

        # prefer shorter labels over longer labels
        label_a = accessor.get_item_label(item_a) or ''
        label_b = accessor.get_item_label(item_b) or ''
        if len(label_a) != len(label_b):
            return len(label_a) - len(label_b)

    # 3.) compare by score in label+description
    if score_a != score_b:
        return -1 if score_a > score_b else 1

    # 4.) scores are identical: prefer matches in label over non-label matches
    item_a_has_label_matches = isinstance(item_score_a.label_match, list) and len(item_score_a.label_match) > 0
    item_b_has_label_matches = isinstance(item_score_b.label_match, list) and len(item_score_b.label_match) > 0
    if item_a_has_label_matches and not item_b_has_label_matches:
        return -1
    elif item_b_has_label_matches and not item_a_has_label_matches:
        return 1

    # 5.) scores are identical: prefer more compact matches (label and description)
    item_a_match_distance = compute_label_and_description_match_distance(item_a, item_score_a, accessor)
    item_b_match_distance = compute_label_and_description_match_distance(item_b, item_score_b, accessor)
    if item_a_match_distance and item_b_match_distance and item_a_match_distance != item_b_match_distance:
        return -1 if item_b_match_distance > item_a_match_distance else 1

    # 6.) scores are identical: start to use the fallback compare
    return fallback_compare(item_a, item_b, query, accessor)


def compute_label_and_description_match_distance(item: T, score: ItemScore, accessor: IItemAccessor[T]) -> int:
    """
    Compute the distance between the first and last match positions across label and description.
    Used to prefer more compact matches when scores are identical.

    Translated from computeLabelAndDescriptionMatchDistance function in fuzzyScorer.ts (lines 685-718)

    Args:
        item: The item being scored
        score: The ItemScore containing match information
        accessor: Accessor to get item properties

    Returns:
        Distance between first and last match positions, or 0 if no matches
    """
    match_start = -1
    match_end = -1

    # If we have description matches, the start is first of description match
    if score.description_match and len(score.description_match) > 0:
        match_start = score.description_match[0]["start"]

    # Otherwise, the start is the first label match
    elif score.label_match and len(score.label_match) > 0:
        match_start = score.label_match[0]["start"]

    # If we have label match, the end is the last label match
    # If we had a description match, we add the length of the description
    # as offset to the end to indicate this.
    if score.label_match and len(score.label_match) > 0:
        match_end = score.label_match[-1]["end"]
        if score.description_match and len(score.description_match) > 0:
            item_description = accessor.get_item_description(item)
            if item_description:
                match_end += len(item_description)

    # If we have just a description match, the end is the last description match
    elif score.description_match and len(score.description_match) > 0:
        match_end = score.description_match[-1]["end"]

    return match_end - match_start


def compare_by_match_length(matches_a: Optional[List[dict]], matches_b: Optional[List[dict]]) -> int:
    """
    Compare two sets of matches by their total length (distance from first to last match).
    Prefers shorter match lengths for more compact results.

    Translated from compareByMatchLength function in fuzzyScorer.ts (lines 720-745)

    Args:
        matches_a: First set of match objects
        matches_b: Second set of match objects

    Returns:
        Comparison result: -1 if matches_a is more compact,
                          1 if matches_b is more compact,
                          0 if they are equal
    """
    # Handle cases where matches are not provided or empty
    if (not matches_a and not matches_b) or \
       ((not matches_a or len(matches_a) == 0) and (not matches_b or len(matches_b) == 0)):
        return 0  # make sure to not cause bad comparing when matches are not provided

    if not matches_b or len(matches_b) == 0:
        return -1

    if not matches_a or len(matches_a) == 0:
        return 1

    # Compute match length of A (first to last match)
    match_start_a = matches_a[0]["start"]
    match_end_a = matches_a[-1]["end"]
    match_length_a = match_end_a - match_start_a

    # Compute match length of B (first to last match)
    match_start_b = matches_b[0]["start"]
    match_end_b = matches_b[-1]["end"]
    match_length_b = match_end_b - match_start_b

    # Prefer shorter match length
    if match_length_a == match_length_b:
        return 0
    else:
        return 1 if match_length_b < match_length_a else -1


def fallback_compare(item_a: T, item_b: T, query: IPreparedQuery, accessor: IItemAccessor[T]) -> int:
    """
    Fallback comparison when fuzzy scores are identical.
    Compares by various criteria in order of preference.

    Translated from fallbackCompare function in fuzzyScorer.ts (lines 747-790)

    Args:
        item_a: First item to compare
        item_b: Second item to compare
        query: The prepared query
        accessor: Accessor to get item properties

    Returns:
        Comparison result: -1, 0, or 1
    """
    # check for label + description length and prefer shorter
    label_a = accessor.get_item_label(item_a) or ''
    label_b = accessor.get_item_label(item_b) or ''

    description_a = accessor.get_item_description(item_a)
    description_b = accessor.get_item_description(item_b)

    label_description_a_length = len(label_a) + (len(description_a) if description_a else 0)
    label_description_b_length = len(label_b) + (len(description_b) if description_b else 0)

    if label_description_a_length != label_description_b_length:
        return label_description_a_length - label_description_b_length

    # check for path length and prefer shorter
    path_a = accessor.get_item_path(item_a)
    path_b = accessor.get_item_path(item_b)

    if path_a and path_b and len(path_a) != len(path_b):
        return len(path_a) - len(path_b)

    # 7.) finally we have equal scores and equal length, we fallback to comparer

    # compare by label
    if label_a != label_b:
        return compare_anything(label_a, label_b, query.normalized)

    # compare by description
    if description_a and description_b and description_a != description_b:
        return compare_anything(description_a, description_b, query.normalized)

    # compare by path
    if path_a and path_b and path_a != path_b:
        return compare_anything(path_a, path_b, query.normalized)

    # equal
    return 0


def compare_anything(one: str, other: str, look_for: str) -> int:
    """
    Compare two strings with special handling for prefix/suffix matches and file names.
    This is a comprehensive string comparison function for fuzzy matching contexts.

    Translated from compareAnything function in comparers.ts (lines 306-331)

    Args:
        one: First string to compare
        other: Second string to compare
        look_for: The search term to consider for prefix/suffix matching

    Returns:
        Comparison result: -1, 0, or 1
    """
    element_a_name = one.lower()
    element_b_name = other.lower()

    # Sort prefix matches over non prefix matches
    prefix_compare = compare_by_prefix(one, other, look_for)
    if prefix_compare != 0:
        return prefix_compare

    # Sort suffix matches over non suffix matches
    element_a_suffix_match = element_a_name.endswith(look_for)
    element_b_suffix_match = element_b_name.endswith(look_for)
    if element_a_suffix_match != element_b_suffix_match:
        return -1 if element_a_suffix_match else 1

    # Understand file names
    r = compare_file_names(element_a_name, element_b_name)
    if r != 0:
        return r

    # Compare by name
    try:
        return locale.strcoll(element_a_name, element_b_name)
    except:
        # Fallback to simple string comparison if locale is not available
        if element_a_name == element_b_name:
            return 0
        else:
            return -1 if element_a_name < element_b_name else 1


def compare_by_prefix(one: str, other: str, look_for: str) -> int:
    """
    Compare two strings by prefix matching, preferring prefix matches and shorter strings.

    Translated from compareByPrefix function in comparers.ts (lines 333-356)

    Args:
        one: First string to compare
        other: Second string to compare
        look_for: The prefix to look for

    Returns:
        Comparison result: -1, 0, or 1
    """
    element_a_name = one.lower()
    element_b_name = other.lower()

    # Sort prefix matches over non prefix matches
    element_a_prefix_match = element_a_name.startswith(look_for)
    element_b_prefix_match = element_b_name.startswith(look_for)
    if element_a_prefix_match != element_b_prefix_match:
        return -1 if element_a_prefix_match else 1

    # Same prefix: Sort shorter matches to the top to have those on top that match more precisely
    elif element_a_prefix_match and element_b_prefix_match:
        if len(element_a_name) < len(element_b_name):
            return -1

        if len(element_a_name) > len(element_b_name):
            return 1

    return 0


def compare_file_names(one: Optional[str], other: Optional[str], case_sensitive: bool = False) -> int:
    """
    Compare filenames without distinguishing the name from the extension.
    Uses locale-aware comparison with numeric sorting.

    Translated from compareFileNames function in comparers.ts (lines 40-51)

    Args:
        one: First filename to compare
        other: Second filename to compare
        case_sensitive: Whether comparison should be case sensitive

    Returns:
        Comparison result: -1, 0, or 1
    """
    a = one or ''
    b = other or ''

    # Apply case sensitivity if needed
    if not case_sensitive:
        a = a.lower()
        b = b.lower()

    # Use locale-aware comparison
    # Note: Python's locale.strcoll doesn't have the same numeric option as Intl.Collator
    # For a more accurate translation, you might want to use a library like PyICU
    # For now, we'll use a simple approach
    try:
        result = locale.strcoll(a, b)
    except:
        # Fallback to simple string comparison if locale is not available
        if a == b:
            result = 0
        else:
            result = -1 if a < b else 1

    # Simple disambiguation for cases where locale comparison returns 0 but strings differ
    if result == 0 and a != b:
        return -1 if a < b else 1

    return result
