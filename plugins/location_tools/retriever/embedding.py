# Started by AICoder, pid:n432esf3a4f0656141b60b82001e59676395dee8 
from typing import List
import json
import requests

embedding_url = "http://10.55.87.68:30804/zero-agents-embed/v1/embeddings"

class ZeroAgentEmbedding():
    url: str = ""

    def __init__(self) -> None:
        self.url = "http://10.55.87.68:30804/zero-agents-embed/v1/embeddings"

    @classmethod
    def class_name(cls) -> str:
        return "ZeroAgentEmbedding"

    def get_general_text_embedding(self, text: str):
        """Get ZeroAgent embeddings."""
        headers = {"User-Agent": "Test Client"}
        embed_pload = {
            'input': text
        }
        response = requests.post(
            self.url, headers=headers, json=embed_pload, timeout=300)
        data = json.loads(response.text)
        embedding = data.get("data")
        return embedding

    def _get_text_embedding(self, text: str) -> List[float]:
        """Get text embedding."""
        return self.get_general_text_embedding(text)

    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get text embeddings."""
        return self.get_general_text_embedding(texts)

import time
def runEmbedding():
    url = "http://10.55.87.68:30804/zero-agents-embed/v1/embeddings"
    text = "return self._get_query_embedding(query)"
    texts = ["return self._get_query_embedding(query)", "_get_text_embeddings(self, texts: List[str]) -> List[List[float]]:"]
    embedding = ZeroAgentEmbedding(url = url)
    start = time.time()
    result = embedding._get_text_embeddings(texts)
    end = time.time()
    print(len(result))
    for res in result:
        print(res)
        print(len(res))
    print("time:", end - start)

if __name__ == '__main__':
    runEmbedding()