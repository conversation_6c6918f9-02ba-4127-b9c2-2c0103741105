"""
Fuzzy scoring functionality for VS Code-like search and matching.
Translated from TypeScript to Python while preserving original logic.
"""

import platform
import re
from typing import List, Optional, Tuple, Union
from dataclasses import dataclass

# Type aliases - equivalent to TypeScript types
FuzzyScore = Tuple[int, List[int]]  # [score, match_positions]

# Platform detection - equivalent to isWindows from platform.ts
IS_WINDOWS = platform.system() == 'Windows'

# Path separator - equivalent to sep from path.ts
SEP = '\\' if IS_WINDOWS else '/'

# Multiple query values separator - equivalent to MULTIPLE_QUERY_VALUES_SEPARATOR
MULTIPLE_QUERY_VALUES_SEPARATOR = ' '

# Debug flags - equivalent to DEBUG constants in TypeScript
# DEBUG = True
# DEBUG_MATRIX = False

# Constants - equivalent to TypeScript constants
NO_MATCH = 0
NO_SCORE = (NO_MATCH, [])

# Character codes - equivalent to CharCode enum from charCode.ts
class CharCode:
    """Character codes for common characters, equivalent to TypeScript CharCode enum."""
    SLASH = 47          # '/'
    BACKSLASH = 92      # '\'
    UNDERLINE = 95      # '_'
    DASH = 45           # '-'
    PERIOD = 46         # '.'
    SPACE = 32          # ' '
    SINGLE_QUOTE = 39   # "'"
    DOUBLE_QUOTE = 34   # '"'
    COLON = 58          # ':'


@dataclass
class IPreparedQueryPiece:
    """
    Represents a single piece of a prepared query.
    Equivalent to IPreparedQueryPiece interface in TypeScript.
    """
    original: str
    originalLowercase: str
    pathNormalized: str
    normalized: str
    normalizedLowercase: str
    expectContiguousMatch: bool


@dataclass
class IPreparedQuery(IPreparedQueryPiece):
    """
    Represents a complete prepared query that may contain multiple pieces.
    Equivalent to IPreparedQuery interface in TypeScript.
    """
    values: Optional[List[IPreparedQueryPiece]]
    containsPathSeparator: bool


def strip_wildcards(pattern: str) -> str:
    """
    Remove wildcard characters (*) from a pattern.
    Equivalent to stripWildcards from strings.ts

    Args:
        pattern: The pattern string to process

    Returns:
        Pattern with wildcards removed
    """
    return pattern.replace('*', '')


def query_expects_exact_match(query: str) -> bool:
    """
    Check if a query is wrapped in quotes, indicating exact match is expected.
    Equivalent to queryExpectsExactMatch function in TypeScript.

    Args:
        query: The query string to check

    Returns:
        True if query expects exact match (wrapped in quotes)
    """
    return query.startswith('"') and query.endswith('"')


def score_separator_at_pos(char_code: int) -> int:
    """
    Score separator characters at a given position for fuzzy matching.
    Path separators get higher scores than other separators.

    Translated from scoreSeparatorAtPos function in fuzzyScorer.ts

    Args:
        char_code: The character code (ASCII value) to score

    Returns:
        Score value: 5 for path separators, 4 for other separators, 0 for non-separators
    """
    if char_code == CharCode.SLASH or char_code == CharCode.BACKSLASH:
        return 5  # prefer path separators...
    elif char_code in (CharCode.UNDERLINE, CharCode.DASH, CharCode.PERIOD,
                       CharCode.SPACE, CharCode.SINGLE_QUOTE, CharCode.DOUBLE_QUOTE,
                       CharCode.COLON):
        return 4  # ...over other separators
    else:
        return 0


def consider_as_equal(a: str, b: str) -> bool:
    """
    Check if two characters should be considered equal for fuzzy matching.
    Special case: path separators are considered equal regardless of platform.

    Translated from considerAsEqual function in fuzzyScorer.ts

    Args:
        a: First character to compare
        b: Second character to compare

    Returns:
        True if characters should be considered equal
    """
    if a == b:
        return True

    # Special case path separators: ignore platform differences
    if a == '/' or a == '\\':
        return b == '/' or b == '\\'

    return False


def is_upper(char_code: int) -> bool:
    """
    Check if a character code represents an uppercase letter.

    Translated from isUpper function in filters.ts

    Args:
        char_code: The character code to check

    Returns:
        True if the character is uppercase (A-Z)
    """
    return 65 <= char_code <= 90  # CharCode.A to CharCode.Z


def compute_char_score(query_char_at_index: str, query_lower_char_at_index: str,
                      target: str, target_lower: str, target_index: int,
                      matches_sequence_length: int) -> int:
    """
    Compute the score for a character match in fuzzy scoring.

    Translated from computeCharScore function in fuzzyScorer.ts

    Args:
        query_char_at_index: The original query character at current index
        query_lower_char_at_index: The lowercase query character at current index
        target: The target string being matched against
        target_lower: The lowercase target string
        target_index: The current index in the target string
        matches_sequence_length: Length of consecutive matches so far

    Returns:
        Score for this character match (0 if no match)
    """
    score = 0

    if not consider_as_equal(query_lower_char_at_index, target_lower[target_index]):
        return score  # no match of characters

    # if DEBUG:
    #     print(f"Found a match of char: {query_lower_char_at_index} at index {target_index}")

    # Character match bonus
    score += 1

    # if DEBUG:
    #     print("Character match bonus: +1")

    # Consecutive match bonus: sequences up to 3 get the full bonus (6)
    # and the remainder gets half the bonus (3). This helps reduce the
    # overall boost for long sequence matches.
    if matches_sequence_length > 0:
        score += (min(matches_sequence_length, 3) * 6) + (max(0, matches_sequence_length - 3) * 3)

        # if DEBUG:
        #     print(f"Consecutive match bonus: +{matches_sequence_length * 5}")

    # Same case bonus
    if query_char_at_index == target[target_index]:
        score += 1

        # if DEBUG:
        #     print("Same case bonus: +1")

    # Start of word bonus
    if target_index == 0:
        score += 8

        # if DEBUG:
        #     print("Start of word bonus: +8")
    else:
        # After separator bonus
        separator_bonus = score_separator_at_pos(ord(target[target_index - 1]))
        if separator_bonus:
            score += separator_bonus

            # if DEBUG:
            #     print(f"After separator bonus: +{separator_bonus}")
        # Inside word upper case bonus (camel case). We only give this bonus if we're not in a contiguous sequence.
        # For example:
        # NPE => NullPointerException = boost
        # HTTP => HTTP = not boost
        elif is_upper(ord(target[target_index])) and matches_sequence_length == 0:
            score += 2

            # if DEBUG:
            #     print("Inside word upper case bonus: +2")

    # if DEBUG:
    #     print(f"Total score: {score}")

    return score


def score_fuzzy(target: str, query: str, query_lower: str, allow_non_contiguous_matches: bool) -> FuzzyScore:
    """
    Score a target string against a query using fuzzy matching.

    Translated from scoreFuzzy function in fuzzyScorer.ts

    Args:
        target: The target string to match against
        query: The query string to match
        query_lower: The lowercase version of the query
        allow_non_contiguous_matches: Whether to allow non-contiguous matches

    Returns:
        FuzzyScore tuple containing (score, match_positions)
    """
    if not target or not query:
        return NO_SCORE  # return early if target or query are undefined

    target_length = len(target)
    query_length = len(query)

    if target_length < query_length:
        return NO_SCORE  # impossible for query to be contained in target

    # if DEBUG:
    #     print(f"Target: {target}, Query: {query}")

    target_lower = target.lower()
    res = do_score_fuzzy(query, query_lower, query_length, target, target_lower, target_length, allow_non_contiguous_matches)

    # if DEBUG:
    #     print(f"Final Score: {res[0]}")

    return res


def do_score_fuzzy(query: str, query_lower: str, query_length: int, target: str,
                  target_lower: str, target_length: int, allow_non_contiguous_matches: bool) -> FuzzyScore:
    """
    Internal function that performs the actual fuzzy scoring using dynamic programming.

    Translated from doScoreFuzzy function in fuzzyScorer.ts

    Args:
        query: The query string to match
        query_lower: The lowercase version of the query
        query_length: Length of the query
        target: The target string to match against
        target_lower: The lowercase version of the target
        target_length: Length of the target
        allow_non_contiguous_matches: Whether to allow non-contiguous matches

    Returns:
        FuzzyScore tuple containing (score, match_positions)
    """
    scores = []
    matches = []

    #
    # Build Scorer Matrix:
    #
    # The matrix is composed of query q and target t. For each index we score
    # q[i] with t[i] and compare that with the previous score. If the score is
    # equal or larger, we keep the match. In addition to the score, we also keep
    # the length of the consecutive matches to use as boost for the score.
    #
    #      t   a   r   g   e   t
    #  q
    #  u
    #  e
    #  r
    #  y
    #
    for query_index in range(query_length):
        query_index_offset = query_index * target_length
        query_index_previous_offset = query_index_offset - target_length

        query_index_gt_null = query_index > 0

        query_char_at_index = query[query_index]
        query_lower_char_at_index = query_lower[query_index]

        for target_index in range(target_length):
            target_index_gt_null = target_index > 0

            current_index = query_index_offset + target_index
            left_index = current_index - 1
            diag_index = query_index_previous_offset + target_index - 1

            left_score = scores[left_index] if target_index_gt_null else 0
            diag_score = scores[diag_index] if query_index_gt_null and target_index_gt_null else 0

            matches_sequence_length = matches[diag_index] if query_index_gt_null and target_index_gt_null else 0

            # If we are not matching on the first query character any more, we only produce a
            # score if we had a score previously for the last query index (by looking at the diagScore).
            # This makes sure that the query always matches in sequence on the target. For example
            # given a target of "ede" and a query of "de", we would otherwise produce a wrong high score
            # for query[1] ("e") matching on target[0] ("e") because of the "beginning of word" boost.
            if not diag_score and query_index_gt_null:
                score = 0
            else:
                score = compute_char_score(query_char_at_index, query_lower_char_at_index, target, target_lower, target_index, matches_sequence_length)

            # We have a score and its equal or larger than the left score
            # Match: sequence continues growing from previous diag value
            # Score: increases by diag score value
            is_valid_score = score and diag_score + score >= left_score
            if is_valid_score and (
                # We don't need to check if it's contiguous if we allow non-contiguous matches
                allow_non_contiguous_matches or
                # We must be looking for a contiguous match.
                # Looking at an index higher than 0 in the query means we must have already
                # found out this is contiguous otherwise there wouldn't have been a score
                query_index_gt_null or
                # lastly check if the query is completely contiguous at this index in the target
                target_lower.startswith(query_lower, target_index)
            ):
                matches.append(matches_sequence_length + 1)
                scores.append(diag_score + score)
            else:
                # We either have no score or the score is lower than the left score
                # Match: reset to 0
                # Score: pick up from left hand side
                matches.append(NO_MATCH)
                scores.append(left_score)

    # Restore Positions (starting from bottom right of matrix)
    positions = []
    query_index = query_length - 1
    target_index = target_length - 1
    while query_index >= 0 and target_index >= 0:
        current_index = query_index * target_length + target_index
        match = matches[current_index]
        if match == NO_MATCH:
            target_index -= 1  # go left
        else:
            positions.append(target_index)
            # go up and left
            query_index -= 1
            target_index -= 1

    # Print matrix
    # if DEBUG_MATRIX:
    #     print_matrix(query, target, matches, scores)

    return (scores[query_length * target_length - 1], list(reversed(positions)))


# def print_matrix(query: str, target: str, matches: List[int], scores: List[int]) -> None:
#     """
#     Print the scoring matrix for debugging purposes.
#
#     Translated from printMatrix function in fuzzyScorer.ts
#     """
#     print('\t' + '\t'.join(target))
#     for query_index in range(len(query)):
#         line = query[query_index] + '\t'
#         for target_index in range(len(target)):
#             current_index = query_index * len(target) + target_index
#             line += f'M{matches[current_index]}/S{scores[current_index]}\t'
#         print(line)


def normalize_query(original: str) -> Tuple[str, str, str]:
    """
    Helper function to prepare a search value for scoring by removing unwanted characters
    and normalizing path separators based on platform.

    Translated from normalizeQuery function in fuzzyScorer.ts (lines 895-911)

    Args:
        original: The original query string to normalize

    Returns:
        Tuple containing (pathNormalized, normalized, normalizedLowercase):
        - pathNormalized: Query with path separators normalized for platform
        - normalized: Query with wildcards and quotes/spaces removed
        - normalizedLowercase: Lowercase version of normalized query
    """
    # Normalize path separators based on platform
    if IS_WINDOWS:
        # Help Windows users to search for paths when using slash
        path_normalized = original.replace('/', SEP)
    else:
        # Help macOS/Linux users to search for paths when using backslash
        path_normalized = original.replace('\\', SEP)

    # Remove quotes (used for exact match search) and wildcards, then remove spaces and quotes
    normalized = strip_wildcards(path_normalized)
    normalized = re.sub(r'\s|"', '', normalized)

    return (path_normalized, normalized, normalized.lower())


def prepare_query(original: str) -> IPreparedQuery:
    """
    Helper function to prepare a search value for scoring by removing unwanted characters
    and allowing to score on multiple pieces separated by whitespace character.

    Translated from prepareQuery function in fuzzyScorer.ts (lines 853-891)

    Args:
        original: The original query string to prepare

    Returns:
        IPreparedQuery object containing normalized query data
    """
    if not isinstance(original, str):
        original = ''

    original_lowercase = original.lower()
    path_normalized, normalized, normalized_lowercase = normalize_query(original)

    contains_path_separator = SEP in path_normalized
    expect_exact_match = query_expects_exact_match(original)

    values: Optional[List[IPreparedQueryPiece]] = None

    # Split by spaces to handle multiple query pieces
    original_split = original.split(MULTIPLE_QUERY_VALUES_SEPARATOR)
    if len(original_split) > 1:
        for original_piece in original_split:
            expect_exact_match_piece = query_expects_exact_match(original_piece)
            path_normalized_piece, normalized_piece, normalized_lowercase_piece = normalize_query(original_piece)

            if normalized_piece:  # Only add non-empty normalized pieces
                if values is None:
                    values = []

                values.append(IPreparedQueryPiece(
                    original=original_piece,
                    originalLowercase=original_piece.lower(),
                    pathNormalized=path_normalized_piece,
                    normalized=normalized_piece,
                    normalizedLowercase=normalized_lowercase_piece,
                    expectContiguousMatch=expect_exact_match_piece
                ))

    return IPreparedQuery(
        original=original,
        originalLowercase=original_lowercase,
        pathNormalized=path_normalized,
        normalized=normalized,
        normalizedLowercase=normalized_lowercase,
        expectContiguousMatch=expect_exact_match,
        values=values,
        containsPathSeparator=contains_path_separator
    )


def piece_to_query(arg: Union[IPreparedQueryPiece, List[IPreparedQueryPiece]]) -> IPreparedQuery:
    """
    Convert query piece(s) back to a prepared query.
    Equivalent to pieceToQuery function in TypeScript.

    Args:
        arg: Either a single IPreparedQueryPiece or a list of IPreparedQueryPiece

    Returns:
        IPreparedQuery object
    """
    if isinstance(arg, list):
        original_parts = [piece.original for piece in arg]
        return prepare_query(MULTIPLE_QUERY_VALUES_SEPARATOR.join(original_parts))
    else:
        return prepare_query(arg.original)



def matches_prefix(query: str, target: str) -> Optional[List[dict]]:
    """
    Check if query matches the prefix of target (case-insensitive).

    Translated from matchesPrefix function in filters.ts

    Args:
        query: The query string to match
        target: The target string to match against

    Returns:
        List of match objects with start/end positions if match found, None otherwise
    """
    if not target or len(target) < len(query):
        return None

    # Case-insensitive prefix match
    if not target.lower().startswith(query.lower()):
        return None

    return [{"start": 0, "end": len(query)}] if len(query) > 0 else []





def normalize_matches(matches: List[dict]) -> List[dict]:
    """
    Normalize matches by sorting and merging overlapping ranges.

    Translated from normalizeMatches function in fuzzyScorer.ts (lines 576-604)

    Args:
        matches: List of match objects to normalize

    Returns:
        List of normalized match objects
    """
    # Sort matches by start position
    sorted_matches = sorted(matches, key=lambda match: match["start"])

    # Merge overlapping matches
    normalized_matches = []
    current_match = None

    for match in sorted_matches:
        # If we have no current match or the matches don't overlap,
        # take it as is and remember it for future merging
        if not current_match or not match_overlaps(current_match, match):
            current_match = match
            normalized_matches.append(match)
        else:
            # Merge the matches
            current_match["start"] = min(current_match["start"], match["start"])
            current_match["end"] = max(current_match["end"], match["end"])

    return normalized_matches


def match_overlaps(match_a: dict, match_b: dict) -> bool:
    """
    Check if two match ranges overlap.

    Translated from matchOverlaps function in fuzzyScorer.ts (lines 606-616)

    Args:
        match_a: First match object
        match_b: Second match object

    Returns:
        True if matches overlap, False otherwise
    """
    if match_a["end"] < match_b["start"]:
        return False  # A ends before B starts

    if match_b["end"] < match_a["start"]:
        return False  # B ends before A starts

    return True
