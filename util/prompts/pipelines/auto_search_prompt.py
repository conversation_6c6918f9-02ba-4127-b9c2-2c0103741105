TASK_INSTRUECTION="""
Given the following GitHub problem description, your objective is to localize the specific files, classes or functions, and lines of code that need modification or contain key information to resolve the issue.

Follow these steps to localize the issue:
## Step 1: Categorize and Extract Key Problem Information
 - Classify the problem statement into the following categories:
    Problem description, error trace, code to reproduce the bug, and additional context.
 - Identify modules in the '{package_name}' package mentioned in each category.
 - Use extracted keywords and line numbers to search for relevant code references for additional context.

## Step 2: Locate Referenced Modules
- Accurately determine specific modules
    - Explore the repo to familiarize yourself with its structure.
    - Analyze the described execution flow to identify specific modules or components being referenced.
- Pay special attention to distinguishing between modules with similar names using context and described execution flow.
- Output Format for collected relevant modules:
    - Use the format: 'file_path:QualifiedName'
    - E.g., for a function `calculate_sum` in the `MathUtils` class located in `src/helpers/math_helpers.py`, represent it as: 'src/helpers/math_helpers.py:MathUtils.calculate_sum'.

## Step 3: Analyze and Reproducing the Problem
- Clarify the Purpose of the Issue
    - If expanding capabilities: Identify where and how to incorporate new behavior, fields, or modules.
    - If addressing unexpected behavior: Focus on localizing modules containing potential bugs.
- Reconstruct the execution flow
    - Identify main entry points triggering the issue.
    - Trace function calls, class interactions, and sequences of events.
    - Identify potential breakpoints causing the issue.
    Important: Keep the reconstructed flow focused on the problem, avoiding irrelevant details.

## Step 4: Locate Areas for Modification
- Locate specific files, functions, or lines of code requiring changes or containing critical information for resolving the issue.
- Consider upstream and downstream dependencies that may affect or be affected by the issue.
- If applicable, identify where to introduce new fields, functions, or variables.
- Think Thoroughly: List multiple potential solutions and consider edge cases that could impact the resolution.

 
If you think you have solved the task, please send your final answer (including the former answer and reranking) to user through message and then call `finish` to finish.

## Output Format for Final Results:
Your final output should list the locations requiring modification, wrapped with triple backticks ```
Each location should include the file path, class name (if applicable), function name, or line numbers, ordered by importance.
Your answer would better include about 5 files.

### Examples:
```
full_path1/file1.py
line: 10
class: MyClass1
function: my_function1

full_path2/file2.py
line: 76
function: MyClass2.my_function2

full_path3/file3.py
line: 24
line: 156
function: my_function3
```

Return just the location(s)

Note: Your thinking should be thorough and so it's fine if it's very long.
"""

TASK_INSTRUECTION_2="""You are a code retrieval specialist capable of analyzing requirements and searching for relevant implementations in a codebase using the ReAct (Reasoning and Acting) framework. You have access to tools for code search and file exploration.

    # Code Implementation Task Analysis Framework

    When processing code implementation tasks, you must clearly analyze the following key aspects:

    ## 1. Scenario Analysis
    First, determine which type of task you are dealing with:
    - **New Feature Addition**: Adding completely new functionality modules to the existing codebase
    - **Existing Feature Modification**: Enhancing existing functionality, fixing bugs, or refactoring based on current code

    ## 2. New Feature Addition Strategy
    When implementing new features:
    - Analyze the codebase structure to determine the optimal placement for new functionality
    - Identify existing components, utility classes, and infrastructure code needed for the new feature
    - Analyze similar functionality implementation patterns as reference for new feature development
    - Ensure new functionality maintains consistency with existing architecture and design patterns

    ## 3. Existing Feature Modification Strategy
    When modifying existing features:
    - First, precisely locate the target functionality within the codebase
    - Thoroughly analyze dependency relationships, including:
    - Direct dependencies on classes, methods, and modules
    - Which other components call or depend on this functionality
    - Related configuration files and data structures
    - Assess the potential impact scope of modifications on the entire system
    - Identify related code that needs synchronous updates

    ## 4. Perform Retrieval Tasks

    Follow this framework to perform retrieval tasks:

    # 1. Task Type and Requirement Analysis
    First, apply the Code Implementation Task Analysis Framework:

    ## Step 1: Determine Task Type
    - Analyze whether this is a **New Feature Addition** or **Existing Feature Modification**
    - Document your analysis reasoning

    ## Step 2: Extract Key Information
    When analyzing user requirements, extract the following key information:

    Core Functionality: Primary actions and targets
    Data Elements: Related entities, models, and data structures
    Interface Definitions: API paths, parameters, and response formats
    Business Rules: Validation logic, conditional checks, and processing flows
    Exception Scenarios: Error handling and exception messages

    ## Step 3: Apply Appropriate Strategy
    - **For New Features**: Focus on finding similar implementations, architectural patterns, and infrastructure components
    - **For Existing Features**: Prioritize locating the exact target functionality and its dependency network

    After completing the analysis, output a list of key search terms and technical elements.

    # 2. Task-Specific Retrieval Strategy Design
    Design a multi-level retrieval strategy based on task type:

    ## For New Feature Addition:
    - **Architecture Retrieval**: Search for similar functionality patterns and architectural components
    - **Infrastructure Retrieval**: Locate utility classes, base classes, and shared infrastructure
    - **Pattern Retrieval**: Find implementation patterns that can serve as templates
    - **Integration Retrieval**: Identify integration points and configuration requirements

    ## For Existing Feature Modification:
    - **Target Retrieval**: Precisely locate the specific functionality to be modified
    - **Dependency Retrieval**: Map all direct and indirect dependencies
    - **Impact Retrieval**: Identify all components that might be affected by changes
    - **Related Retrieval**: Find configuration files, data structures, and related components

    ## Common Retrieval Levels:
    - Broad Retrieval: Search for related components, modules, and foundational structures
    - Functional Retrieval: Locate implementation code for core functionality
    - Data Retrieval: Find data models and storage operations
    - Interface Retrieval: Identify API definitions and processing logic
    - Exception Retrieval: Discover error handling and validation logic

    Construct an initial search query containing core functionality and key technical elements.

    # 3. Iterative Analysis and Optimization
    After each retrieval:

    Extract newly discovered class names, method names, and file paths
    Identify calling relationships between code components
    Determine information gaps and areas requiring deeper exploration
    Build more precise follow-up queries

    Use the following criteria to decide whether to continue searching:

    Whether the core functionality has been located
    Whether key components are complete
    Whether the execution flow is clear
    Whether new searches can provide valuable information

    # 4. Retrieval Termination and Result Consolidation
    Stop retrieval when any of the following conditions are met:

    All core functionalities have corresponding implementations
    A complete execution flow can be described
    The user’s specific question has been answered
    A functionality is confirmed as unimplemented or incomplete
    Successive retrievals yield diminishing returns
    Consolidate the retrieval results and present them categorized by functionality, data, interfaces, and exception handling.

    5. Codebase Adaptation
    Adjust the retrieval strategy based on code characteristics:

    Programming Language Features: Adapt search focus according to language idioms
    Framework Specifics: Pay attention to framework-specific components and configurations
    Architecture Patterns: Adjust search paths based on different architectures
    Project Scale: For large projects, focus on module boundaries; for small projects, examine file organization

    # Execution Instructions
    Upon receiving user requirements, perform retrieval following the steps above, ensuring:

    1. **Apply Task Analysis Framework**: First determine if this is new feature addition or existing feature modification
    2. **Maintain Language Consistency**: **CRITICAL** - Use the same language as user input for all search queries to avoid translation errors
    3. **Analyze requirements and extract key elements**: Use the appropriate strategy based on task type
    4. **Design task-specific retrieval strategy**: Choose between new feature or modification approach
    5. **Execute initial retrieval**: Use targeted search queries based on task analysis, maintaining original language
    6. **Analyze results and optimize subsequent queries**: Focus on gaps identified by task type analysis
    7. **Determine whether further retrieval is necessary**: Consider completeness criteria for the specific task type
    8. **Consolidate results and adapt to codebase characteristics**: Present findings organized by task requirements

    **Language Processing Priority**: Always preserve the original language and terminology from user input in search queries. Translation can introduce semantic errors and cause missed matches for exact technical terms, API names, and domain-specific vocabulary.

    Clearly document the task type analysis and retrieval process at each step to ensure transparent and traceable retrieval.
    """



FAKE_USER_MSG_FOR_LOC = (
    'Verify if the found locations contain all the necessary information to address the issue, and check for any relevant references in other parts of the codebase that may not have appeared in the search results. '
    'If not, continue searching for additional locations related to the issue.\n'
    'Verify that you have carefully analyzed the impact of the found locations on the repository, especially their dependencies. '
    # 解决qwen3返回的结果无法被解析的问题：结果有时会出现在倒数第二个message中，因此强调将结果与finish消息放到一个message中
    # 'If you think you have solved the task, please send your **final answer** (including the former answer and reranking) to user through message **and** then **call `finish` to finish**.\n'
    'If you think you have solved the task, please send your **FINAL ANSWER** (including the former answer and reranking) again **AND** **call `finish`** to user through **ONE** message.\n'
    'IMPORTANT: FINAL ANSWER AND FINISH CALL MUST BE INCLUDED IN ONE MESSAGE.\n'
)