import os
import dspy

from filelock import FileLock
from pathvalidate import sanitize_filename

from auto_search_main import Localize
from util.server import ROOT_INDEX_BASE_DIR, logger
from plugins.location_tools.repo_ops.index import Index
from plugins.location_tools.repo_ops.repo_ops import RepoOps


lm = dspy.LM(
    "openai/DeepSeek-V3",
    cache=False,
    num_retries=15,
    api_key="7d6c438d2b0441bd96c79d885fb06503",
    base_url="https://maas-apigateway.dt.zte.com.cn/STREAM/deepseek-v3-api/v1",
)
dspy.configure(lm=lm)


def process_locagent_request() -> tuple[dict, int]:

    

    repo_name = sanitize_filename(root_path, replacement_text="_")
    user_repo_name = user_name + repo_name

    # TODO 后期可以统一接口
    index_lock = os.path.join(ROOT_INDEX_BASE_DIR, f"{user_repo_name}.lock")
    with FileLock(index_lock):
        try:
            repo_index = Index.get(
                index_dir=ROOT_INDEX_BASE_DIR, instance_id=user_repo_name
            )
            repo_ops = RepoOps(repo_index)
        except Exception as e:
            return {"status": "error", "message": "Index not found", "error": str(e)}, 400

    react = dspy.ReAct(
        Localize,
        max_iters=20,
        tools=[
            repo_ops.search_code_snippets,
            repo_ops.explore_tree_structure,
            repo_ops.get_entity_contents,
        ],
    )
    try:
        pred = react(problem_statement=problem_statement)
        return pred.locations, 200
    except Exception as e:
        logger.error(f"Error: {e}")
        return {}, 200


def retrieve_code(user_instruction: str, user_name: str, workspace: str) -> str:
    repo_name = sanitize_filename(workspace, replacement_text="_")
    user_repo_name = user_name + repo_name

    # TODO 后期可以统一接口
    index_lock = os.path.join(ROOT_INDEX_BASE_DIR, f"{user_repo_name}.lock")
    with FileLock(index_lock):
        repo_index = Index.get(
            index_dir=ROOT_INDEX_BASE_DIR, instance_id=user_repo_name
        )
        repo_ops = RepoOps(repo_index)

    react = dspy.ReAct(
        Localize,
        max_iters=20,
        tools=[
            repo_ops.search_code_snippets,
            repo_ops.explore_tree_structure,
            repo_ops.get_entity_contents,
        ],
    )
    return react(problem_statement=user_instruction)

__all__ = ["retrieve_code"]
