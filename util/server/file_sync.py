import os
import time
import queue
import shutil
import requests
import json

from pathlib import Path
from filelock import FileLock
from pathvalidate import sanitize_filename

from util.server import (
    ROOT_REPO_BASE_DIR, 
    ROOT_INDEX_BASE_DIR, 
    logger
)

SUPPORT_LANGUAGE = [".java"]
WHITELIST_URL = "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/zero-agents/LocAgent/LocAgent.whitelist"
LOCAL_WHITELIST_PATH = Path(__file__).parent / "LocAgent.whitelist"
queue_dict = {}


def process_queued():
    while True:
        repos_to_process = list(queue_dict.keys())
        for repo_path in repos_to_process:
            q = queue_dict.get(repo_path)
            if not q:
                continue

            batch = []
            qsize = q.qsize()
            while qsize:
                try:
                    req = q.get(timeout=1)
                    batch.append(req)
                    q.task_done()
                except queue.Empty:
                    break
                qsize -= 1

            if not batch:
                continue
            repo_name = repo_path.split("/")[-1]
            repo_lock = os.path.join(os.path.dirname(repo_path), f"{repo_name}.lock")
            with FileLock(repo_lock):
                for req in batch:
                    file_sync(req)
        time.sleep(0.1)


def file_sync(request_data):
    text = request_data["text"]
    file_path = request_data["file_path"]
    repo_path = request_data["repo_path"]
    operation_type = request_data["operation_type"]

    full_path = Path(repo_path) / file_path
    full_path = full_path.resolve()
    if not str(full_path).startswith(str(Path(repo_path).resolve())):
        raise PermissionError("非法路径访问")

    full_path.parent.mkdir(parents=True, exist_ok=True)

    if operation_type == "add":
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(text + "\n")
    elif operation_type == "modify":
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(text + "\n")
    elif operation_type == "delete":
        new_path = full_path.with_name(full_path.name + ".locagent_delete")
        if full_path.exists():
            full_path.unlink()
        with open(new_path, "w", encoding="utf-8") as f:
            pass


def process_update_request(request_data):
    required_fields = [
        "text",
        "user_name",
        "file_path",
        "rootPath",
        "type"
    ]
    for field in required_fields:
        if field not in request_data:
            return {"status": "error", "message": f"缺少必要字段: {field}"}, 400

    user_name = request_data["user_name"]
    root_path = request_data["rootPath"]
    project_name = sanitize_filename(root_path, replacement_text="_")
    repo_path = os.path.join(ROOT_REPO_BASE_DIR, user_name, project_name)

    # 确保队列存在
    if repo_path not in queue_dict:
        queue_dict[repo_path] = queue.Queue()

    # 将请求加入对应队列
    queue_dict[repo_path].put(
        {
            "text": request_data["text"],
            "file_path": request_data["file_path"],
            "repo_path": repo_path,
            "operation_type": request_data["type"],
        }
    )

    return {"status": "success", "message": "请求已加入处理队列"}, 200


def process_delete_index_request(request_data):
    """
    处理索引删除请求
    """
    try:
        required_fields = ['user_name', 'projectPath']
        for field in required_fields:
            if field not in request_data:
                return {"status": "error", "message": f"缺少必要字段: {field}"}, 400
        user_name = request_data['user_name']
        root_path = request_data['projectPath']
        project_name = sanitize_filename(root_path, replacement_text="_")
        repo_path = os.path.join(ROOT_REPO_BASE_DIR, user_name, project_name)
        repo_lock, index_lock = get_lock(repo_path)
        with FileLock(index_lock):
            with FileLock(repo_lock):
                logger.info(f"删除代码库: {repo_path}")
                if os.path.exists(repo_path):
                    shutil.rmtree(repo_path)
                delete_index(repo_path)
                return {"status": "success", "message": f"索引已删除: {repo_path}"}, 200

    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}")
        return {"status": "error", "message": str(e)}, 500


def process_get_config_request(request_data):
    required_fields = ['user_name']
    for field in required_fields:
        if field not in request_data:
            return {"status": "error", "message": f"缺少必要字段: {field}"}, 400
    user_name = request_data['user_name']
    white_list = get_white_list()
    file_pattern = SUPPORT_LANGUAGE
    file_pattern = json.dumps(file_pattern)
    has_authentication = "false"
    if user_name in white_list:
        has_authentication = "true"
    response = {
        "status": "success",
        "message": "文件模式已更新",
        "has_authentication": has_authentication,
        "file_pattern": file_pattern
        }
    return response, 200


def get_lock(repo_path):
    repo_name = repo_path.split("/")[-1]
    user_name = os.path.dirname(repo_path).split("/")[-1]
    user_repo_name = user_name + repo_name

    repo_lock = os.path.join(os.path.dirname(repo_path), f"{repo_name}.lock")
    index_lock = os.path.join(ROOT_INDEX_BASE_DIR, f"{user_repo_name}.lock")

    return repo_lock, index_lock


def delete_index(repo_path):
    logger.info(f"删除索引: {repo_path}")
    repo_name = repo_path.split("/")[-1]
    user_name = os.path.dirname(repo_path).split("/")[-1]
    instance_id = user_name + repo_name
    graph_index_subdir = f"{ROOT_INDEX_BASE_DIR}/graph_index_v2.3"
    bm25_index_subdir = f"{ROOT_INDEX_BASE_DIR}/BM25_index"
    graph_index_file = os.path.join(graph_index_subdir, f"{instance_id}.pkl")
    bm25_persist_path = os.path.join(bm25_index_subdir, instance_id)
    if os.path.exists(graph_index_file):
        logger.info(f"delete repo graph index {graph_index_file}")
        os.unlink(graph_index_file)
    if os.path.exists(bm25_persist_path):
        shutil.rmtree(bm25_persist_path)
        logger.info(f"delete repo bm25 index {bm25_persist_path}")


def get_white_list():
    response = requests.get(WHITELIST_URL)
    if response.status_code == 200:
        file_content = response.text
        whitelist = [line.strip() for line in file_content.split('\n')]
        if whitelist[-1] == '':
            whitelist = whitelist[:-1]
        return whitelist
    else:
        print(f"下载失败，状态码: {response.status_code},从本地获取白名单")
    with open(LOCAL_WHITELIST_PATH, 'r') as file:
        whitelist = [line.strip() for line in file.readlines()]
        return whitelist