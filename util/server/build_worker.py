import os
import queue
import shutil
import threading

from filelock import FileLock
from concurrent.futures import Thread<PERSON>oolExecutor
from apscheduler.schedulers.background import BackgroundScheduler

from plugins.location_tools.repo_ops.index import Index

from util.server import ROOT_REPO_BASE_DIR, ROOT_INDEX_BASE_DIR, logger


TIMER_INTERVAL: int = 20
TASK_QUEUE_MAX_SIZE: int = 1000
THREAD_POOL_MAX_WORKERS: int = 8

task_queue = queue.Queue(maxsize=TASK_QUEUE_MAX_SIZE)

thread_pool = ThreadPoolExecutor(
    max_workers=THREAD_POOL_MAX_WORKERS, thread_name_prefix="ConsumerThread"
)

scheduler = BackgroundScheduler()


def get_thread_name() -> str:
    return threading.current_thread().name


def produce_directory_tasks() -> None:
    logger.info(f"Thread '{get_thread_name()}', scaning directory...")

    try:
        for user_dir in os.listdir(ROOT_REPO_BASE_DIR):
            user_path = os.path.join(ROOT_REPO_BASE_DIR, user_dir)
            for repo_dir in os.listdir(user_path):
                repo_path = os.path.join(user_path, repo_dir)
                if os.path.isdir(repo_path):
                    for _, _, file_names in os.walk(repo_path):
                        if file_names:
                            task_queue.put(repo_path)
                            logger.info(f"Task queue put directory: '{repo_path}'")
                            break
    except Exception as e:
        logger.error(f"Sacning error: {e}")


def consume_directory_task() -> None:
    logger.info(f"Thread '{get_thread_name()}', waiting for tasks...")
    while True:
        try:
            repo_path = task_queue.get(timeout=1)
            logger.info(f"Get the task from the queue: '{repo_path}'")
            if not any(os.listdir(repo_path)):
                logger.info(f"Directory: '{repo_path}' is empty, skip processing.")
                continue
            process_directory_task(repo_path)
        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"Error in handling the task: {e}")
            import traceback
            traceback.print_exc()


def clean_directory_except_lock(root_dir: str) -> None:
    # 确保传入的是有效目录
    if not os.path.isdir(root_dir):
        logger.error(f"错误: {root_dir} 不是有效目录")
        return

    # 遍历目录下的所有内容
    for item in os.listdir(root_dir):
        item_path = os.path.join(root_dir, item)

        # 跳过 .lock 文件
        if item == ".lock":
            continue

        try:
            # 删除文件或目录
            if os.path.isfile(item_path) or os.path.islink(item_path):
                os.unlink(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
        except Exception as e:
            logger.error(f"删除 {item_path} 失败: {e}")


def process_directory_task(repo_path: str) -> None:
    logger.info(f"Thread '{get_thread_name()}', handling directory '{repo_path}'...")

    repo_name = repo_path.split("/")[-1]
    user_name = os.path.dirname(repo_path).split("/")[-1]
    user_repo_name = user_name + repo_name

    repo_lock = os.path.join(os.path.dirname(repo_path), f"{repo_name}.lock")
    index_lock = os.path.join(ROOT_INDEX_BASE_DIR, f"{user_repo_name}.lock")

    repo_lock_name = repo_lock.split("/")[-1]
    index_lock_name = index_lock.split("/")[-1]

    with FileLock(repo_lock):
        with FileLock(index_lock):
            logger.info(f"Locking folder: '{repo_path}', with '{repo_lock_name}'")
            logger.info(f"Locking index with '{index_lock_name}'")
            Index.build(
                repo_dir=repo_path,
                index_dir=ROOT_INDEX_BASE_DIR,
                instance_id=user_repo_name,
            )
            clean_directory_except_lock(repo_path)
        logger.info(f"Unlocking index with '{index_lock_name}'")
    logger.info(f"Unlocking folder: '{repo_path}', with '{repo_lock_name}'")


def produce_scheduler() -> None:
    scheduler.add_job(
        produce_directory_tasks,
        "interval",
        seconds=TIMER_INTERVAL,
        max_instances=1,
        name="ProducerThread",
    )
    scheduler.start()
    logger.info(f"Start {scheduler._thread.name} thread...")


def consume_thread_pool() -> None:
    for _ in range(THREAD_POOL_MAX_WORKERS):
        thread_pool.submit(consume_directory_task)


__all__ = ["produce_scheduler", "consume_thread_pool"]


if __name__ == "__main__":

    logger.info("Start main thread...")

    produce_scheduler()
    consume_thread_pool()

    try:
        while True:
            threading.Event().wait(1)
    except (KeyboardInterrupt, SystemExit):
        logger.warning("Received interrupt signal, ready to exit...")
        scheduler.shutdown(wait=True)
        thread_pool.shutdown(wait=True)
        logger.warning("Exit the main thread...")
