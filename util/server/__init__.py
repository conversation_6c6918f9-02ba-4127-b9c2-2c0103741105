import os
import logging


USER_HOME = os.environ.get("HOME", os.path.expanduser("~"))
ROOT_REPO_BASE_DIR = os.path.join(USER_HOME, "zeroAgentRuntime", "repo")
ROOT_INDEX_BASE_DIR = os.path.join(USER_HOME, "zeroAgentRuntime", "index")

os.makedirs(ROOT_REPO_BASE_DIR, exist_ok=True)
os.makedirs(ROOT_INDEX_BASE_DIR, exist_ok=True)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

formatter = logging.Formatter(
    fmt="%(asctime)s (%(levelname)s) [%(name)s:%(funcName)s:%(lineno)d]: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
handler.setFormatter(formatter)

logger.addHandler(handler)
logger.propagate = False


__all__ = ["ROOT_REPO_BASE_DIR", "ROOT_INDEX_BASE_DIR", "logger"]
