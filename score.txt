Executing: python evaluation/eval_metric.py --dataset datasets/multi_swe_java/mockito__mockito_dataset_processed.jsonl --locagent_loc_file /home/<USER>/Downloads/loc_outputs.jsonl --metrics recall precision

Detailed Results:
--------------------------------------------------------------------------------

FILE Level Results:
   Recall@1  Recall@3  Recall@5     P@1     P@3     P@5
0    0.3222       0.7       0.7  0.8333  0.6111  0.3667

MODULE Level Results:
   Recall@5  Recall@10  P@5  P@10
0       0.0        0.0  0.0   0.0

FUNCTION Level Results:
   Recall@5  Recall@10  P@5  P@10
0       0.0        0.0  0.0   0.0
Executing: python evaluation/eval_metric.py --dataset datasets/multi_swe_java/mockito__mockito_dataset_processed.jsonl --locagent_loc_file /home/<USER>/Downloads/loc_outputs.jsonl --metrics recall precision --verbose

================================================================================
Detailed Results for FILE Level
================================================================================

Instance: mockito__mockito-3424
Problem: Fixes #3419: Disable mocks with an error message
This allows us to avoid the memory leak issues addressed by clearInlineMocks, but track how a mock object might leak out of its creating test, as refer...
Ground Truth (5): ['src/main/java/org/mockito/MockitoFramework.java', 'src/main/java/org/mockito/exceptions/misusing/DisabledMockException.java', 'src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/framework/DisabledMockHandler.java', 'src/main/java/org/mockito/plugins/InlineMockMaker.java']
Predictions (4): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/creation/bytebuddy/MockMethodInterceptor.java', 'src/main/java/org/mockito/invocation/MockHandler.java', 'src/main/java/org/mockito/invocation/Invocation.java']
Matches (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Metrics: Recall@1: 1.0, Recall@3: 0.3333, Recall@5: 0.2, P@1: 1.0, P@3: 0.3333, P@5: 0.2

Instance: mockito__mockito-3220
Problem: Fixes #3219: Add support for static mocks on DoNotMockEnforcer
Fixes #3219
Fix mockStatic bypassing DoNotMockEnforcer
Add (optional) method on DoNotMockEnforcer for static mocks

<!-- Hey,
Thanks...
Ground Truth (11): ['src/main/java/org/mockito/internal/MockitoCore.java', 'src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java', 'src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry.java', 'src/main/java/org/mockito/internal/configuration/plugins/Plugins.java', 'src/main/java/org/mockito/internal/creation/MockSettingsImpl.java', 'src/main/java/org/mockito/internal/creation/settings/CreationSettings.java', 'src/main/java/org/mockito/internal/util/MockNameImpl.java', 'src/main/java/org/mockito/mock/MockCreationSettings.java', 'src/main/java/org/mockito/mock/MockType.java', 'src/main/java/org/mockito/plugins/DoNotMockEnforcer.java', 'src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType.java']
Predictions (4): ['src/main/java/org/mockito/plugins/DoNotMockEnforcer.java', 'src/main/java/org/mockito/internal/MockitoCore.java', 'src/main/java/org/mockito/internal/MockitoCore.java', 'src/main/java/org/mockito/internal/configuration/DefaultDoNotMockEnforcer.java']
Matches (3): ['src/main/java/org/mockito/plugins/DoNotMockEnforcer.java', 'src/main/java/org/mockito/internal/MockitoCore.java', 'src/main/java/org/mockito/internal/MockitoCore.java']
Metrics: Recall@1: 1.0, Recall@3: 1.0, Recall@5: 0.6, P@1: 1.0, P@3: 1.0, P@5: 0.6

Instance: mockito__mockito-3173
Problem: Fixes #3160 : Fix interference between spies when spying on records.
This fixes #3160. This is a bug where spied records end up having all their fields null. Here's a reproducer of the bug:

```java...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Predictions (4): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/util/reflection/InstrumentationMemberAccessor.java', 'src/main/java/org/mockito/internal/util/reflection/LenientCopyTool.java']
Matches (2): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Metrics: Recall@1: 1.0, Recall@3: 2.0, Recall@5: 2.0, P@1: 1.0, P@3: 0.6667, P@5: 0.4

Instance: mockito__mockito-3167
Problem: Deep Stubs Incompatible With Mocking Enum
Mockito can't mock abstract enums in Java 15 or later because they are now marked as sealed.
So Mockito reports that now with a better error message.

If a...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@1: 0.0, Recall@3: 0.0, Recall@5: 0.0, P@1: 0.0, P@3: 0.0, P@5: 0.0

Instance: mockito__mockito-3133
Problem: Fixes #1382 Jupiter Captor annotation support
## Checklist

 - [X] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)
 - [X] PR should be motivated,...
Ground Truth (7): ['src/main/java/org/mockito/Captor.java', 'src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java', 'src/main/java/org/mockito/internal/util/reflection/GenericMaster.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver.java']
Predictions (3): ['subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java', 'src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java', 'src/main/java/org/mockito/internal/configuration/MockAnnotationProcessor.java']
Matches (2): ['subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java', 'src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java']
Metrics: Recall@1: 1.0, Recall@3: 0.6667, Recall@5: 0.4, P@1: 1.0, P@3: 0.6667, P@5: 0.4

Instance: mockito__mockito-3129
Problem: Make MockUtil.getMockMaker() public Mockito API
The MockitoPlugins interface now provides access to the `MockUtil.getMockMaker()` method.

Fixes #3128

## Checklist

 - [x] Read the [contributin...
Ground Truth (3): ['src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java', 'src/main/java/org/mockito/internal/util/MockUtil.java', 'src/main/java/org/mockito/plugins/MockitoPlugins.java']
Predictions (3): ['src/main/java/org/mockito/internal/util/MockUtil.java', 'src/main/java/org/mockito/plugins/MockitoPlugins.java', 'src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java']
Matches (3): ['src/main/java/org/mockito/internal/util/MockUtil.java', 'src/main/java/org/mockito/plugins/MockitoPlugins.java', 'src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java']
Metrics: Recall@1: 1.0, Recall@3: 1.0, Recall@5: 1.0, P@1: 1.0, P@3: 1.0, P@5: 0.6

================================================================================
Detailed Results for MODULE Level
================================================================================

Instance: mockito__mockito-3424
Problem: Fixes #3419: Disable mocks with an error message
This allows us to avoid the memory leak issues addressed by clearInlineMocks, but track how a mock object might leak out of its creating test, as refer...
Ground Truth (5): ['src/main/java/org/mockito/MockitoFramework.java:src/main/java/org/mockito/MockitoFramework', 'src/main/java/org/mockito/exceptions/misusing/DisabledMockException.java:src/main/java/org/mockito/exceptions/misusing/DisabledMockException', 'src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker', 'src/main/java/org/mockito/internal/framework/DisabledMockHandler.java:src/main/java/org/mockito/internal/framework/DisabledMockHandler', 'src/main/java/org/mockito/plugins/InlineMockMaker.java:src/main/java/org/mockito/plugins/InlineMockMaker']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3220
Problem: Fixes #3219: Add support for static mocks on DoNotMockEnforcer
Fixes #3219
Fix mockStatic bypassing DoNotMockEnforcer
Add (optional) method on DoNotMockEnforcer for static mocks

<!-- Hey,
Thanks...
Ground Truth (11): ['src/main/java/org/mockito/internal/MockitoCore.java:src/main/java/org/mockito/internal/MockitoCore', 'src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java:src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins', 'src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry.java:src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry', 'src/main/java/org/mockito/internal/configuration/plugins/Plugins.java:src/main/java/org/mockito/internal/configuration/plugins/Plugins', 'src/main/java/org/mockito/internal/creation/MockSettingsImpl.java:src/main/java/org/mockito/internal/creation/MockSettingsImpl', 'src/main/java/org/mockito/internal/creation/settings/CreationSettings.java:src/main/java/org/mockito/internal/creation/settings/CreationSettings', 'src/main/java/org/mockito/internal/util/MockNameImpl.java:src/main/java/org/mockito/internal/util/MockNameImpl', 'src/main/java/org/mockito/mock/MockCreationSettings.java:src/main/java/org/mockito/mock/MockCreationSettings', 'src/main/java/org/mockito/mock/MockType.java:src/main/java/org/mockito/mock/MockType', 'src/main/java/org/mockito/plugins/DoNotMockEnforcer.java:src/main/java/org/mockito/plugins/DoNotMockEnforcer', 'src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType.java:src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3173
Problem: Fixes #3160 : Fix interference between spies when spying on records.
This fixes #3160. This is a bug where spied records end up having all their fields null. Here's a reproducer of the bug:

```java...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3167
Problem: Deep Stubs Incompatible With Mocking Enum
Mockito can't mock abstract enums in Java 15 or later because they are now marked as sealed.
So Mockito reports that now with a better error message.

If a...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3133
Problem: Fixes #1382 Jupiter Captor annotation support
## Checklist

 - [X] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)
 - [X] PR should be motivated,...
Ground Truth (7): ['src/main/java/org/mockito/Captor.java:src/main/java/org/mockito/Captor', 'src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java:src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor', 'src/main/java/org/mockito/internal/util/reflection/GenericMaster.java:src/main/java/org/mockito/internal/util/reflection/GenericMaster', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3129
Problem: Make MockUtil.getMockMaker() public Mockito API
The MockitoPlugins interface now provides access to the `MockUtil.getMockMaker()` method.

Fixes #3128

## Checklist

 - [x] Read the [contributin...
Ground Truth (3): ['src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java:src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins', 'src/main/java/org/mockito/internal/util/MockUtil.java:src/main/java/org/mockito/internal/util/MockUtil', 'src/main/java/org/mockito/plugins/MockitoPlugins.java:src/main/java/org/mockito/plugins/MockitoPlugins']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

================================================================================
Detailed Results for FUNCTION Level
================================================================================

Instance: mockito__mockito-3424
Problem: Fixes #3419: Disable mocks with an error message
This allows us to avoid the memory leak issues addressed by clearInlineMocks, but track how a mock object might leak out of its creating test, as refer...
Ground Truth (5): ['src/main/java/org/mockito/MockitoFramework.java:src/main/java/org/mockito/MockitoFramework.java', 'src/main/java/org/mockito/exceptions/misusing/DisabledMockException.java:src/main/java/org/mockito/exceptions/misusing/DisabledMockException.java', 'src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java', 'src/main/java/org/mockito/internal/framework/DisabledMockHandler.java:src/main/java/org/mockito/internal/framework/DisabledMockHandler.java', 'src/main/java/org/mockito/plugins/InlineMockMaker.java:src/main/java/org/mockito/plugins/InlineMockMaker.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3220
Problem: Fixes #3219: Add support for static mocks on DoNotMockEnforcer
Fixes #3219
Fix mockStatic bypassing DoNotMockEnforcer
Add (optional) method on DoNotMockEnforcer for static mocks

<!-- Hey,
Thanks...
Ground Truth (11): ['src/main/java/org/mockito/internal/MockitoCore.java:src/main/java/org/mockito/internal/MockitoCore.java', 'src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java:src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java', 'src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry.java:src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry.java', 'src/main/java/org/mockito/internal/configuration/plugins/Plugins.java:src/main/java/org/mockito/internal/configuration/plugins/Plugins.java', 'src/main/java/org/mockito/internal/creation/MockSettingsImpl.java:src/main/java/org/mockito/internal/creation/MockSettingsImpl.java', 'src/main/java/org/mockito/internal/creation/settings/CreationSettings.java:src/main/java/org/mockito/internal/creation/settings/CreationSettings.java', 'src/main/java/org/mockito/internal/util/MockNameImpl.java:src/main/java/org/mockito/internal/util/MockNameImpl.java', 'src/main/java/org/mockito/mock/MockCreationSettings.java:src/main/java/org/mockito/mock/MockCreationSettings.java', 'src/main/java/org/mockito/mock/MockType.java:src/main/java/org/mockito/mock/MockType.java', 'src/main/java/org/mockito/plugins/DoNotMockEnforcer.java:src/main/java/org/mockito/plugins/DoNotMockEnforcer.java', 'src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType.java:src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3173
Problem: Fixes #3160 : Fix interference between spies when spying on records.
This fixes #3160. This is a bug where spied records end up having all their fields null. Here's a reproducer of the bug:

```java...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3167
Problem: Deep Stubs Incompatible With Mocking Enum
Mockito can't mock abstract enums in Java 15 or later because they are now marked as sealed.
So Mockito reports that now with a better error message.

If a...
Ground Truth (1): ['src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java:src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3133
Problem: Fixes #1382 Jupiter Captor annotation support
## Checklist

 - [X] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)
 - [X] PR should be motivated,...
Ground Truth (7): ['src/main/java/org/mockito/Captor.java:src/main/java/org/mockito/Captor.java', 'src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java:src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java', 'src/main/java/org/mockito/internal/util/reflection/GenericMaster.java:src/main/java/org/mockito/internal/util/reflection/GenericMaster.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver.java', 'subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver.java:subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Instance: mockito__mockito-3129
Problem: Make MockUtil.getMockMaker() public Mockito API
The MockitoPlugins interface now provides access to the `MockUtil.getMockMaker()` method.

Fixes #3128

## Checklist

 - [x] Read the [contributin...
Ground Truth (3): ['src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java:src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java', 'src/main/java/org/mockito/internal/util/MockUtil.java:src/main/java/org/mockito/internal/util/MockUtil.java', 'src/main/java/org/mockito/plugins/MockitoPlugins.java:src/main/java/org/mockito/plugins/MockitoPlugins.java']
Predictions (0): []
Matches (0): []
Metrics: Recall@5: 0.0, Recall@10: 0.0, P@5: 0.0, P@10: 0.0

Detailed Results:
--------------------------------------------------------------------------------

FILE Level Results:
   Recall@1  Recall@3  Recall@5     P@1     P@3     P@5
0    0.3222       0.7       0.7  0.8333  0.6111  0.3667

MODULE Level Results:
   Recall@5  Recall@10  P@5  P@10
0       0.0        0.0  0.0   0.0

FUNCTION Level Results:
   Recall@5  Recall@10  P@5  P@10
0       0.0        0.0  0.0   0.0
