{"localize": true, "merge": true, "use_example": false, "ranking_method": "mrr", "dataset": "czlll/SWE-bench_Lite", "split": "test", "eval_n_limit": 300, "used_list": "selected_ids", "output_folder": "/home/<USER>/Zero/LocAgent/scripts/../results/2025_0618_193211", "output_file": "/home/<USER>/Zero/LocAgent/scripts/../results/2025_0618_193211/loc_outputs.jsonl", "merge_file": "merged_loc_outputs.jsonl", "model": "openai/Qwen3-235B-A22B", "use_function_calling": true, "simple_desc": true, "max_attempt_num": 1, "num_samples": 1, "num_processes": 8, "log_level": "INFO", "timeout": 900, "rerun_empty_location": false, "clone_dir": "/home/<USER>/Zero", "qwen3_enable_thinking": true}