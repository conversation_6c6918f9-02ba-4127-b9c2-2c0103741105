{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/codellm/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"3\" halign=\"left\">file</th>\n", "      <th colspan=\"2\" halign=\"left\">module</th>\n", "      <th colspan=\"2\" halign=\"left\">function</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>Acc@1</th>\n", "      <th>Acc@3</th>\n", "      <th>Acc@5</th>\n", "      <th>Acc@5</th>\n", "      <th>Acc@10</th>\n", "      <th>Acc@5</th>\n", "      <th>Acc@10</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.7774</td>\n", "      <td>0.9197</td>\n", "      <td>0.9416</td>\n", "      <td>0.865</td>\n", "      <td>0.8759</td>\n", "      <td>0.7336</td>\n", "      <td>0.7737</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     file                 module         function        \n", "    Acc@1   Acc@3   Acc@5  Acc@5  Acc@10    Acc@5  Acc@10\n", "0  0.7774  0.9197  0.9416  0.865  0.8759   0.7336  0.7737"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('/home/<USER>/workspace/LocAgent')\n", "\n", "from evaluation.eval_metric import evaluate_results\n", "level2key_dict = {\n", "    'file': 'found_files',\n", "    'module': 'found_modules',\n", "    'function': 'found_entities',\n", "}\n", "\n", "# eval with dataset\n", "locagent_loc_file = 'loc_output/locagent/claude_3-5/loc_outputs.jsonl'\n", "locagent_res = evaluate_results(locagent_loc_file,\n", "                        level2key_dict,\n", "                        metrics=['acc'],\n", "                        # metrics=['ndcg'],\n", "                        )\n", "locagent_res"]}], "metadata": {"kernelspec": {"display_name": "codellm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}