{"id": 1, "repo": "elastic/logstash", "instance_id": "elastic__logstash-17021", "base_commit": "32e6def9f8a9bcfe98a0cb080932dd371a9f439c", "patch": "", "problem_statement": "Backport PR #16968 to 8.16: <PERSON>x BufferedTokenizer to properly resume after a buffer full condition respecting the encoding of the input string\n**Backport PR #16968 to 8.16 branch, original message:**\n\n---\n\n## Release notes\r\n\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nThis is a second take to fix the processing of tokens from the tokenizer after a buffer full error. The first try #16482 was rollbacked to the encoding error #16694.\r\nThe first try failed on returning the tokens in the same encoding of the input.\r\nThis PR does a couple of things:\r\n- accumulates the tokens, so that after a full condition can resume with the next tokens after the offending one.\r\n- respect the encoding of the input string. Use `concat` method instead of `addAll`, which avoid to convert RubyString to String and back to RubyString. When return the head `StringBuilder` it enforce the encoding with the input charset.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPermit to use effectively the tokenizer also in context where a line is bigger than a limit.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test plan has two sides:\r\n- one to check that the behaviour of size limiting acts as expected. In such case follow the instructions in https://github.com/elastic/logstash/issues/16483.\r\n- the other to verify the encoding is respected.\r\n\r\n#### How to test the encoding is respected\r\nStartup a REPL with Logstash and exercise the tokenizer:\r\n```sh\r\n$> bin/logstash -i irb\r\n> buftok = FileWatch::BufferedTokenizer.new\r\n> buftok.extract(\"\\xA3\".force_encoding(\"ISO8859-1\")); buftok.flush.bytes\r\n```\r\n\r\nor use the following script\r\n```ruby\r\nrequire 'socket'\r\n\r\nhostname = 'localhost'\r\nport = 1234\r\n\r\nsocket = TCPSocket.open(hostname, port)\r\n\r\ntext = \"\\xA3\" # the £ symbol in ISO-8859-1 aka Latin-1\r\ntext.force_encoding(\"ISO-8859-1\")\r\nsocket.puts(text)\r\n\r\nsocket.close\r\n```\r\nwith the Logstash run as\r\n```sh\r\nbin/logstash -e \"input { tcp { port => 1234 codec => line { charset => 'ISO8859-1' } } } output { stdout { codec => rubydebug } }\"\r\n```\r\n\r\nIn the output the `£` as to be present and not `Â£`\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16694 \r\n- Relates #16482 \r\n- Relates #16483 \r\n\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 2, "repo": "elastic/logstash", "instance_id": "elastic__logstash-17020", "base_commit": "7cb1968a2eac42b41e04e62673ed920d12098ff5", "patch": "", "problem_statement": "Backport PR #16968 to 8.18: Fix BufferedTokenizer to properly resume after a buffer full condition respecting the encoding of the input string\n**Backport PR #16968 to 8.18 branch, original message:**\n\n---\n\n## Release notes\r\n\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nThis is a second take to fix the processing of tokens from the tokenizer after a buffer full error. The first try #16482 was rollbacked to the encoding error #16694.\r\nThe first try failed on returning the tokens in the same encoding of the input.\r\nThis PR does a couple of things:\r\n- accumulates the tokens, so that after a full condition can resume with the next tokens after the offending one.\r\n- respect the encoding of the input string. Use `concat` method instead of `addAll`, which avoid to convert RubyString to String and back to RubyString. When return the head `StringBuilder` it enforce the encoding with the input charset.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPermit to use effectively the tokenizer also in context where a line is bigger than a limit.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test plan has two sides:\r\n- one to check that the behaviour of size limiting acts as expected. In such case follow the instructions in https://github.com/elastic/logstash/issues/16483.\r\n- the other to verify the encoding is respected.\r\n\r\n#### How to test the encoding is respected\r\nStartup a REPL with Logstash and exercise the tokenizer:\r\n```sh\r\n$> bin/logstash -i irb\r\n> buftok = FileWatch::BufferedTokenizer.new\r\n> buftok.extract(\"\\xA3\".force_encoding(\"ISO8859-1\")); buftok.flush.bytes\r\n```\r\n\r\nor use the following script\r\n```ruby\r\nrequire 'socket'\r\n\r\nhostname = 'localhost'\r\nport = 1234\r\n\r\nsocket = TCPSocket.open(hostname, port)\r\n\r\ntext = \"\\xA3\" # the £ symbol in ISO-8859-1 aka Latin-1\r\ntext.force_encoding(\"ISO-8859-1\")\r\nsocket.puts(text)\r\n\r\nsocket.close\r\n```\r\nwith the Logstash run as\r\n```sh\r\nbin/logstash -e \"input { tcp { port => 1234 codec => line { charset => 'ISO8859-1' } } } output { stdout { codec => rubydebug } }\"\r\n```\r\n\r\nIn the output the `£` as to be present and not `Â£`\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16694 \r\n- Relates #16482 \r\n- Relates #16483 \r\n\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 3, "repo": "elastic/logstash", "instance_id": "elastic__logstash-17019", "base_commit": "f561207b4b562989192cc5c3c7d18b39f6846003", "patch": "", "problem_statement": "Backport PR #16968 to 8.x: Fix BufferedTokenizer to properly resume after a buffer full condition respecting the encoding of the input string\n**Backport PR #16968 to 8.x branch, original message:**\n\n---\n\n## Release notes\r\n\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nThis is a second take to fix the processing of tokens from the tokenizer after a buffer full error. The first try #16482 was rollbacked to the encoding error #16694.\r\nThe first try failed on returning the tokens in the same encoding of the input.\r\nThis PR does a couple of things:\r\n- accumulates the tokens, so that after a full condition can resume with the next tokens after the offending one.\r\n- respect the encoding of the input string. Use `concat` method instead of `addAll`, which avoid to convert RubyString to String and back to RubyString. When return the head `StringBuilder` it enforce the encoding with the input charset.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPermit to use effectively the tokenizer also in context where a line is bigger than a limit.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test plan has two sides:\r\n- one to check that the behaviour of size limiting acts as expected. In such case follow the instructions in https://github.com/elastic/logstash/issues/16483.\r\n- the other to verify the encoding is respected.\r\n\r\n#### How to test the encoding is respected\r\nStartup a REPL with Logstash and exercise the tokenizer:\r\n```sh\r\n$> bin/logstash -i irb\r\n> buftok = FileWatch::BufferedTokenizer.new\r\n> buftok.extract(\"\\xA3\".force_encoding(\"ISO8859-1\")); buftok.flush.bytes\r\n```\r\n\r\nor use the following script\r\n```ruby\r\nrequire 'socket'\r\n\r\nhostname = 'localhost'\r\nport = 1234\r\n\r\nsocket = TCPSocket.open(hostname, port)\r\n\r\ntext = \"\\xA3\" # the £ symbol in ISO-8859-1 aka Latin-1\r\ntext.force_encoding(\"ISO-8859-1\")\r\nsocket.puts(text)\r\n\r\nsocket.close\r\n```\r\nwith the Logstash run as\r\n```sh\r\nbin/logstash -e \"input { tcp { port => 1234 codec => line { charset => 'ISO8859-1' } } } output { stdout { codec => rubydebug } }\"\r\n```\r\n\r\nIn the output the `£` as to be present and not `Â£`\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16694 \r\n- Relates #16482 \r\n- Relates #16483 \r\n\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 4, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16968", "base_commit": "14c16de0c5fdfc817799d04dcdc7526298558101", "patch": "", "problem_statement": "Fix BufferedTokenizer to properly resume after a buffer full condition respecting the encoding of the input string\n## Release notes\r\n\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nThis is a second take to fix the processing of tokens from the tokenizer after a buffer full error. The first try #16482 was rollbacked to the encoding error #16694.\r\nThe first try failed on returning the tokens in the same encoding of the input.\r\nThis PR does a couple of things:\r\n- accumulates the tokens, so that after a full condition can resume with the next tokens after the offending one.\r\n- respect the encoding of the input string. Use `concat` method instead of `addAll`, which avoid to convert RubyString to String and back to RubyString. When return the head `StringBuilder` it enforce the encoding with the input charset.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPermit to use effectively the tokenizer also in context where a line is bigger than a limit.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test plan has two sides:\r\n- one to check that the behaviour of size limiting acts as expected. In such case follow the instructions in https://github.com/elastic/logstash/issues/16483.\r\n- the other to verify the encoding is respected.\r\n\r\n#### How to test the encoding is respected\r\nStartup a REPL with Logstash and exercise the tokenizer:\r\n```sh\r\n$> bin/logstash -i irb\r\n> buftok = FileWatch::BufferedTokenizer.new\r\n> buftok.extract(\"\\xA3\".force_encoding(\"ISO8859-1\")); buftok.flush.bytes\r\n```\r\n\r\nor use the following script\r\n```ruby\r\nrequire 'socket'\r\n\r\nhostname = 'localhost'\r\nport = 1234\r\n\r\nsocket = TCPSocket.open(hostname, port)\r\n\r\ntext = \"\\xA3\" # the £ symbol in ISO-8859-1 aka Latin-1\r\ntext.force_encoding(\"ISO-8859-1\")\r\nsocket.puts(text)\r\n\r\nsocket.close\r\n```\r\nwith the Logstash run as\r\n```sh\r\nbin/logstash -e \"input { tcp { port => 1234 codec => line { charset => 'ISO8859-1' } } } output { stdout { codec => rubydebug } }\"\r\n```\r\n\r\nIn the output the `£` as to be present and not `Â£`\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16694 \r\n- Relates #16482 \r\n- Relates #16483 \r\n\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 5, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16681", "base_commit": "a4bbb0e7b52f43fe5c422105cd88da158a7f6370", "patch": "", "problem_statement": "Backport PR #16671 to 8.15: PipelineBusV2 deadlock proofing\n**Backport PR #16671 to 8.15 branch, original message:**\n\n---\n\n## Release notes\r\n\r\n - Fixes an issue where Logstash shutdown could stall in some cases when using pipeline-to-pipeline.\r\n\r\n## What does this PR do?\r\n\r\n - Adds tests to detect deadlock betweeen `PipelineBus#unlisten` and `PipelineBus#unregisterSender`\r\n - Eliminates a deadlock that can occur in `PipelineBusV2`\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFailing to shut down is not good.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n - Resolves #16657 \r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/plugins/pipeline/PipelineBusV2.java"]}
{"id": 6, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16579", "base_commit": "2866bf9e3cacf294508154869ac5a17ed73ea027", "patch": "", "problem_statement": "Backport PR #16482 to 8.15: Bugfix for BufferedTokenizer to completely consume lines in case of lines bigger then sizeLimit\n**Backport PR #16482 to 8.15 branch, original message:**\n\n---\n\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip] \r\n\r\n## What does this PR do?\r\nUpdates `BufferedTokenizerExt` so that can accumulate token fragments coming from different data segments. When a \"buffer full\" condition is matched, it record this state in a local field so that on next data segment it can consume all the token fragments till the next token delimiter.\r\nUpdated the accumulation variable from `RubyArray` containing strings to a StringBuilder which contains the head token, plus the remaining token fragments are stored in the `input` array.\r\nPort the tests present at https://github.com/elastic/logstash/blob/f35e10d79251b4ce3a5a0aa0fbb43c2e96205ba1/logstash-core/spec/logstash/util/buftok_spec.rb#L20 in Java. \r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes the behaviour of the tokenizer to be able to work properly when buffer full conditions are met.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [x] test as described in #16483\r\n\r\n## How to test this PR locally\r\n\r\nFollow the instructions in #16483\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16483\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 7, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16569", "base_commit": "6a573f40fa3d957ef19691b8194b16528eee3ba5", "patch": "", "problem_statement": "Backport PR #16482 to 8.x: Bugfix for BufferedTokenizer to completely consume lines in case of lines bigger then sizeLimit\n**Backport PR #16482 to 8.x branch, original message:**\n\n---\n\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip] \r\n\r\n## What does this PR do?\r\nUpdates `BufferedTokenizerExt` so that can accumulate token fragments coming from different data segments. When a \"buffer full\" condition is matched, it record this state in a local field so that on next data segment it can consume all the token fragments till the next token delimiter.\r\nUpdated the accumulation variable from `RubyArray` containing strings to a StringBuilder which contains the head token, plus the remaining token fragments are stored in the `input` array.\r\nPort the tests present at https://github.com/elastic/logstash/blob/f35e10d79251b4ce3a5a0aa0fbb43c2e96205ba1/logstash-core/spec/logstash/util/buftok_spec.rb#L20 in Java. \r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes the behaviour of the tokenizer to be able to work properly when buffer full conditions are met.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [x] test as described in #16483\r\n\r\n## How to test this PR locally\r\n\r\nFollow the instructions in #16483\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16483\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 8, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16482", "base_commit": "f35e10d79251b4ce3a5a0aa0fbb43c2e96205ba1", "patch": "", "problem_statement": "Bugfix for BufferedTokenizer to completely consume lines in case of lines bigger then sizeLimit\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip] \r\n\r\n## What does this PR do?\r\nUpdates `BufferedTokenizerExt` so that can accumulate token fragments coming from different data segments. When a \"buffer full\" condition is matched, it record this state in a local field so that on next data segment it can consume all the token fragments till the next token delimiter.\r\nUpdated the accumulation variable from `RubyArray` containing strings to a StringBuilder which contains the head token, plus the remaining token fragments are stored in the `input` array.\r\nPort the tests present at https://github.com/elastic/logstash/blob/f35e10d79251b4ce3a5a0aa0fbb43c2e96205ba1/logstash-core/spec/logstash/util/buftok_spec.rb#L20 in Java. \r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes the behaviour of the tokenizer to be able to work properly when buffer full conditions are met.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [x] test as described in #16483\r\n\r\n## How to test this PR locally\r\n\r\nFollow the instructions in #16483\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16483\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/BufferedTokenizerExt.java"]}
{"id": 9, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16195", "base_commit": "d0606ff098091fa3fe482ef4a198da0163018b43", "patch": "", "problem_statement": "Introduce filesystem signalling from DLQ read to writer to update byte size metric accordingly when the reader uses `clean_consumed`\n## Release notes\r\nBugfixes DLQ's `queue_size_in_bytes` on the writer side when the reader uses `clean_consumed` and purge consumed segments.\r\n\r\n## What does this PR do?\r\n\r\nUpdates the DLQ reader to create a notification file (`.deleted_segment`) which signal when a segment is deleted in consequence of `clean_consumed` set. Updates the DLQ writer to have a filesystem watch so that can receive the reader's signal and update the exposed metric loading the size by listing FS segments occupation.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes the metric under `pipelines.<piepeline name>.dead_letter_queue.queue_size_in_bytes`\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [x] cover with tests\r\n\r\n## How to test this PR locally\r\n\r\n1. Create the `test_index` index in an ES cluster and [close it](https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-close.html).\r\n2. Edit `config/pipelines.yml` to configure one upstream and downstream pipelines.\r\n```\r\n- pipeline.id: dlq_upstream\r\n  dead_letter_queue.enable: true\r\n  config.string: |\r\n    input {\r\n      generator {\r\n        message => '{\"name\": \"Andrea\"}'\r\n        codec => json\r\n      }\r\n    }\r\n    filter {\r\n      sleep {\r\n        every => 125\r\n        time => 2\r\n      }\r\n    }\r\n    output {\r\n      elasticsearch {\r\n        cloud_id => an4m3\r\n        api_key => s3cr3t\r\n        index => \"test_index\" #index must be in closed state\r\n      }\r\n    }\r\n\r\n- pipeline.id: dlq_reader\r\n  config.string: |\r\n    input {\r\n      dead_letter_queue {\r\n        path => \"/tmp/logstash_home/data/dead_letter_queue/\"\r\n        pipeline_id => \"dlq_upstream\"\r\n        clean_consumed => true\r\n      }\r\n    }\r\n    output {\r\n      stdout {\r\n        codec => dots\r\n      }\r\n    }\r\n```\r\n3. Verify the metric with\r\n```\r\nwhile true; do curl -XGET 'localhost:9600/_node/stats/pipelines/dlq_upstream?pretty' | jq .pipelines.dlq_upstream.dead_letter_queue.queue_size_in_bytes; sleep 3; clear; done\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15721 \r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueReader.java", "logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 10, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16094", "base_commit": "18583787b3cc1095a002f4a8e1f4d9436e712c54", "patch": "", "problem_statement": "Backport PR #15969 to 8.14: Provide opt-in flag to avoid fields name clash when log format is json\n**Backport PR #15969 to 8.14 branch, original message:**\n\n---\n\n## Release notes\r\n\r\nExposes `log.format.json.fix_duplicate_message_fields` flag to avoid collision of field names in log lines when `log.format` is JSON.\r\n\r\n## What does this PR do?\r\n\r\nAdds `log.format.json.fix_duplicate_message_fields` feature flag to rename the clashing fields when json logging format (`log.format`) is selected.\r\nIn case two `message` fields clashes on structured log message, then the second is renamed attaching `_1` suffix to the field name.\r\nBy default the feature is disabled and requires user to explicitly enable the behaviour.\r\nThe PR provides description of the flag only in the throuble shooting section, and not in general description of all the command line flags and settings (https://github.com/elastic/logstash/blob/59bd376360176c4b408b3523606462567e2cc3a5/docs/static/settings-file.asciidoc?plain=1#L335).\r\nIn this way the flag can be deprecated or dropped more easily and the behaviour enabled by default.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nA user that enables json log format for their Logstash's logs could stumble on a problem to have two `message` fields in the same json document. Despite this is a valid json, is not common practice and could lead to confusion: which is the effective log message body and which is the field?\r\nWith this PR the user can choose to enable a stricter behaviour when encounter such problem.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] road test\r\n\r\n## How to test this PR locally\r\n\r\n1. run logstash with\r\n```sh\r\nbin/logstash -e \"input {stdin{codec => json}} output{stdout{}}\" --log.format json --log.format.json.fix_duplicate_message_fields true\r\n```\r\n2. type some invalid input to trigger https://github.com/logstash-plugins/logstash-codec-json/blob/d2b10edf9a63646e17e60de8c77b51ca81614c73/lib/logstash/codecs/json.rb#L84\r\n```json\r\n{\"name\": [}\r\n```\r\n\r\n3.  verify in console the json logs contains both `message` and `message_1` fields.\r\n\r\n```json\r\n{\r\n   \"level\":\"WARN\",\r\n   \"loggerName\":\"logstash.codecs.jsonlines\",\r\n   \"timeMillis\":1710838609569,\r\n   \"thread\":\"[main]<stdin\",\r\n   \"logEvent\":{\r\n      \"message\":\"JSON parse error, original data now in message field\",\r\n      \"message_1\":\"Unexpected close marker '}': expected ']' (for Array starting at [Source: (String)\\\"{\\\"name\\\": [}\\\"; line: 1, column: 10])\\n at [Source: (String)\\\"{\\\"name\\\": [}\\\"; line: 1, column: 12]\",\r\n      \"exception\":\"LogStash::Json::ParserError\",\r\n      \"data\":\"{\\\"name\\\": [}\"\r\n   }\r\n}\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14335 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["config/logstash.yml", "docker/data/logstash/env2yaml/env2yaml.go", "docs/static/running-logstash-command-line.asciidoc", "docs/static/settings-file.asciidoc", "docs/static/troubleshoot/ts-logstash.asciidoc", "logstash-core/lib/logstash/environment.rb", "logstash-core/lib/logstash/runner.rb", "logstash-core/locales/en.yml", "logstash-core/src/main/java/org/logstash/log/CustomLogEventSerializer.java"]}
{"id": 11, "repo": "elastic/logstash", "instance_id": "elastic__logstash-16079", "base_commit": "9483ee04c6bc9f8e1e80527d7ae5169dedc3f022", "patch": "", "problem_statement": "Split LS_JAVA_OPTS content when contains multiple options\n## Release notes\r\nBugfix to parse correctly Java options when the environment variable LS_JAVA_OPTS contains multiple definitions separated by space character.\r\n\r\n## What does this PR do?\r\nAdapt the parsing of `LS_JAVA_OPTS` environment variable to split by space various definitions it can contains.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPermit the user insert multiple space separated options in `LS_JAVA_OPTS`, fixing a bug that created duplicated maxOrder option when used by Docker.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n```sh\r\nexport LS_JAVA_OPTS=\" -Xmx4g -Dio.netty.allocator.maxOrder=6\" && bin/Logstash -e \"input{stdin{}} output{stdout{codec => rubydebug}}\"\r\n```\r\n\r\nIn Logstash's logs shouldn't appear both `-Dio.netty.allocator.maxOrder=6` and the default setting `-Dio.netty.allocator.maxOrder=11`, because the maxOrder is added only if it wasn't specified before.\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #16078 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["tools/jvm-options-parser/src/main/java/org/logstash/launchers/JvmOptionsParser.java"]}
{"id": 12, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15969", "base_commit": "cb45cd28cc005a580c81c05ce6032206c5731f3b", "patch": "", "problem_statement": "Provide opt-in flag to avoid fields name clash when log format is json\n## Release notes\r\n\r\nExposes `log.format.json.fix_duplicate_message_fields` flag to avoid collision of field names in log lines when `log.format` is JSON.\r\n\r\n## What does this PR do?\r\n\r\nAdds `log.format.json.fix_duplicate_message_fields` feature flag to rename the clashing fields when json logging format (`log.format`) is selected.\r\nIn case two `message` fields clashes on structured log message, then the second is renamed attaching `_1` suffix to the field name.\r\nBy default the feature is disabled and requires user to explicitly enable the behaviour.\r\nThe PR provides description of the flag only in the throuble shooting section, and not in general description of all the command line flags and settings (https://github.com/elastic/logstash/blob/59bd376360176c4b408b3523606462567e2cc3a5/docs/static/settings-file.asciidoc?plain=1#L335).\r\nIn this way the flag can be deprecated or dropped more easily and the behaviour enabled by default.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nA user that enables json log format for their Logstash's logs could stumble on a problem to have two `message` fields in the same json document. Despite this is a valid json, is not common practice and could lead to confusion: which is the effective log message body and which is the field?\r\nWith this PR the user can choose to enable a stricter behaviour when encounter such problem.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] road test\r\n\r\n## How to test this PR locally\r\n\r\n1. run logstash with\r\n```sh\r\nbin/logstash -e \"input {stdin{codec => json}} output{stdout{}}\" --log.format json --log.format.json.fix_duplicate_message_fields true\r\n```\r\n2. type some invalid input to trigger https://github.com/logstash-plugins/logstash-codec-json/blob/d2b10edf9a63646e17e60de8c77b51ca81614c73/lib/logstash/codecs/json.rb#L84\r\n```json\r\n{\"name\": [}\r\n```\r\n\r\n3.  verify in console the json logs contains both `message` and `message_1` fields.\r\n\r\n```json\r\n{\r\n   \"level\":\"WARN\",\r\n   \"loggerName\":\"logstash.codecs.jsonlines\",\r\n   \"timeMillis\":1710838609569,\r\n   \"thread\":\"[main]<stdin\",\r\n   \"logEvent\":{\r\n      \"message\":\"JSON parse error, original data now in message field\",\r\n      \"message_1\":\"Unexpected close marker '}': expected ']' (for Array starting at [Source: (String)\\\"{\\\"name\\\": [}\\\"; line: 1, column: 10])\\n at [Source: (String)\\\"{\\\"name\\\": [}\\\"; line: 1, column: 12]\",\r\n      \"exception\":\"LogStash::Json::ParserError\",\r\n      \"data\":\"{\\\"name\\\": [}\"\r\n   }\r\n}\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14335 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["config/logstash.yml", "docker/data/logstash/env2yaml/env2yaml.go", "docs/static/running-logstash-command-line.asciidoc", "docs/static/settings-file.asciidoc", "docs/static/troubleshoot/ts-logstash.asciidoc", "logstash-core/lib/logstash/environment.rb", "logstash-core/lib/logstash/runner.rb", "logstash-core/locales/en.yml", "logstash-core/src/main/java/org/logstash/log/CustomLogEventSerializer.java"]}
{"id": 13, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15964", "base_commit": "ff37e1e0d3d19b605951c94263b72c5e5a053112", "patch": "", "problem_statement": "Add shutdown step of DLQ flusher scheduled service\n\r\n## Release notes\r\n[rn:skip]\r\n\r\n\r\n## What does this PR do?\r\n\r\nThis PR adds a `shutdown` method to the `SchedulerService` class used to handle actions to be executed on a certain cadence. In particular is used to execute scheduled finalization of DLQ head segment.\r\nUpdates the `close` method of the DLQ writer to invoke this additional shutdown on the service instance.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nTime to time in unit tests an error related to segment finalization happend.\r\nIt's mostly likely:\r\n```\r\n[WARN ] 2024-02-21 12:46:44.723 [dlq-flush-check] DeadLetterQueueWriter - Unable to finalize segment\r\n    java.nio.file.NoSuchFileException: /var/folders/f2/6ln9srr13hsdp3kwfz68w3940000gn/T/junit3045222654236224377/junit12477847196935554659/2.log.tmp -> /var/folders/f2/6ln9srr13hsdp3kwfz68w3940000gn/T/junit3045222654236224377/junit12477847196935554659/2.log\r\n        at sun.nio.fs.UnixException.translateToIOException(UnixException.java:92) ~[?:?]\r\n        at sun.nio.fs.UnixException.rethrowAsIOException(UnixException.java:106) ~[?:?]\r\n        at sun.nio.fs.UnixCopyFile.move(UnixCopyFile.java:416) ~[?:?]\r\n        at sun.nio.fs.UnixFileSystemProvider.move(UnixFileSystemProvider.java:266) ~[?:?]\r\n        at java.nio.file.Files.move(Files.java:1432) ~[?:?]\r\n        at org.logstash.common.io.DeadLetterQueueWriter.sealSegment(DeadLetterQueueWriter.java:586) ~[main/:?]\r\n```\r\nThis could limit the confidence of developers to the quality of the test itself, but also implementing a properly shutdown sequence is always a good practice.\r\n\r\n## Checklist\r\n\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- ~~[ ] I have commented my code, particularly in hard-to-understand areas~~\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- ~~[ ] I have added tests that prove my fix is effective or that my feature works~~\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\nRun the DLQ unit test locally and verify no stacktraces of bad finalization of a segment happens. \r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15962 \r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 14, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15928", "base_commit": "1cca6bcb2c769db169260a30531c4f2bd2f184c3", "patch": "", "problem_statement": "Backport PR #15925 to 8.12: Set Netty's maxOrder options to previous default\n**Backport PR #15925 to 8.12 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nUpdates Netty's configuration of maxOrder to a previously proven value, if not already customised by the user.\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\nAdds a step to the JvmOption parsing tool, which is used to compose the JVM options string to pass down to Logstash at startup.\r\nThe added step rework the parsed options to set  the allocator max order `-Dio.netty.allocator.maxOrder=11` so that the maximum pooled buffer is up to 16MB and not 4MB. \r\nThis option is added iff it's not yet specified by the user\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\nIt brings back the performance of Netty based plugins to the same as of Logstash previous of `8.7.0`, which introduced an update of Netty library.\r\nWith messages bigger then 2Kb each it triggered a problem of using unpooled buffers, which kills performance. With this change it moved back to 8Kb.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test splits in 2, verify that the line `-Dio.netty.allocator.maxOrder=11` is added when the flag is not in `config/jvm.options` and verify that if the flag is already present it's not overwritten.\r\nRun Logstash with\r\n```sh\r\nbin/logstash -e \"input {stdin {}} output {stdout{}}\"\r\n```\r\n- first test: run Logstash and verify that the log that prints all the flag contains the redefinition of maxOrder\r\n```\r\n[INFO ][logstash.runner          ][main] JVM bootstrap flags: [-Xms4g,...-Dio.netty.allocator.maxOrder=11...\r\n```\r\n-second test: edit `config/jvm.options` and add a custom `io.netty.allocator.maxOrder`, start Logstash and verify logs contains the definition was added.\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15765 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["tools/jvm-options-parser/build.gradle", "tools/jvm-options-parser/src/main/java/org/logstash/launchers/JvmOptionsParser.java"]}
{"id": 15, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15925", "base_commit": "5c3e64d5916c33e7de5db2259d6ac6dd40b121ea", "patch": "", "problem_statement": "Set Netty's maxOrder options to previous default\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nUpdates Netty's configuration of maxOrder to a previously proven value, if not already customised by the user.\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\nAdds a step to the JvmOption parsing tool, which is used to compose the JVM options string to pass down to Logstash at startup.\r\nThe added step rework the parsed options to set  the allocator max order `-Dio.netty.allocator.maxOrder=11` so that the maximum pooled buffer is up to 16MB and not 4MB. \r\nThis option is added iff it's not yet specified by the user\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\nIt brings back the performance of Netty based plugins to the same as of Logstash previous of `8.7.0`, which introduced an update of Netty library.\r\nWith messages bigger then 2Kb each it triggered a problem of using unpooled buffers, which kills performance. With this change it moved back to 8Kb.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\nThe test splits in 2, verify that the line `-Dio.netty.allocator.maxOrder=11` is added when the flag is not in `config/jvm.options` and verify that if the flag is already present it's not overwritten.\r\nRun Logstash with\r\n```sh\r\nbin/logstash -e \"input {stdin {}} output {stdout{}}\"\r\n```\r\n- first test: run Logstash and verify that the log that prints all the flag contains the redefinition of maxOrder\r\n```\r\n[INFO ][logstash.runner          ][main] JVM bootstrap flags: [-Xms4g,...-Dio.netty.allocator.maxOrder=11...\r\n```\r\n-second test: edit `config/jvm.options` and add a custom `io.netty.allocator.maxOrder`, start Logstash and verify logs contains the definition was added.\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15765 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["tools/jvm-options-parser/build.gradle", "tools/jvm-options-parser/src/main/java/org/logstash/launchers/JvmOptionsParser.java"]}
{"id": 16, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15697", "base_commit": "5e28bffedaad1c872b8ce059b3905225f2ccc9a2", "patch": "", "problem_statement": "Backport PR #15680 to 8.12: Separate scheduling of segments flushes from time\n**Backport PR #15680 to 8.12 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nIntroduces a new interface named `SchedulerService` to abstract from the `ScheduledExecutorService` to execute the DLQ flushes of segments. Abstracting from time provides a benefit in testing, where the test doesn't have to wait for things to happen, but those things could happen synchronously.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nThe user in this case is the developer of test, which doesn't have to put wait conditions or sleeps in test code, resulting in more stable (less flaky tests, avoid time variability) and deterministic tests.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- ~~[ ] I have added tests that prove my fix is effective or that my feature works~~\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] make a run with Logstash flushing files, to be sure the old behaviour is maintained\r\n\r\n## How to test this PR locally\r\n\r\nThe local test just assures that the existing feature works as expected.\r\n\r\n- download Elasticsearch, unpack and run the first time. It will print the generated credentials on console, copy those somewhere for later usage.\r\n- create and close an index:\r\n```sh\r\ncurl --user elastic:<generated pwd> -k -XPUT \"https://localhost:9200/test_index\"\r\ncurl --user elastic:<generated pwd> -k -XPOST \"https://localhost:9200/test_index/_close\"\r\n```\r\n- copy the http certificates from Elasticsearch (`<es dir>/config/certs/http_ca.crt`) somewhere and make them not writeable (`chmod a-w `/tmp/http_ca.crt`)\r\n- edit a Logstash pipeline to index data into the closed index\r\n```\r\ninput {\r\n  stdin {\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"https://localhost:9200\"\r\n    user => \"elastic\"\r\n    password => \"<generated pwd>\"\r\n    ssl_enabled => true\r\n    ssl_certificate_authorities => [\"/tmp/http_ca.crt\"]\r\n  }\r\n}  \r\n```\r\n- enable DLQ on Logstash and modify the `flush_interval`, edit `config/logstash.yml`:\r\n```yaml\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.flush_interval: 10000\r\n```\r\n- run the pipeline:\r\n```sh\r\nbin/logstash -f `pwd`/dlq_pipeline.conf\r\n```\r\n- verify in `data/dead_letter_queue/main` that everytime a json message is typed into the LS console, then the DLQ folder  receives a new segment (it generates a new head segment with `tmp` suffix and previous head becomes a new segment) in 30 seconds.\r\n\r\n\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15594 \r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 17, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15680", "base_commit": "241c03274c5084851c76baf145f3878bd3c9d39b", "patch": "", "problem_statement": "Separate scheduling of segments flushes from time\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nIntroduces a new interface named `SchedulerService` to abstract from the `ScheduledExecutorService` to execute the DLQ flushes of segments. Abstracting from time provides a benefit in testing, where the test doesn't have to wait for things to happen, but those things could happen synchronously.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nThe user in this case is the developer of test, which doesn't have to put wait conditions or sleeps in test code, resulting in more stable (less flaky tests, avoid time variability) and deterministic tests.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- ~~[ ] I have added tests that prove my fix is effective or that my feature works~~\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] make a run with Logstash flushing files, to be sure the old behaviour is maintained\r\n\r\n## How to test this PR locally\r\n\r\nThe local test just assures that the existing feature works as expected.\r\n\r\n- download Elasticsearch, unpack and run the first time. It will print the generated credentials on console, copy those somewhere for later usage.\r\n- create and close an index:\r\n```sh\r\ncurl --user elastic:<generated pwd> -k -XPUT \"https://localhost:9200/test_index\"\r\ncurl --user elastic:<generated pwd> -k -XPOST \"https://localhost:9200/test_index/_close\"\r\n```\r\n- copy the http certificates from Elasticsearch (`<es dir>/config/certs/http_ca.crt`) somewhere and make them not writeable (`chmod a-w `/tmp/http_ca.crt`)\r\n- edit a Logstash pipeline to index data into the closed index\r\n```\r\ninput {\r\n  stdin {\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"https://localhost:9200\"\r\n    user => \"elastic\"\r\n    password => \"<generated pwd>\"\r\n    ssl_enabled => true\r\n    ssl_certificate_authorities => [\"/tmp/http_ca.crt\"]\r\n  }\r\n}  \r\n```\r\n- enable DLQ on Logstash and modify the `flush_interval`, edit `config/logstash.yml`:\r\n```yaml\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.flush_interval: 10000\r\n```\r\n- run the pipeline:\r\n```sh\r\nbin/logstash -f `pwd`/dlq_pipeline.conf\r\n```\r\n- verify in `data/dead_letter_queue/main` that everytime a json message is typed into the LS console, then the DLQ folder  receives a new segment (it generates a new head segment with `tmp` suffix and previous head becomes a new segment) in 30 seconds.\r\n\r\n\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #15594 \r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 18, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15241", "base_commit": "36c75c11a9c91cda3b0f00e7500f7329c8615574", "patch": "", "problem_statement": "Backport PR #15233 to 8.9: Fix DeadLetterQueueWriter unable to finalize segment error\n**Backport PR #15233 to 8.9 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\nFixed `DeadLetterQueueWriter` unable to finalize segment - `java.nio.file.NoSuchFileException`\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\n\r\nFor more details about the error, please check the https://github.com/elastic/logstash/issues/15227.\r\n\r\nThis PR moves the `Files.size(...)` call into the try catch [block](https://github.com/elastic/logstash/pull/15233/files#diff-a0ee6ca8e72a830020520ea556f56e46ec1326e48593d9dfd1f252b70d3af45aR449), that way, when the oldest segment is deleted by the `DeadLetterQueueReader`, no `NoSuchFileException` will be thrown up, and the writter will gracefully  update the oldest segment on the next `updateOldestSegmentReference` invocation (scheduled flush, entry write, delete expired, etc).\r\n\r\nIt also adds the `volatile` keyword to the shared mutable variables, making sure that all the changes will be instantly visible among all the running threads (scheduler & writer).\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [X] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [ ] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [X] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n- Set up the example pipeline posted here: https://github.com/elastic/logstash/issues/15227\r\n- Change the `DeadLetterQueueWriter#createFlushScheduler` to run more oftetn, it's not mandatory but will make the problem happen much faster:\r\n```diff\r\n- flushScheduler.scheduleAtFixedRate(this::scheduledFlushCheck, 1L, 1L, TimeUnit.SECONDS);\r\n+ flushScheduler.scheduleAtFixedRate(this::scheduledFlushCheck, 1L, 1L, TimeUnit.MILLISECONDS);\r\n```\r\n- Running with the `main` branch code, it should eventually throw a few `java.nio.file.NoSuchFileException`.\r\n- Running with this PR changes, no errors should be raised.\r\n\r\n## Related issues\r\n\r\n- Closes https://github.com/elastic/logstash/issues/15227\r\n- Closes #15078\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 19, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15233", "base_commit": "d196496f364e9f14104744609ea2c280dddd9865", "patch": "", "problem_statement": "Fix DeadLetterQueueWriter unable to finalize segment error\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\nFixed `DeadLetterQueueWriter` unable to finalize segment - `java.nio.file.NoSuchFileException`\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\n\r\nFor more details about the error, please check the https://github.com/elastic/logstash/issues/15227.\r\n\r\nThis PR moves the `Files.size(...)` call into the try catch [block](https://github.com/elastic/logstash/pull/15233/files#diff-a0ee6ca8e72a830020520ea556f56e46ec1326e48593d9dfd1f252b70d3af45aR449), that way, when the oldest segment is deleted by the `DeadLetterQueueReader`, no `NoSuchFileException` will be thrown up, and the writter will gracefully  update the oldest segment on the next `updateOldestSegmentReference` invocation (scheduled flush, entry write, delete expired, etc).\r\n\r\nIt also adds the `volatile` keyword to the shared mutable variables, making sure that all the changes will be instantly visible among all the running threads (scheduler & writer).\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [X] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [ ] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [X] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n- Set up the example pipeline posted here: https://github.com/elastic/logstash/issues/15227\r\n- Change the `DeadLetterQueueWriter#createFlushScheduler` to run more oftetn, it's not mandatory but will make the problem happen much faster:\r\n```diff\r\n- flushScheduler.scheduleAtFixedRate(this::scheduledFlushCheck, 1L, 1L, TimeUnit.SECONDS);\r\n+ flushScheduler.scheduleAtFixedRate(this::scheduledFlushCheck, 1L, 1L, TimeUnit.MILLISECONDS);\r\n```\r\n- Running with the `main` branch code, it should eventually throw a few `java.nio.file.NoSuchFileException`.\r\n- Running with this PR changes, no errors should be raised.\r\n\r\n## Related issues\r\n\r\n- Closes https://github.com/elastic/logstash/issues/15227\r\n- Closes #15078\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 20, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15008", "base_commit": "e2e16adbc2ff042dff4defa0cfbe391892dd7420", "patch": "", "problem_statement": "Backport PR #15000 to 8.7: Fix DLQscheduled checks removes expired age segments\n**Backport PR #15000 to 8.7 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nBugfix on DLQ age policy not removing expired segments until current segment receives a new event.\r\n\r\n\r\n## What does this PR do?\r\n\r\nModifies the logic used by the scheduled task flusher so that execute age policy also in case the current (head) segments is not stale (haven't received any write, and the segment is empty).\r\nThis means that generally used finalize segment logic is applied plus a reinforcement  step to grant the age policy is respected.\r\nHowever this PR:\r\n- introduced new debug log lines, improving the description of the context when a segment is finalized (because the DLQ is closing or  because the segment file has reached its maximum size or because the flush interval expiration). This is done with the introduction of `SealReason` enumeration.\r\n- introduces `Awaitility` test dependency to improve the testing of asychronous conditions.\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nComplete the fix initiated by #14878 which didn't covered the case of a head segment that remains always empty.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] test locally\r\n\r\n## How to test this PR locally\r\n\r\n- Use same pipeline, index configuration and `logstash.yml` described in #14878 \r\n- Start the pipeline and check just and empty file (1 byte) is present in DLQ folder (`<data.path>/dead_letter_queue/main/`)\r\n- Type something into the LS console to create an event that go in DLQ\r\n```json\r\n{\"name\":  \"John\"}\r\n``` \r\n- Check that the event in present in the DLQ (`cat` is enough)\r\n- Verify the existing head segment is sealed (from `1.log.tmp` is renamed to `1.tmp`) and a new 1 byte head segment is created (`2.log.tmp`)\r\n- Wait for 1 minute (the `retain.age` configured in `config/logstash.yml`)\r\n- Verify the old sealed and expired segment is removed.\r\n\r\nEnable DLQ debug log in `config/log4j2.properties` to have better visibility:\r\n```\r\nlogger.dlq.name = org.logstash.common\r\nlogger.dlq.level = debug\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14851\r\n", "edit_functions": ["logstash-core/build.gradle", "logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 21, "repo": "elastic/logstash", "instance_id": "elastic__logstash-15000", "base_commit": "0df07d3f11f2c94deb380b73f7c5265aff04cfc9", "patch": "", "problem_statement": "Fix DLQscheduled checks removes expired age segments\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nBugfix on DLQ age policy not removing expired segments until current segment receives a new event.\r\n\r\n\r\n## What does this PR do?\r\n\r\nModifies the logic used by the scheduled task flusher so that execute age policy also in case the current (head) segments is not stale (haven't received any write, and the segment is empty).\r\nThis means that generally used finalize segment logic is applied plus a reinforcement  step to grant the age policy is respected.\r\nHowever this PR:\r\n- introduced new debug log lines, improving the description of the context when a segment is finalized (because the DLQ is closing or  because the segment file has reached its maximum size or because the flush interval expiration). This is done with the introduction of `SealReason` enumeration.\r\n- introduces `Awaitility` test dependency to improve the testing of asychronous conditions.\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nComplete the fix initiated by #14878 which didn't covered the case of a head segment that remains always empty.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] test locally\r\n\r\n## How to test this PR locally\r\n\r\n- Use same pipeline, index configuration and `logstash.yml` described in #14878 \r\n- Start the pipeline and check just and empty file (1 byte) is present in DLQ folder (`<data.path>/dead_letter_queue/main/`)\r\n- Type something into the LS console to create an event that go in DLQ\r\n```json\r\n{\"name\":  \"John\"}\r\n``` \r\n- Check that the event in present in the DLQ (`cat` is enough)\r\n- Verify the existing head segment is sealed (from `1.log.tmp` is renamed to `1.tmp`) and a new 1 byte head segment is created (`2.log.tmp`)\r\n- Wait for 1 minute (the `retain.age` configured in `config/logstash.yml`)\r\n- Verify the old sealed and expired segment is removed.\r\n\r\nEnable DLQ debug log in `config/log4j2.properties` to have better visibility:\r\n```\r\nlogger.dlq.name = org.logstash.common\r\nlogger.dlq.level = debug\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14851\r\n", "edit_functions": ["logstash-core/build.gradle", "logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 22, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14981", "base_commit": "b7b714e666f8a5e32bf2aa38fccac1ebb0d9dc3d", "patch": "", "problem_statement": "Backport PR #14970 to 8.7: Fixed the DLQ writer to bypass 1 byte entry\n**Backport PR #14970 to 8.7 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n\r\nFixed the DLQ writer to bypass 1 byte entry during the search of the oldest segment timestamp\r\n\r\n## What does this PR do?\r\n\r\nWhen DLQ writer is initializing and the content of DLQ entry has only 1 byte (version number), the search of the oldest segment timestamp return `-1` [block](https://github.com/elastic/logstash/blob/v8.6.2/logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java#L398), hence, [seek](https://github.com/elastic/logstash/blob/v8.6.2/logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java#L403) the wrong position.\r\n\r\nThis PR skips \r\n- segments with size == 1 byte\r\n- seeking the block if blockId < 0. Empty timestamp returns to the caller.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPipelines are unable to start when DLQ entry is 1 byte.\r\n\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [ ] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fix #14969\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 23, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14970", "base_commit": "5e3038a3d3fd3b5792f64d7bb0ed39538f1a5a5c", "patch": "", "problem_statement": "Fixed the DLQ writer to bypass 1 byte entry\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n\r\nFixed the DLQ writer to bypass 1 byte entry during the search of the oldest segment timestamp\r\n\r\n## What does this PR do?\r\n\r\nWhen DLQ writer is initializing and the content of DLQ entry has only 1 byte (version number), the search of the oldest segment timestamp return `-1` [block](https://github.com/elastic/logstash/blob/v8.6.2/logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java#L398), hence, [seek](https://github.com/elastic/logstash/blob/v8.6.2/logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java#L403) the wrong position.\r\n\r\nThis PR skips \r\n- segments with size == 1 byte\r\n- seeking the block if blockId < 0. Empty timestamp returns to the caller.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nPipelines are unable to start when DLQ entry is 1 byte.\r\n\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [ ] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fix #14969\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 24, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14898", "base_commit": "4f0229a28712eb16c78e6c8eaff04560828a6ae2", "patch": "", "problem_statement": "Backport PR #14878 to 8.6: Fix DLQ age retention policy to be applied also in case head segment is untouched\n**Backport PR #14878 to 8.6 branch, original message:**\n\n---\n\n\r\n\r\n## Release notes\r\n\r\nBugfix on DLQ age policy not executed if the current head segment haven't receives any write\r\n\r\n## What does this PR do?\r\n\r\nThis PR fixes a bug on DLQ age policy not executed if the current head segment haven't receives any write.\r\nThe change update the `flushCheck` method, executed both on DLQ writer close and also by the scheduled flusher, so that the `executeAgeRetentionPolicy` is invoked also when the current writer hasn't received any writes.\r\nAdds some test, and to separate the testing of the close from the scheduled flush a new constructor's parameter is added, and consequently updated builder utility.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes a  bug that prohibited the execution of the age retention policy when the current head segment doesn't receive any event.\r\nThis could happen if the DLQ ends in a situation that doesn't receive any event for long period and the expired segments aren't deleted, contrasting with the age retention policy requirement.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] test locally\r\n\r\n## How to test this PR locally\r\n\r\nThe local test has to verify two conditions:\r\n1. on  Logstash shutdown, the age expired segments are removed\r\n2.  the DLQ scheduled flusher (which runs every 5 second by default) clean all expired segments *if* the current writer has been written.\r\n\r\n#### Commons setup\r\n- create an index (`test_index`) in Elasticsearch and close it, to generate DLQ eligible error codes\r\n```\r\nPUT test_index/\r\nPOST test_index/_close\r\n```\r\n- enable DLQ in Logstash, edit `config/logstash.yml` adding\r\n```yaml\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.retain.age: 1m\r\n```\r\n- create a test pipeline:\r\n```\r\ninput {\r\n  stdin {\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"http://localhost:9200\"\r\n    user => \"<user>\"\r\n    password => \"<secret>\"\r\n  }\r\n```\r\n#### 1. Verify on Logstash shutdown\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- stop Logstash\r\n- *verify* that the old segment file is removed and exist only the empty one.\r\n\r\n#### 2. Verify that the flusher clean age expired segments\r\nThe schedule flusher clean only segments that are stale, a stale segment is a current segment that hasn't been flushed in 5 seconds..\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- *verify* that the expired segment is still present\r\n- type again something on the stdin, so that an event is written into current head segment and becomes stale\r\n- *verify* the old segment is gone.\r\n\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14851 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 25, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14897", "base_commit": "c98ab61054b124a54564a8e526c036e2c95f9add", "patch": "", "problem_statement": "Backport PR #14878 to 8.7: Fix DLQ age retention policy to be applied also in case head segment is untouched\n**Backport PR #14878 to 8.7 branch, original message:**\n\n---\n\n\r\n\r\n## Release notes\r\n\r\nBugfix on DLQ age policy not executed if the current head segment haven't receives any write\r\n\r\n## What does this PR do?\r\n\r\nThis PR fixes a bug on DLQ age policy not executed if the current head segment haven't receives any write.\r\nThe change update the `flushCheck` method, executed both on DLQ writer close and also by the scheduled flusher, so that the `executeAgeRetentionPolicy` is invoked also when the current writer hasn't received any writes.\r\nAdds some test, and to separate the testing of the close from the scheduled flush a new constructor's parameter is added, and consequently updated builder utility.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes a  bug that prohibited the execution of the age retention policy when the current head segment doesn't receive any event.\r\nThis could happen if the DLQ ends in a situation that doesn't receive any event for long period and the expired segments aren't deleted, contrasting with the age retention policy requirement.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] test locally\r\n\r\n## How to test this PR locally\r\n\r\nThe local test has to verify two conditions:\r\n1. on  Logstash shutdown, the age expired segments are removed\r\n2.  the DLQ scheduled flusher (which runs every 5 second by default) clean all expired segments *if* the current writer has been written.\r\n\r\n#### Commons setup\r\n- create an index (`test_index`) in Elasticsearch and close it, to generate DLQ eligible error codes\r\n```\r\nPUT test_index/\r\nPOST test_index/_close\r\n```\r\n- enable DLQ in Logstash, edit `config/logstash.yml` adding\r\n```yaml\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.retain.age: 1m\r\n```\r\n- create a test pipeline:\r\n```\r\ninput {\r\n  stdin {\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"http://localhost:9200\"\r\n    user => \"<user>\"\r\n    password => \"<secret>\"\r\n  }\r\n```\r\n#### 1. Verify on Logstash shutdown\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- stop Logstash\r\n- *verify* that the old segment file is removed and exist only the empty one.\r\n\r\n#### 2. Verify that the flusher clean age expired segments\r\nThe schedule flusher clean only segments that are stale, a stale segment is a current segment that hasn't been flushed in 5 seconds..\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- *verify* that the expired segment is still present\r\n- type again something on the stdin, so that an event is written into current head segment and becomes stale\r\n- *verify* the old segment is gone.\r\n\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14851 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 26, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14878", "base_commit": "0600ff98bbd54918c8d18d2e4372f96c71dc235c", "patch": "", "problem_statement": "Fix DLQ age retention policy to be applied also in case head segment is untouched\n\r\n\r\n## Release notes\r\n\r\nBugfix on DLQ age policy not executed if the current head segment haven't receives any write\r\n\r\n## What does this PR do?\r\n\r\nThis PR fixes a bug on DLQ age policy not executed if the current head segment haven't receives any write.\r\nThe change update the `flushCheck` method, executed both on DLQ writer close and also by the scheduled flusher, so that the `executeAgeRetentionPolicy` is invoked also when the current writer hasn't received any writes.\r\nAdds some test, and to separate the testing of the close from the scheduled flush a new constructor's parameter is added, and consequently updated builder utility.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nFixes a  bug that prohibited the execution of the age retention policy when the current head segment doesn't receive any event.\r\nThis could happen if the DLQ ends in a situation that doesn't receive any event for long period and the expired segments aren't deleted, contrasting with the age retention policy requirement.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] test locally\r\n\r\n## How to test this PR locally\r\n\r\nThe local test has to verify two conditions:\r\n1. on  Logstash shutdown, the age expired segments are removed\r\n2.  the DLQ scheduled flusher (which runs every 5 second by default) clean all expired segments *if* the current writer has been written.\r\n\r\n#### Commons setup\r\n- create an index (`test_index`) in Elasticsearch and close it, to generate DLQ eligible error codes\r\n```\r\nPUT test_index/\r\nPOST test_index/_close\r\n```\r\n- enable DLQ in Logstash, edit `config/logstash.yml` adding\r\n```yaml\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.retain.age: 1m\r\n```\r\n- create a test pipeline:\r\n```\r\ninput {\r\n  stdin {\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"http://localhost:9200\"\r\n    user => \"<user>\"\r\n    password => \"<secret>\"\r\n  }\r\n```\r\n#### 1. Verify on Logstash shutdown\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- stop Logstash\r\n- *verify* that the old segment file is removed and exist only the empty one.\r\n\r\n#### 2. Verify that the flusher clean age expired segments\r\nThe schedule flusher clean only segments that are stale, a stale segment is a current segment that hasn't been flushed in 5 seconds..\r\n- start Logstash with the previous pipeline\r\n- type an event on `stdin` console\r\n- verify that in `data/dead_letter_queue/main` there is a segment file with size > 1\r\n- shutdown Logstash and restart so that is seal the current segment file and create a new a one (with `.tmp` postfix)\r\n- wait a period greater than `dead_letter_queue.retain.age`\r\n- *verify* that the expired segment is still present\r\n- type again something on the stdin, so that an event is written into current head segment and becomes stale\r\n- *verify* the old segment is gone.\r\n\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #14851 \r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java"]}
{"id": 27, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14571", "base_commit": "6dc5c5648ab497dfdeb31f0f1e085f9298135191", "patch": "", "problem_statement": "Extended Flow Metrics\n## Release notes\r\n\r\nExtends the flow rates introduced to the Node Stats API in 8.5.0 (which included windows for `current` and `lifetime`) to include a Technology Preview of several additional windows such as `last_15_minutes`, `last_24_hours`, etc..\r\n\r\n## What does this PR do?\r\n\r\n1. breaks the initial `FlowMetric` implementation into an interface, a group of sharable components, and a net-unchanged concrete implementation `SimpleFlowMetric`.\r\n2. ensures that flow metrics are rounded to a _precision_ instead of a _scale_ since we are looking for 3ish significant figures to make the rates meaningful and easily readable, not 3 decimal places as initially delivered (backport eligible)\r\n3. introduce a new `ExtendedFlowMetric` that produces a number of policy-driven rates which are all marked as Technology Preview:\r\n   > * `last_1_minute`: `1m`@`3s`\r\n   > * `last_5_minutes`: `5m`@`15s`\r\n   > * `last_15_minutes`: `15m`@`30s`\r\n   > * `last_1_hour`: `1h` @ `60s`\r\n   > * `last_24_hours`: `24h`@`15m`\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nIn 8.5.0 we introduced `current` and `lifetime` windows for all flow metrics, but these are made much more valuable when we have additional rates because they allow us to determine at a glance if things are actively improving or declining:\r\n\r\n~~~ json\r\n    \"filter_throughput\": {\r\n      \"current\": 263.4,\r\n      \"last_1_minute\": 32040,\r\n      \"last_5_minutes\": 83270,\r\n      \"last_15_minutes\": 97070,\r\n      \"last_1_hour\": 91230,\r\n      \"last_24_hours\": 92810,\r\n      \"lifetime\": 90840\r\n    },\r\n~~~\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- ~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## How to test this PR locally\r\n\r\nThe following pipeline will create extremely bursty throughput by injecting randomized sleeps during prime minutes, ensuring that the bursts are sustained for long enough that we can see them propagate through the rate windows.\r\n\r\n~~~\r\ninput { generator { threads => 4 } }\r\n\r\nfilter {\r\n  ruby {\r\n    init => 'require \"prime\"'\r\n    code => \"\r\n      if Prime.prime?(Time.now.min) && (Random.rand(100) <= 1)\r\n        sleep(2.0 ** Random.rand(4.0))\r\n      end\r\n    \"\r\n  }\r\n}\r\n\r\noutput { sink { } }\r\n~~~\r\n\r\nOnce the pipeline is running, watch the Node Stats API noting that rate windows will not be present until sufficient time has elapsed from pipeline start for them to be meaningful (e.g., the `last_1_hour` will be present after the pipeline has been running for ~1 hour):\r\n\r\n~~~\r\n watch \"curl -XGET 'localhost:9600/_node/stats' | jq .flow\"\r\n~~~\r\n\r\nNote that trace-level logging in `ExtendedFlowMetric` and `BaseFlowMetric` make it possible to reconstruct the retention and compaction of our windows.\r\n\r\n## Related issues\r\n\r\n- Closes #14570\r\n", "edit_functions": ["docs/static/monitoring/monitoring-apis.asciidoc", "logstash-core/lib/logstash/agent.rb", "logstash-core/src/main/java/org/logstash/execution/AbstractPipelineExt.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/BaseFlowMetric.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/ExtendedFlowMetric.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/FlowCapture.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/FlowMetric.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/FlowMetricRetentionPolicy.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/LazyInstantiatedFlowMetric.java", "logstash-core/src/main/java/org/logstash/instrument/metrics/SimpleFlowMetric.java", "logstash-core/src/main/java/org/logstash/util/SetOnceReference.java"]}
{"id": 28, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14058", "base_commit": "1c851bb15c6d8651be591f3c9389116536d22770", "patch": "", "problem_statement": "Adds DLQ drop counter and last error metrics into management API\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nExposes the counter of events dropped from the DLQ, and the last error reason.\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\nAdds some metrics to the dead_letter_queue part of the monitoring endpoint `_node/stats/pipelines/`, precisely under the path `pipelines.<pipeline name>.dead_letter_queue`. The metrics added are:\r\n- `dropped_events`: count the number of dropped events caused by \"queue full condition\", when `drop_newer` storage policy is enabled, happened to this DLQ since the last restart of Logstash process.\r\n- `last_error`: a string reporting the last error registered for DLQ dropping condition.\r\n-  `max_queue_size`: like for PQ it's the maximum size that the DLQ can reach.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\nThe user can monitor the size of the DLQ, the counter of dropped events and the last error message string.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] Run a DLQ Logstash pipeline against an always rejecting ES and check with HTTP API the data.\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\nEnable DLQ on `logstash.yml`, use an ES with a closed index (to trigger 404 errors) and use a pipeline to push data into ES closed index. Monitor the HTTP endpoint.\r\n\r\n- Enable DLQ in `logstash.yml` with:\r\n```\r\ndead_letter_queue.enable: true\r\ndead_letter_queue.storage_policy: drop_older\r\ndead_letter_queue.max_bytes: 50mb\r\n```\r\n- close an index (`test_index`) in an ES instance\r\n```\r\nPOST test_index/_close\r\n```\r\nto reopen:\r\n```\r\nPOST test_index/_open\r\n```\r\n- create the sender pipeline:\r\n```\r\ninput {\r\n  generator {\r\n    message => '{\"name\": \"John\", \"surname\": \"Doe\"}'\r\n    codec => json\r\n  }\r\n}\r\n\r\noutput {\r\n  elasticsearch {\r\n    index => \"test_index\"\r\n    hosts => \"http://localhost:9200\"\r\n    user => \"elastic\"\r\n    password => \"changeme\"\r\n  }\r\n}\r\n```\r\n- set `pipeline.yml` with\r\n```\r\n- pipeline.id: test_dlq_upstream\r\n  path.config: \"/tmp/dlq_upstream.conf\"\r\n```\r\n- run logstash `bin/logstash`\r\n- check the monitoring endpoint:\r\n```\r\ncurl 'localhost:9600/_node/stats/pipelines/test_dlq_upstream' | jq .pipelines.test_dlq_upstream.dead_letter_queue\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fixes #14010\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\nA user which enabled DLQ needs to monitor the behavior of the queue to understand when eventually the messages are dropped, and lost without possibility to reprocess.\r\n\r\n", "edit_functions": ["logstash-core/src/main/java/org/logstash/common/io/DeadLetterQueueWriter.java", "logstash-core/src/main/java/org/logstash/execution/AbstractPipelineExt.java"]}
{"id": 29, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14045", "base_commit": "25796737c3351610cfdd2c55f0b3710b30b11c44", "patch": "", "problem_statement": "Add complex password policy on basic auth\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\n## Communication\r\nPlease refer to #14000 PR for the history. I closed that PR since upstream git merge messed file changes (tried several git solutions but still merged file changes appear).\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\nCurrently, when using HTTP basic authentification, Logstash accepts any password user sets. However, this leads to security vulnerability in case of guessing the password. In this change, we are introducing complex password policy which, when using HTTP basic auth, Logstash validates password at LS startup.\r\nValidation policies are based on security institutions recommendation such as [NIST.SP.800-63b](https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-63b.pdf), [OWASP](https://github.com/OWASP/www-community/blob/master/pages/OWASP_Validation_Regex_Repository.md)\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\nWhen using HTTP basic authentification, Logstash accepts any password users set. However, some use cases strongly require to set complex passwords to protect the Logstash data leak. In this complex policy validator change, Logstash requires strong password when using the HTTP basic auth.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- [x] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n - Add policy configuration to `logstash.yml`\r\n```\r\n# ------------ Password Policy --------------\r\npassword_policy.mode: WARN\r\npassword_policy:\r\n  length:\r\n    minimum: 8\r\n  include:\r\n    upper: REQUIRED\r\n    lower: REQUIRED\r\n    digit: REQUIRED\r\n    symbol: REQUIRED\r\n```\r\n\r\n- Invalid password use cases\r\n   - Set `api.auth.type: basic` in `logstash.yml`\r\n   - Setup simple password eg. `Password`\r\n   - Run the Logstash with `./bin/logstash` command\r\n   - We get invalid password error message with explanations, \r\n   ```\r\n    Password must contain at least one special character., Password must contain at least one digit between 0 and 9.]>\r\n    ```\r\n- Valid password use cases\r\n   - Set `api.auth.type: basic` in `logstash.yml`\r\n   - Setup complex password eg. `Passwor123$!d`\r\n   - Run the Logstash with `./bin/logstash` command\r\n   - We don't get any errors related to password validation \r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #13884\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n   #### HTTP basic auth used\r\n     Scenario: Invalid password use cases\r\n       Enable `basic` HTTP auth in `logstash.yml`\r\n       Setup simple password eg. `Password`\r\n       Customer gets invalid password error message with explanations, such as it does not contain digit or special char(s).\r\n     Scenario: Valid password use cases\r\n       Enable `basic` HTTP auth in `logstash.yml`\r\n       Setup a complex password eg. `Passwor123$d`\r\n       Customer will not face any issue when run the Logstash\r\n       Monitoring APIs will respond properly\r\n         HTTP 401 if password incorrect\r\n         HTTP 200 with data if correct password\r\n\r\n   #### HTTP basic auth not used\r\n     Any of added logic will not be executed.\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n\r\n- When using invalid password\r\n```\r\n// when password_policy.mode: WARN\r\n[2022-04-21T17:21:14,789][FATAL][logstash.runner          ] An unexpected error occurred! {:error=>#<ArgumentError: Password must contain at least one upper case, must contain at least one digit between 0 and 9, must contain at least one special character.\r\n\r\n// when password_policy.mode: ERROR\r\n[2022-04-21T17:17:58,682][WARN ][logstash.settings        ] Password must contain at least one upper case, must contain at least one digit between 0 and 9, must contain at least one special character.        \r\n\r\n```", "edit_functions": ["config/logstash.yml", "docker/data/logstash/env2yaml/env2yaml.go", "docs/static/settings-file.asciidoc", "logstash-core/lib/logstash/agent.rb", "logstash-core/lib/logstash/environment.rb", "logstash-core/lib/logstash/settings.rb", "logstash-core/lib/logstash/util/password.rb", "logstash-core/lib/logstash/webserver.rb", "logstash-core/spec/logstash/settings_spec.rb", "logstash-core/spec/logstash/webserver_spec.rb", "logstash-core/src/main/java/org/logstash/secret/password/DigitValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/EmptyStringValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/LengthValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/LowerCaseValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordParamConverter.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordPolicyParam.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordPolicyType.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/SymbolValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/UpperCaseValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/Validator.java"]}
{"id": 30, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14027", "base_commit": "96f7e2949d4f8a3b3e198fa3775ccd107ee63d03", "patch": "", "problem_statement": "Introduce a retry mechanism in pipeline-to-pipeline\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\nIntroduce a retry mechanism in pipeline-to-pipeline to avoid that the downstream input stops the upstream one.\r\n\r\n## What does this PR do?\r\n\r\nUpdates the `internalReceive` method implementation in Pipeline Input to catch exception error and return the position where the stream was interrupted. Modify the EventBus's batch events forwarding logic to handle errors from Pipeline Input and apply rtray logic only from last error position in the batch of events.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nMake more robust the handling of error in pipeline-to-pipeline use case, avoiding the failure of upstream pipeline when there are problems on the downstream pipeline input plugin. Suppose there is any IO error in inserting into PQ on the downstream pipeline, then with this fix the upstream continues to forward messages to the downstream without duplication of events. \r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [x] test with a real use case of failing PQ.\r\n\r\n## How to test this PR locally\r\n\r\nTo test is necessary to create an error in the downstream pipeline input. To realize this the idea is to write an event that is bigger then PQ page size and enable the PQ for the downstream pipeline.\r\nSuch pipelines should be:\r\n\r\n**upstream**\r\n```\r\ninput {\r\n  tcp {\r\n    port => 5554\r\n    codec => plain\r\n  }\r\n}\r\n\r\noutput {\r\n  pipeline {\r\n    send_to => [downstream_pq]\r\n  }\r\n}\r\n```\r\n**downstream**\r\n```\r\ninput {\r\n  pipeline {\r\n    address => downstream_pq\r\n  }\r\n}\r\noutput {\r\n  stdout {\r\n    codec => dots\r\n  }\r\n}\r\n```\r\nand cat a big file into the upstream with:\r\n```\r\ncat pq_test_file.txt | netcat localhost 5554\r\n```\r\n\r\nTo generate such big file, use the script:\r\n```ruby\r\nputs \"Writing content to file\"\r\n\r\nFile.open('pq_test_file.txt', \"w\") do |f|\r\n  f.write \"This is the first line and should be stored in the PQ.\\n\"\r\n  first_chars = \"This is a 64Mb long line, which makes the PQ to explode because payload is greater than page size aka capacity.\"\r\n  f.write first_chars\r\n  counter = 1\r\n  max_len = first_chars.length\r\n  while max_len < 64 * 1024 * 1024 do\r\n    content = counter.to_s + \".\"\r\n    counter = counter + 1\r\n    max_len = max_len + content.length\r\n    f.write content\r\n  end\r\nend\r\n\r\nputs \"Done.\"\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fixes #12005\r\n\r\n## Use cases\r\nAs a user I wan that in pipeline-to-pipeline an event bigger then PQ page size doesn't crush the upstream pipeline but continues to retry.\r\n\r\n", "edit_functions": ["logstash-core/lib/logstash/plugins/builtin/pipeline/input.rb", "logstash-core/spec/logstash/plugins/builtin/pipeline_input_output_spec.rb", "logstash-core/src/main/java/org/logstash/ackedqueue/Page.java", "logstash-core/src/main/java/org/logstash/plugins/pipeline/PipelineBus.java", "logstash-core/src/main/java/org/logstash/plugins/pipeline/PipelineInput.java", "logstash-core/src/main/java/org/logstash/plugins/pipeline/ReceiveResponse.java"]}
{"id": 31, "repo": "elastic/logstash", "instance_id": "elastic__logstash-14000", "base_commit": "5392ad7511b89e1df966dad24c89c1b89a5dcb26", "patch": "", "problem_statement": "Apply complex password policy on HTTP basic auth.\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\nCurrently, when using HTTP basic authentification, Logstash accepts any password user sets. However, this leads to security vulnerability in case of guessing the password. In this change, we are introducing complex password policy which, when using HTTP basic auth, Logstash validates password at LS startup.\r\nValidation policies are based on security institutions recommendation such as [NIST.SP.800-63b](https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-63b.pdf), [OWASP](https://github.com/OWASP/www-community/blob/master/pages/OWASP_Validation_Regex_Repository.md)\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\nWhen using HTTP basic authentification, Logstash accepts any password users set. However, some use cases strongly require to set complex passwords to protect the Logstash data leak. In this complex policy validator change, Logstash requires strong password when using the HTTP basic auth.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [x] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n - Add policy configuration to `logstash.yml`\r\n```\r\n# ------------ Password Policy --------------\r\npassword_policy.mode: WARN\r\npassword_policy:\r\n  length:\r\n    minimum: 8\r\n  include:\r\n    upper: REQUIRED\r\n    lower: REQUIRED\r\n    digit: REQUIRED\r\n    symbol: REQUIRED\r\n```\r\n\r\n- Invalid password use cases\r\n   - Set `api.auth.type: basic` in `logstash.yml`\r\n   - Setup simple password eg. `Password`\r\n   - Run the Logstash with `./bin/logstash` command\r\n   - We get invalid password error message with explanations, \r\n   ```\r\n    Password must contain at least one special character., Password must contain at least one digit between 0 and 9.]>\r\n    ```\r\n- Valid password use cases\r\n   - Set `api.auth.type: basic` in `logstash.yml`\r\n   - Setup complex password eg. `Passwor123$!d`\r\n   - Run the Logstash with `./bin/logstash` command\r\n   - We don't get any errors related to password validation \r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Closes #13884\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n   #### HTTP basic auth used\r\n     Scenario: Invalid password use cases\r\n       Enable `basic` HTTP auth in `logstash.yml`\r\n       Setup simple password eg. `Password`\r\n       Customer gets invalid password error message with explanations, such as it does not contain digit or special char(s).\r\n     Scenario: Valid password use cases\r\n       Enable `basic` HTTP auth in `logstash.yml`\r\n       Setup a complex password eg. `Passwor123$d`\r\n       Customer will not face any issue when run the Logstash\r\n       Monitoring APIs will respond properly\r\n         HTTP 401 if password incorrect\r\n         HTTP 200 with data if correct password\r\n\r\n   #### HTTP basic auth not used\r\n     Any of added logic will not be executed.\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n\r\n- When using invalid password\r\n```\r\n// when password_policy.mode: WARN\r\n[2022-04-21T17:21:14,789][FATAL][logstash.runner          ] An unexpected error occurred! {:error=>#<ArgumentError: Password must contain at least one upper case, must contain at least one digit between 0 and 9, must contain at least one special character.\r\n\r\n// when password_policy.mode: ERROR\r\n[2022-04-21T17:17:58,682][WARN ][logstash.settings        ] Password must contain at least one upper case, must contain at least one digit between 0 and 9, must contain at least one special character.        \r\n\r\n```", "edit_functions": [".github/workflows/add-docs-preview-link.yml", "CONTRIBUTING.md", "ci/logstash_releases.json", "docs/index.asciidoc", "docs/static/configuration.asciidoc", "docs/static/env-vars.asciidoc", "docs/static/event-data.asciidoc", "docs/static/jvm.asciidoc", "docs/static/pipeline-config-exps.asciidoc", "docs/static/pipeline-configuration.asciidoc", "docs/static/pipeline-structure.asciidoc", "docs/static/security/es-security.asciidoc", "logstash-core/build.gradle", "logstash-core/lib/logstash/agent.rb", "logstash-core/lib/logstash/environment.rb", "logstash-core/lib/logstash/pipeline_action.rb", "logstash-core/lib/logstash/pipeline_action/stop_and_delete.rb", "logstash-core/lib/logstash/plugins/registry.rb", "logstash-core/lib/logstash/settings.rb", "logstash-core/lib/logstash/state_resolver.rb", "logstash-core/lib/logstash/util/password.rb", "logstash-core/lib/logstash/webserver.rb", "logstash-core/logstash-core.gemspec", "logstash-core/spec/logstash/agent/converge_spec.rb", "logstash-core/spec/logstash/settings_spec.rb", "logstash-core/spec/logstash/state_resolver_spec.rb", "logstash-core/spec/support/matchers.rb", "logstash-core/src/main/java/org/logstash/Rubyfier.java", "logstash-core/src/main/java/org/logstash/config/ir/CompiledPipeline.java", "logstash-core/src/main/java/org/logstash/config/ir/compiler/EventCondition.java", "logstash-core/src/main/java/org/logstash/config/ir/expression/ExpressionSubstitution.java", "logstash-core/src/main/java/org/logstash/config/ir/expression/ValueExpression.java", "logstash-core/src/main/java/org/logstash/plugins/AliasRegistry.java", "logstash-core/src/main/java/org/logstash/plugins/ConfigVariableExpander.java", "logstash-core/src/main/java/org/logstash/plugins/discovery/PluginRegistry.java", "logstash-core/src/main/java/org/logstash/plugins/factory/PluginFactoryExt.java", "logstash-core/src/main/java/org/logstash/secret/SecretVariable.java", "logstash-core/src/main/java/org/logstash/secret/password/DigitValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/EmptyStringValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/LengthValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/LowerCaseValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordParamConverter.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordPolicyParam.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordPolicyType.java", "logstash-core/src/main/java/org/logstash/secret/password/PasswordValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/SymbolValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/UpperCaseValidator.java", "logstash-core/src/main/java/org/logstash/secret/password/Validator.java", "qa/integration/specs/env_variables_condition_spec.rb"]}
{"id": 32, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13997", "base_commit": "7b2bec2e7a8cd11bcde34edec229792822037893", "patch": "", "problem_statement": "Fix/avoid leak secrects in debug log of ifs\n## Release notes\r\nFix the leak of secret store secrets when if statements are printed when started with debug log.\r\n\r\n## What does this PR do?\r\nUpdates the `ConfigVariableExpander.expand` to selectively create `SecretVariable` instances for SecretStore resolved environment variables.\r\n`SecretVariable` instances in if statements are decrypted during `eq` `EventCondition` compilation; bringing the secret value and using in the comparator.\r\n\r\n## Why is it important/What is the impact to the user?\r\nPermit the user to avoid leakage into debug log of secret stores's variables, when used in if conditions.\r\n\r\n## Checklist\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n- [x] test with a pipeline and debug log enabled. No leak but the condition should work as expected\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n- create a local secret store\r\n```\r\nbin/logstash-keystore create and save into a variable named `SECRET`\r\nbin/logstash-keystore add SECRET\r\n```\r\n- run Logstash in debug with a pipeline that uses the secret variable\r\n```\r\ninput { http { } }\r\n\r\nfilter {\r\n  if [@metadata][input][http][request][headers][auth] != \"${SECRET}\" {\r\n    mutate {\r\n      add_field => { \"a_secre_field\" => \"${SECRET}\" }\r\n      add_tag => \"${SECRET}\"\r\n    }\r\n    drop {}\r\n  } \r\n}\r\n\r\n\r\noutput {\r\n  stdout {codec => rubydebug {metadata => true}}\r\n}\r\n```\r\n- verify your secret isn't leak into the logs (run `bin/logstash -f <pipeline.conf> --debug`)\r\n- verify the pipeline works as expected\r\n```\r\ncurl -v  --header \"auth: s3cr3t\" \"localhost:8080\"\r\n```\r\nan event should be logged to the console.\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fixes #13685\r\n\r\n## Use cases\r\nA user would like to use secret store's resolved variables and avoid to leak in logs/console when Logstash is run with debug or trace levels.\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\nExample of secret disclosure launching `bin/logstash --debug`:\r\n```\r\n[2022-04-14T15:40:21,845][INFO ][logstash.javapipeline    ][main] Starting pipeline {:pipeline_id=>\"main\", \"pipeline.workers\"=>12, \"pipeline.batch.size\"=>125, \"pipeline.batch.delay\"=>50, \"pipeline.max_inflight\"=>1500, \"pipeline.sources\"=>[\"/home/<USER>/workspace/logstash_andsel/leak_secret_in_debug_pipeline.conf\"], :thread=>\"#<Thread:0xab4113a run>\"}\r\n[2022-04-14T15:40:22,249][DEBUG][org.logstash.config.ir.CompiledPipeline][main] Compiled conditional\r\n [if (event.getField('[@metadata][input][http][request][headers][auth]')!='s3cr3t')] \r\n into \r\n org.logstash.config.ir.compiler.ComputeStepSyntaxElement@9fb449bc\r\n```", "edit_functions": ["logstash-core/src/main/java/org/logstash/Rubyfier.java", "logstash-core/src/main/java/org/logstash/config/ir/CompiledPipeline.java", "logstash-core/src/main/java/org/logstash/config/ir/compiler/EventCondition.java", "logstash-core/src/main/java/org/logstash/config/ir/expression/ExpressionSubstitution.java", "logstash-core/src/main/java/org/logstash/config/ir/expression/ValueExpression.java", "logstash-core/src/main/java/org/logstash/plugins/ConfigVariableExpander.java", "logstash-core/src/main/java/org/logstash/secret/SecretVariable.java", "qa/integration/specs/env_variables_condition_spec.rb"]}
{"id": 33, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13931", "base_commit": "7df02cc828c894a619687a41a7ff961461c276d3", "patch": "", "problem_statement": "Backport PR #13902 to 7.17: add backoff to checkpoint write\n**Backport PR #13902 to 7.17 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\nEnable retry by default for failure of checkpoint write, which has been seen in Microsoft Windows platform\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\n\r\n- Change the default value of `queue.checkpoint.retry` to `true` meaning retry the checkpoint write failure by default.\r\n- Add a deprecation warning message for `queue.checkpoint.retry`. The plan is to remove `queue.checkpoint.retry` in near future.\r\n- Change the one-off retry to multiple retries with exponential backoff\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\n\r\nThe retry is mitigation of AccessDeniedException in Windows. There are reports that retrying one more time is not enough. The exception can be triggered by using file explorer viewing the target folder or activity of antivirus. A effective solution is to retry a few more times.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- [x] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n-  Fix #12345\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["docs/static/settings-file.asciidoc", "logstash-core/lib/logstash/environment.rb", "logstash-core/src/main/java/org/logstash/ackedqueue/io/FileCheckpointIO.java", "logstash-core/src/main/java/org/logstash/util/CheckedSupplier.java", "logstash-core/src/main/java/org/logstash/util/ExponentialBackoff.java"]}
{"id": 34, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13930", "base_commit": "94a7aa33577ecdef4be5e3efef1755bb766ecc74", "patch": "", "problem_statement": "Backport PR #13902 to 8.1: add backoff to checkpoint write\n**Backport PR #13902 to 8.1 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\nEnable retry by default for failure of checkpoint write, which has been seen in Microsoft Windows platform\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\n\r\n- Change the default value of `queue.checkpoint.retry` to `true` meaning retry the checkpoint write failure by default.\r\n- Add a deprecation warning message for `queue.checkpoint.retry`. The plan is to remove `queue.checkpoint.retry` in near future.\r\n- Change the one-off retry to multiple retries with exponential backoff\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\n\r\nThe retry is mitigation of AccessDeniedException in Windows. There are reports that retrying one more time is not enough. The exception can be triggered by using file explorer viewing the target folder or activity of antivirus. A effective solution is to retry a few more times.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- [x] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n-  Fix #12345\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["docs/static/settings-file.asciidoc", "logstash-core/lib/logstash/environment.rb", "logstash-core/src/main/java/org/logstash/ackedqueue/io/FileCheckpointIO.java", "logstash-core/src/main/java/org/logstash/util/CheckedSupplier.java", "logstash-core/src/main/java/org/logstash/util/ExponentialBackoff.java"]}
{"id": 35, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13914", "base_commit": "86cdc7a38e7571ae2592fe0f206c8c1b5521a4de", "patch": "", "problem_statement": "Backport PR #13880 to 8.1: Print bundled jdk's version in launch scripts when `LS_JAVA_HOME` is provided\n**Backport PR #13880 to 8.1 branch, original message:**\n\n---\n\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nExtracts the bundled JDK's version into a one-line text file which could easily read and printed from bash/batch scripts.\r\nUpdates Logstash's bash/batch launcher scripts to print the bundled JDK version when warn the user about his override.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nGive information about the JDK version that is bundled with distribution, so that he can immediately compare which environment's provided one.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] run under Bash and on Windows cmd\r\n- [x]  verify `rake artifact:archives` generate packages containing the jdk version file\r\n- [x] check the MacOS tar.gz pack, contains the jdk version file and setting `LS_JAVA_HOME` report the bundled version.\r\n- [x] verifies packages with JDK contanis the \"JDK version file\" while the other doesn't.\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n- checkout this branch\r\n- build archives packs with: \r\n```\r\nrake artifact:archives\r\n```\r\n- set an `LS_JAVA_HOME` to locally installed JDK\r\n- unpack the archive for your OS and run \r\n```\r\nbin/logstash -e \"input{ stdin { } } output { stdout { codec => rubydebug } }\"\r\n```\r\n- verify packages with JDK contains the JDK_VERSION file, and the one without doesn't.\r\n```\r\ntar -tvf build/logstash-8.2.0-SNAPSHOT-linux-x86_64.tar.gz | grep JDK*\r\n```\r\n- verify `deb`/`rpm` distribution packages with JDK contains the JDK_VERSION file, and the one without doesn't.\r\n```\r\ndpkg -c ./build/logstash-8.2.0-SNAPSHOT-amd64.deb | grep JDK*\r\nrpm -qlp ./build/logstash-8.2.0-SNAPSHOT-amd64.deb | grep JDK*\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fixes #13205\r\n\r\n## Use cases\r\n\r\nA user has customized `LS_JAVA_HOME` to be used from Logstash. When Logstash start prints a warning message about this, showing the version it would otherwise use.\r\n\r\n\r\n", "edit_functions": ["bin/logstash.lib.sh", "bin/setup.bat", "build.gradle", "buildSrc/src/main/groovy/org/logstash/gradle/tooling/ExtractBundledJdkVersion.groovy", "buildSrc/src/main/groovy/org/logstash/gradle/tooling/ToolingUtils.groovy", "rakelib/artifacts.rake"]}
{"id": 36, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13902", "base_commit": "32675c1a88bd3393e3f8d6d9275217d2f3891e66", "patch": "", "problem_statement": "add backoff to checkpoint write\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n\r\nEnable retry by default for failure of checkpoint write, which has been seen in Microsoft Windows platform\r\n\r\n## What does this PR do?\r\n\r\n<!-- Mandatory\r\nExplain here the changes you made on the PR. Please explain the WHAT: patterns used, algorithms implemented, design architecture, message processing, etc.\r\n\r\nExample:\r\n  Expose 'xpack.monitoring.elasticsearch.proxy' in the docker environment variables and update logstash.yml to surface this config option.\r\n  \r\n  This commit exposes the 'xpack.monitoring.elasticsearch.proxy' variable in the docker by adding it in env2yaml.go, which translates from\r\n  being an environment variable to a proper yaml config.\r\n  \r\n  Additionally, this PR exposes this setting for both xpack monitoring & management to the logstash.yml file.\r\n-->\r\n\r\n- Change the default value of `queue.checkpoint.retry` to `true` meaning retry the checkpoint write failure by default.\r\n- Add a deprecation warning message for `queue.checkpoint.retry`. The plan is to remove `queue.checkpoint.retry` in near future.\r\n- Change the one-off retry to multiple retries with exponential backoff\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\n<!-- Mandatory\r\nExplain here the WHY or the IMPACT to the user, or the rationale/motivation for the changes.\r\n\r\nExample:\r\n  This PR fixes an issue that was preventing the docker image from using the proxy setting when sending xpack monitoring information.\r\n  and/or\r\n  This PR now allows the user to define the xpack monitoring proxy setting in the docker container.\r\n-->\r\n\r\nThe retry is mitigation of AccessDeniedException in Windows. There are reports that retrying one more time is not enough. The exception can be triggered by using file explorer viewing the target folder or activity of antivirus. A effective solution is to retry a few more times.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [x] I have made corresponding changes to the documentation\r\n- [x] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [ ]\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n-  Fix #12345\r\n\r\n## Use cases\r\n\r\n<!-- Recommended\r\nExplain here the different behaviors that this PR introduces or modifies in this project, user roles, environment configuration, etc.\r\n\r\nIf you are familiar with Gherkin test scenarios, we recommend its usage: https://cucumber.io/docs/gherkin/reference/\r\n-->\r\n\r\n## Screenshots\r\n\r\n<!-- Optional\r\nAdd here screenshots about how the project will be changed after the PR is applied. They could be related to web pages, terminal, etc, or any other image you consider important to be shared with the team.\r\n-->\r\n\r\n## Logs\r\n\r\n<!-- Recommended\r\nPaste here output logs discovered while creating this PR, such as stack traces or integration logs, or any other output you consider important to be shared with the team.\r\n-->\r\n", "edit_functions": ["docs/static/settings-file.asciidoc", "logstash-core/lib/logstash/environment.rb", "logstash-core/src/main/java/org/logstash/ackedqueue/io/FileCheckpointIO.java", "logstash-core/src/main/java/org/logstash/util/CheckedSupplier.java", "logstash-core/src/main/java/org/logstash/util/ExponentialBackoff.java"]}
{"id": 37, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13880", "base_commit": "27dc80f7e12e1c27b65ec138c0abc177a9780c05", "patch": "", "problem_statement": "Print bundled jdk's version in launch scripts when `LS_JAVA_HOME` is provided\n<!-- Type of change\r\nPlease label this PR with the release version and one of the following labels, depending on the scope of your change:\r\n- bug\r\n- enhancement\r\n- breaking change\r\n- doc\r\n-->\r\n\r\n## Release notes\r\n<!-- Add content to appear in  [Release Notes](https://www.elastic.co/guide/en/logstash/current/releasenotes.html), or add [rn:skip] to leave this PR out of release notes -->\r\n[rn:skip]\r\n\r\n## What does this PR do?\r\n\r\nExtracts the bundled JDK's version into a one-line text file which could easily read and printed from bash/batch scripts.\r\nUpdates Logstash's bash/batch launcher scripts to print the bundled JDK version when warn the user about his override.\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nGive information about the JDK version that is bundled with distribution, so that he can immediately compare which environment's provided one.\r\n\r\n## Checklist\r\n\r\n<!-- Mandatory\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n\r\nList here all the items you have verified BEFORE sending this PR. Please DO NOT remove any item, striking through those that do not apply. (Just in case, strikethrough uses two tildes. ~~Scratch this.~~)\r\n-->\r\n\r\n- [x] My code follows the style guidelines of this project\r\n- [x] I have commented my code, particularly in hard-to-understand areas\r\n- ~~[ ] I have made corresponding changes to the documentation~~\r\n- ~~[ ] I have made corresponding change to the default configuration files (and/or docker env variables)~~\r\n- [x] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n<!-- Recommended\r\nAdd a checklist of things that are required to be reviewed in order to have the PR approved\r\n-->\r\n- [x] run under Bash and on Windows cmd\r\n- [x]  verify `rake artifact:archives` generate packages containing the jdk version file\r\n- [x] check the MacOS tar.gz pack, contains the jdk version file and setting `LS_JAVA_HOME` report the bundled version.\r\n- [x] verifies packages with JDK contanis the \"JDK version file\" while the other doesn't.\r\n\r\n## How to test this PR locally\r\n\r\n<!-- Recommended\r\nExplain here how this PR will be tested by the reviewer: commands, dependencies, steps, etc.\r\n-->\r\n- checkout this branch\r\n- build archives packs with: \r\n```\r\nrake artifact:archives\r\n```\r\n- set an `LS_JAVA_HOME` to locally installed JDK\r\n- unpack the archive for your OS and run \r\n```\r\nbin/logstash -e \"input{ stdin { } } output { stdout { codec => rubydebug } }\"\r\n```\r\n- verify packages with JDK contains the JDK_VERSION file, and the one without doesn't.\r\n```\r\ntar -tvf build/logstash-8.2.0-SNAPSHOT-linux-x86_64.tar.gz | grep JDK*\r\n```\r\n- verify `deb`/`rpm` distribution packages with JDK contains the JDK_VERSION file, and the one without doesn't.\r\n```\r\ndpkg -c ./build/logstash-8.2.0-SNAPSHOT-amd64.deb | grep JDK*\r\nrpm -qlp ./build/logstash-8.2.0-SNAPSHOT-amd64.deb | grep JDK*\r\n```\r\n\r\n## Related issues\r\n\r\n<!-- Recommended\r\nLink related issues below. Insert the issue link or reference after the word \"Closes\" if merging this should automatically close it.\r\n\r\n- Closes #123\r\n- Relates #123\r\n- Requires #123\r\n- Superseeds #123\r\n-->\r\n- Fixes #13205\r\n\r\n## Use cases\r\n\r\nA user has customized `LS_JAVA_HOME` to be used from Logstash. When Logstash start prints a warning message about this, showing the version it would otherwise use.\r\n\r\n\r\n", "edit_functions": ["bin/logstash.lib.sh", "bin/setup.bat", "build.gradle", "buildSrc/src/main/groovy/org/logstash/gradle/tooling/ExtractBundledJdkVersion.groovy", "buildSrc/src/main/groovy/org/logstash/gradle/tooling/ToolingUtils.groovy", "rakelib/artifacts.rake"]}
{"id": 38, "repo": "elastic/logstash", "instance_id": "elastic__logstash-13825", "base_commit": "d64248f62837efb4b69de23539e350be70704f38", "patch": "", "problem_statement": "[Java17] Add `--add-export` settings to restore JDK17 compatibility\nAfter #13700 updated google-java-format dependency, it is now required to add a number of `--add-export` flags in order to run on JDK17. This commit adds these flags to the jvm options for a running logstash, and to the tests running on gradle to enable tests to still work\r\n\r\n## Release notes\r\nNote: If you use custom `jvm.options`, you will need to add the following settings to `config.jvm.options` to allow Logstash to start:\r\n\r\n```\r\n11-:--add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED\r\n11-:--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED\r\n11-:--add-exports=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED\r\n11-:--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED\r\n11-:--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED\r\n```\r\n\r\n\r\n\r\n## Why is it important/What is the impact to the user?\r\n\r\nWithout these additional settings, logstash will not start, and tests will fail.\r\n\r\n## Checklist\r\n\r\n- [ ] My code follows the style guidelines of this project\r\n- [ ] I have commented my code, particularly in hard-to-understand areas\r\n- [ ] I have made corresponding changes to the documentation\r\n- [ ] I have made corresponding change to the default configuration files (and/or docker env variables)\r\n- [ ] I have added tests that prove my fix is effective or that my feature works\r\n\r\n## Author's Checklist\r\n\r\n- [ ] This should be tested using the JDK matrix test - however, until https://github.com/elastic/infra/pull/34918 is committed, tests will incorrectly pass\r\n\r\n## How to test this PR locally\r\n\r\n- [ ] Ensure that JDK17 is installed and set using `LS_JAVA_HOME`\r\n- [ ] Run `bin/logstash` with a simple pipeline including at least one filter\r\n- [ ] Also run unit and integration tests, ensuring that JDK17 is being used\r\n\r\n## Related issues\r\n\r\nCloses #13819\r\nRelates #13700 \r\nRelates https://github.com/logstash-plugins/logstash-filter-kv/issues/98 \r\n\r\n", "edit_functions": ["build.gradle", "config/jvm.options", "logstash-core/src/main/java/org/logstash/launchers/JvmOptionsParser.java"]}
