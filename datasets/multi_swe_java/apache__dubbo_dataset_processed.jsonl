{"id": 1, "repo": "apache/dubbo", "instance_id": "apache__dubbo-11781", "base_commit": "d0a1bd014331483c19208b831c4f6b488654a508", "patch": "", "problem_statement": "Fix #11767,  restore the original parameter pair instead of giving default value when doing URL.parse.\n## What is the purpose of the change\r\n\r\nWhen value has no value, make sure value is an empty string, rather than assigning the value of name to value\r\n\r\nFor key-value pair `key_name=`, the generated URL parameter should be 'key_name=' rather than `key_name=key_ name`\r\n\r\n## Brief changelog\r\n\r\n## Verifying this change\r\n\r\n## Checklist\r\n- [x] Make sure there is a [GitHub_issue](https://github.com/apache/dubbo/issues/11767) \r\n\r\n", "edit_functions": ["dubbo-common/src/main/java/org/apache/dubbo/common/URLStrParser.java"]}
{"id": 2, "repo": "apache/dubbo", "instance_id": "apache__dubbo-10638", "base_commit": "ddd1786578438c68f1f6214bcab600a299245d7d", "patch": "", "problem_statement": "Fix PojoUtils support localdatetime,lcaldate,localtime serializable and deserialize\nfixed #10631 \r\n\r\n## What is the purpose of the change\r\n\r\n\r\n\r\n## Brief changelog\r\n\r\n\r\n## Verifying this change\r\n\r\n\r\n<!-- Follow this checklist to help us incorporate your contribution quickly and easily: -->\r\n\r\n## Checklist\r\n- [x] Make sure there is a [GitHub_issue](https://github.com/apache/dubbo/issues) field for the change (usually before you start working on it). Trivial changes like typos do not require a GitHub issue. Your pull request should address just this issue, without pulling in other changes - one PR resolves one issue.\r\n- [ ] Each commit in the pull request should have a meaningful subject line and body.\r\n- [ ] Write a pull request description that is detailed enough to understand what the pull request does, how, and why.\r\n- [ ] Check if is necessary to patch to Dubbo 3 if you are work on Dubbo 2.7\r\n- [ ] Write necessary unit-test to verify your logic correction, more mock a little better when cross module dependency exist. If the new feature or significant change is committed, please remember to add sample in [dubbo samples](https://github.com/apache/dubbo-samples) project.\r\n- [ ] Add some description to [dubbo-website](https://github.com/apache/dubbo-website) project if you are requesting to add a feature.\r\n- [ ] GitHub Actions works fine on your own branch.\r\n- [ ] If this contribution is large, please follow the [Software Donation Guide](https://github.com/apache/dubbo/wiki/Software-donation-guide).\r\n", "edit_functions": ["dubbo-common/src/main/java/org/apache/dubbo/common/utils/CompatibleTypeUtils.java", "dubbo-common/src/main/java/org/apache/dubbo/common/utils/PojoUtils.java"]}
{"id": 3, "repo": "apache/dubbo", "instance_id": "apache__dubbo-7041", "base_commit": "e84cdc217a93f4628415ea0a7d8a9d0090e2c940", "patch": "", "problem_statement": "Fix ReflectUtils not support generic call with Future\n## What is the purpose of the change\r\n\r\nAdd TypeVariable support for ReflectUtils\r\n\r\nFix #7040\r\n\r\nInterface example:\r\n``` java\r\npublic interface TypeClass<T> {\r\n    CompletableFuture<T> getGenericFuture();\r\n}\r\n```", "edit_functions": ["dubbo-common/src/main/java/org/apache/dubbo/common/utils/ReflectUtils.java"]}
