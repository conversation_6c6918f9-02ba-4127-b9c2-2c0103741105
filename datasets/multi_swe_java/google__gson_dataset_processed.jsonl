{"id": 1, "repo": "google/gson", "instance_id": "google__gson-1787", "base_commit": "e614e71ee43ca7bc1cb466bd1eaf4d85499900d9", "patch": "", "problem_statement": "Fix TypeAdapterRuntimeTypeWrapper not detecting reflective TreeTypeAdapter and FutureTypeAdapter\nFixes #543\r\nFixes #2032\r\nFixes #1833\r\n\r\nPreviously on serialization TypeAdapterRuntimeTypeWrapper preferred a TreeTypeAdapter without `serializer` which falls back to the reflective adapter.\r\nThis behavior was incorrect because it caused the reflective adapter for a Base class to be used for serialization (indirectly as TreeTypeAdapter delegate) instead of using the reflective adapter for a Subclass extending Base.", "edit_functions": ["gson/src/main/java/com/google/gson/Gson.java", "gson/src/main/java/com/google/gson/internal/bind/SerializationDelegatingTypeAdapter.java", "gson/src/main/java/com/google/gson/internal/bind/TreeTypeAdapter.java", "gson/src/main/java/com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.java"]}
{"id": 2, "repo": "google/gson", "instance_id": "google__gson-1703", "base_commit": "6d2557d5d1a8ac498f2bcee20e5053c93b33ecce", "patch": "", "problem_statement": "Fix #1702: Gson.toJson creates CharSequence which does not implement toString\nFix #1702", "edit_functions": ["gson/src/main/java/com/google/gson/internal/Streams.java"]}
{"id": 3, "repo": "google/gson", "instance_id": "google__gson-1555", "base_commit": "aa236ec38d39f434c1641aeaef9241aec18affde", "patch": "", "problem_statement": "Fixed nullSafe usage.\nIt is impossible to create a JsonDeserializer that transforms JSON null values. This PR makes it so that a serializer/deserializer for annotated field does get called on nulls when `nullSafe` property of JsonAdapter annotation is false.\r\n\r\nnullSafe is still ignored if adapter is registered via GsonBuilder.\r\n\r\nFixes #1553\r\n\r\nSigned-off-by: Dmitry Bufistov <<EMAIL>>", "edit_functions": ["gson/src/main/java/com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.java", "gson/src/main/java/com/google/gson/internal/bind/TreeTypeAdapter.java"]}
{"id": 4, "repo": "google/gson", "instance_id": "google__gson-1391", "base_commit": "3f4ac29f9112799a7374a99b18acabd0232ff075", "patch": "", "problem_statement": "Fix issue with recursive type variable protections to fix #1390\nWhen a type variable is referenced multiple times it needs to resolve to the same value.  Previously, the second attempt would abort resolution early in order to protect against infinite recursion.\r\n\r\nNOTE: I could use some scrutiny on this as I don't fully understand the implications of all the code branches.  This commit does resolve the issue but stylistically I'm not really sold on breaking out of the while loop in order to capture the final result for subsequent resolution attempts.\r\n\r\nFixes #1390 \r\n", "edit_functions": ["gson/src/main/java/com/google/gson/internal/$Gson$Types.java"]}
{"id": 5, "repo": "google/gson", "instance_id": "google__gson-1093", "base_commit": "0aaef0fd1bb1b9729543dc40168adfb829eb75a4", "patch": "", "problem_statement": "value(double) can write NaN and infinite values when lenient, as value(Number) does\nFixes #1090.", "edit_functions": ["gson/src/main/java/com/google/gson/stream/JsonWriter.java"]}
