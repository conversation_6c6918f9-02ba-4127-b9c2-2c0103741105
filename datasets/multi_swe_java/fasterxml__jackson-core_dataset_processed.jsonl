{"id": 1, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1309", "base_commit": "449ed86748bf672b0a65f13e7f8573298b543384", "patch": "", "problem_statement": "Fix #1308: allow trailing dot for \"Stringified numbers\"\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/io/NumberInput.java"]}
{"id": 2, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1263", "base_commit": "9ed17fc7e9df9203f11ccb17819009ab0a898aa3", "patch": "", "problem_statement": "Fixes #1262: Add diagnostic method pooledCount() in RecyclerPool\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/util/RecyclerPool.java"]}
{"id": 3, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1208", "base_commit": "9438a5ec743342ccd04ae396ce410ef75f08371b", "patch": "", "problem_statement": "Fixes #1207: apply \"maxNameLength\" change to CharsToNameCanonicalizer\nUnfortunately need to remove a `final` case here, but seems like the simplest fix.", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/JsonFactory.java"]}
{"id": 4, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1204", "base_commit": "2fdbf07978813ff40bea88f9ca9961cece59467c", "patch": "", "problem_statement": "Fix #1202: add `RecyclerPool.clear()` method\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/json/ReaderBasedJsonParser.java", "src/main/java/com/fasterxml/jackson/core/util/RecyclerPool.java"]}
{"id": 5, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1182", "base_commit": "d14acac9536939886e432d7145670a9210f54699", "patch": "", "problem_statement": "Fixes #1149: add `JsonParser.getNumberTypeFP()`\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/JsonParser.java", "src/main/java/com/fasterxml/jackson/core/util/JsonParserDelegate.java"]}
{"id": 6, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1172", "base_commit": "fb695545312cc2340604e61642f36138152fba93", "patch": "", "problem_statement": "Fix #1168 (JsonPointer.append() with JsonPointer.tail())\nNone", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/JsonPointer.java"]}
{"id": 7, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1142", "base_commit": "f1dc3c512d211ae3e14fb59af231caebf037d510", "patch": "", "problem_statement": "Fix #1141: prevent NPE in Version.equals()\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/Version.java"]}
{"id": 8, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1053", "base_commit": "bb778a0a4d6d492ca0a39d7d0e32b6e44e90e7aa", "patch": "", "problem_statement": "support snapshot versioning\nThis could change default behavior for clients, so I expect it should be merged into master instead of here.\r\n\r\nI have proposed two PRs, one for the current version, and one for master assuming this is unacceptable.\r\n\r\nI expect and recommend that this PR be closed without merging because it is a non-backwards-compatible change, but wanted to offer it nonetheless.\r\n\r\nCloses #1050 \r\nSame as #1054 but rebased onto `2.16`", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/Version.java"]}
{"id": 9, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-1016", "base_commit": "315ac5ad2ad5228129a3df50623570d9bc2d7045", "patch": "", "problem_statement": "Fix #1015: JsonFactory always respects `CANONICALIZE_FIELD_NAMES`\nPreviously a subset of methods did not check for\r\n`CANONICALIZE_FIELD_NAMES` in the factory features configuration.\r\n\r\nI would appreciate an opinion on whether or not you'd prefer an update to `UTF8StreamJsonParser` to support a non-canonicalizing ByteQuadsCanonicalizer. Currently we don't use this parser with a non-canonicalizing parser because it underperforms on large input and I don't have any intention of changing that, however it may be a footgun to fail if that ever changes in a refactor. Failure would likely cause us to re-discover that the reader-based implementation is a better fit when canonicalization is disabled in the new path which is a plus, but potential instability in the interim wouldn't be ideal. I've left the class as is for now, keeping this change as minimal as possible.", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/JsonFactory.java", "src/main/java/com/fasterxml/jackson/core/json/UTF8DataInputJsonParser.java", "src/main/java/com/fasterxml/jackson/core/json/async/NonBlockingJsonParserBase.java"]}
{"id": 10, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-964", "base_commit": "2b53cce78c6ca037938e6f26d6935c5f9b8c07dd", "patch": "", "problem_statement": "Add `JsonFactory.setStreamReadConstraints()`\nFix #962: add a direct set method for overriding `StreamReadConstraints`, in addition to preferred Builder method.\r\n", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/JsonFactory.java"]}
{"id": 11, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-922", "base_commit": "5956b59a77f9599317c7ca7eaa073cb5d5348940", "patch": "", "problem_statement": "Fix #912: use `requiresPaddingOnRead()` instead of `usesPadding()`\nNone", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/json/ReaderBasedJsonParser.java", "src/main/java/com/fasterxml/jackson/core/json/UTF8DataInputJsonParser.java", "src/main/java/com/fasterxml/jackson/core/json/UTF8StreamJsonParser.java"]}
{"id": 12, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-891", "base_commit": "287ec3223b039f24d2db99809ae04a333b287435", "patch": "", "problem_statement": "filter generator: create child object context when writing start object. fixes #890\nFixes #890.", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/filter/FilteringGeneratorDelegate.java"]}
{"id": 13, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-729", "base_commit": "4465e7a383b4ca33f9a011e1444d67d7f58fca1c", "patch": "", "problem_statement": "Allow TokenFilter to preserve empty\nThis creates two new method on `TokenFilter` which you can override to\r\ndecide if empty arrays and objects should be included or excluded. An\r\noverride like this, for example, will include all arrays and objects\r\nthat were sent empty but strip any arrays or objects that were\r\n*filtered* to be empty:\r\n```\r\n        @Override\r\n        public boolean includeEmptyArray(boolean contentsFiltered) {\r\n            return !contentsFiltered;\r\n        }\r\n\r\n        @Override\r\n        public boolean includeEmptyObject(boolean contentsFiltered) {\r\n            return !contentsFiltered;\r\n        }\r\n```\r\n\r\nThe default to preserve backwards compatibility is to always *exclude*\r\nempty objects.\r\n\r\nCloses #715", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/filter/FilteringParserDelegate.java", "src/main/java/com/fasterxml/jackson/core/filter/TokenFilter.java", "src/main/java/com/fasterxml/jackson/core/filter/TokenFilterContext.java"]}
{"id": 14, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-566", "base_commit": "3b20a1c603cb02b7f499ce28b6030577ad63c0f7", "patch": "", "problem_statement": "Synchronize variants of `JsonGenerator#writeNumberField` with `JsonGenerator#writeNumber`\nFix #565 ", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/JsonGenerator.java"]}
{"id": 15, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-370", "base_commit": "f42556388bb8ad547a55e4ee7cfb52a99f670186", "patch": "", "problem_statement": "Fix #367 (missing code paths for ALLOW_TRAILING_COMMA)\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/json/ReaderBasedJsonParser.java", "src/main/java/com/fasterxml/jackson/core/json/UTF8DataInputJsonParser.java"]}
{"id": 16, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-183", "base_commit": "ac6d8e22847c19b2695cbd7d1f418e07a9a3dbb2", "patch": "", "problem_statement": "Always return empty array instead of null for empty buffer\nFixes #182\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/util/TextBuffer.java"]}
{"id": 17, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-174", "base_commit": "b0f217a849703a453952f93b5999c557c201a4be", "patch": "", "problem_statement": "resolves #172 by adding last operation in JsonPointer.\nAdds `last` method in `JsonPointer` class as described in issue #172 \n", "edit_functions": ["src/main/java/com/fasterxml/jackson/core/JsonPointer.java"]}
{"id": 18, "repo": "fasterxml/jackson-core", "instance_id": "fasterxml__jackson-core-980", "base_commit": "0de50fbc6e9709d0f814fd6e30b6595905f70e63", "patch": "", "problem_statement": "Fix #968: prevent some conversion from BigInteger to BigDecimal for perf reasons\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/core/StreamReadConstraints.java", "src/main/java/com/fasterxml/jackson/core/base/ParserBase.java"]}
