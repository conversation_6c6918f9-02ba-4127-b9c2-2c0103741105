{"id": 1, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4641", "base_commit": "3ed7f4572534383e54f9fd0d2521131f64283410", "patch": "", "problem_statement": "Prioritize constructor parameter over field if both are annotated with `@JsonAnySetter`, to fix #4634\nSwitching the priority not just because doing so happens to fix #4634, but also because it feels weird to prioritise field if both are annotated with `@JsonAnySetter`.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/BeanDeserializerFactory.java"]}
{"id": 2, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4615", "base_commit": "bed90645e269d30b0b94d446f821a3a0f45ce07b", "patch": "", "problem_statement": "Fixes #4584: add AnnotationIntrospector method for default Creator discovery (`findDefaultCreator()`)\nImplements #4584.", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/AnnotationIntrospector.java", "src/main/java/com/fasterxml/jackson/databind/introspect/AnnotationIntrospectorPair.java", "src/main/java/com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector.java", "src/main/java/com/fasterxml/jackson/databind/introspect/PotentialCreator.java"]}
{"id": 3, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4487", "base_commit": "a479197ec08b50dfe01521c95d9d9edcef228395", "patch": "", "problem_statement": "Fix #4443: detect `Iterable` as `IterationType`\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/type/TypeFactory.java"]}
{"id": 4, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4486", "base_commit": "e71e1a227e796abd8a55e8135044133667d6555e", "patch": "", "problem_statement": "Fix #4481: allow override of `READ_UNKNOWN_ENUM_VALUES_AS_NULL`\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/std/EnumDeserializer.java"]}
{"id": 5, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4469", "base_commit": "7c9e7c1e2acd9c55926b9d2592fc65234d9c3ff7", "patch": "", "problem_statement": "Make `FieldProperty` skip nulls on `set()` method\nfixes #4441 (original PR is #4441)\r\n\r\nAs [per comment](https://github.com/FasterXML/jackson-databind/issues/4441#issuecomment-2024353152), the problem seems to be around buffering + ordering of fields (implementing current PR made me believe more so). But since buffer sort of buffers by natural ordering of input JSON, I figured maybe we can just skip nulls within the field itself.\r\n\r\n**PS** : target branch TBD\r\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/impl/FieldProperty.java", "src/main/java/com/fasterxml/jackson/databind/deser/impl/MethodProperty.java"]}
{"id": 6, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4468", "base_commit": "9d31ec7b804e47979cf3d5fc62a5b5c543708a49", "patch": "", "problem_statement": "Fix #4450: deserialize QName \"\" as empty, not null\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/std/FromStringDeserializer.java", "src/main/java/com/fasterxml/jackson/databind/ext/CoreXMLDeserializers.java"]}
{"id": 7, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4426", "base_commit": "f6d2f949c96ed378202c462ebdbaa9ae26a1ec4a", "patch": "", "problem_statement": "Fix #2543: Skip delegating creator arguments when collecting properties\nFixes #2543.", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector.java"]}
{"id": 8, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4365", "base_commit": "18825428cfa704155ec1b4c41aa4d2b42199c866", "patch": "", "problem_statement": "Fix #4364: add `PropertyName.merge()`, use by AnnotationIntrospectorPair\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/AnnotationIntrospector.java", "src/main/java/com/fasterxml/jackson/databind/PropertyName.java", "src/main/java/com/fasterxml/jackson/databind/introspect/AnnotationIntrospectorPair.java"]}
{"id": 9, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4360", "base_commit": "23551ecf0240486c87af36b00a41f8eebf51ecfd", "patch": "", "problem_statement": "Fix #4355: don't fail getting serializer for `Enum` with `toString()`…\n… returning null", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/util/EnumValues.java"]}
{"id": 10, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4338", "base_commit": "93dd44fd9603599ff4b797ae7945a7b9846f4612", "patch": "", "problem_statement": "Fix #4337: support `@JsonSerialize(contentConverter)` with `AtomicReference`\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/ser/std/ReferenceTypeSerializer.java"]}
{"id": 11, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4325", "base_commit": "6b738ac6540556ede1cc0d4ea8e268ab7094918f", "patch": "", "problem_statement": "Fix `NPE` when deserializing `@JsonAnySetter` field in `Throwable`\nfixes #4316 ", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/std/ThrowableDeserializer.java"]}
{"id": 12, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4320", "base_commit": "00b24c6786d993e31f433f3b959a443889e69c56", "patch": "", "problem_statement": "Fix #4309\nfixes #4309 \r\n\r\nPR was made agaisnt 2.17, because there is `_tryToAddNull(JsonParser, DeserializationContext, Collection<?>)` is from 2.17. Either we can backport, or base this PR against 2.16.", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/std/CollectionDeserializer.java"]}
{"id": 13, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4311", "base_commit": "cc6a1ae3a01a5e68387338a3d25c7ba5aa0f30b9", "patch": "", "problem_statement": "Make `PropertyNamingStrategy` skip renaming on `Enum`s\nfixes #4302.\r\n", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector.java"]}
{"id": 14, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4304", "base_commit": "56356fe15bec52f18f0c05b59aa0aafa9ee8e8bf", "patch": "", "problem_statement": "Make TypeIdResolvers serializable for Jackson 2.15\nresolves #4303\r\nblocks #4305", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/jsontype/impl/ClassNameIdResolver.java", "src/main/java/com/fasterxml/jackson/databind/jsontype/impl/TypeNameIdResolver.java"]}
{"id": 15, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4257", "base_commit": "0a2cde800221a58392847d96a28c206ecd99566c", "patch": "", "problem_statement": "Fix `REQUIRE_SETTERS_FOR_GETTERS` taking no effect\nfixes #736 from way back.\r\n\r\n## Notes\r\n\r\n- **PR is against 2.16 branch, in case we want to add to 2.16", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder.java"]}
{"id": 16, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4230", "base_commit": "04daeaba75614f0eec89ec180d3268b1a2f3301d", "patch": "", "problem_statement": "Fix regression from #4008,  optimize `ObjectNode.findValue(s)` and `findParent(s)`\nfixes #4229 .\r\n\r\nReverts only methods that return list that are ... `findValues()`, `findParents()`, `findValuesAsText()`.\r\nThe three methods should not return early, as before.", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/node/ObjectNode.java"]}
{"id": 17, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4228", "base_commit": "8371ce1cb59441d1d90f505d2ac3936c6ca25dd1", "patch": "", "problem_statement": "Fix #4200: use annotations for delegating `@JsonCreator`\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/BeanDeserializerBase.java"]}
{"id": 18, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4219", "base_commit": "c6fd21152af31357f68de2d6344e99b4aab36d7c", "patch": "", "problem_statement": "Allow primitive array deserializer to be captured by `DeserializerModifier`\nfixes #4216 ", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/BasicDeserializerFactory.java"]}
{"id": 19, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4189", "base_commit": "866f95fc0198a5b8beb4f25976125697b115e236", "patch": "", "problem_statement": "Include `BaseDeserializerBase._externalTypeIdHandler` during copy construction\nfixes #4185\r\n\r\n### Summary\r\n\r\n- `BaseDeserializerBase._externalTypeIdHandler` is dropped during `createContextual()`, when `BeanDeserializer.withByNameInclusion()` is called.\r\n\r\n### Modification\r\n\r\n- Copy `src._externalTypeIdHandler` also, during copy construction.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/BeanDeserializerBase.java"]}
{"id": 20, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4186", "base_commit": "b332a4268d25d69ac4603e008d90701cd62d6e4c", "patch": "", "problem_statement": "Fix #4184: setCurrentValue() for empty POJO called at wrong time\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/BeanDeserializer.java"]}
{"id": 21, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4159", "base_commit": "8146fa8191176b5d463fb0d445bc313d777a1483", "patch": "", "problem_statement": "Add new `DefaultTyping.NON_FINAL_AND_ENUMS` to allow Default Typing  for `Enum`s\nAs title says, allow default type handler for Enum's.\r\nFixes #3569 ", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/ObjectMapper.java"]}
{"id": 22, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4132", "base_commit": "042cd3d6f95b86583ffb4cfad0ee1cb251c23285", "patch": "", "problem_statement": "Fix #4096: change `JsonNode.withObject(String)` to accept non-expression argument (property name)\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/JsonNode.java", "src/main/java/com/fasterxml/jackson/databind/node/ObjectNode.java"]}
{"id": 23, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4131", "base_commit": "ebf2a82760fda04fdfd20cb1c3f3e7adf9f6b3b2", "patch": "", "problem_statement": "Fix #4095: add `JsonNode.withObjectProperty()`/`.withArrayProperty()`\nAs per title: add 2 new methods for more convenient/efficient addition of Object/ArrayNodes as immediate properties.\r\n", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/JsonNode.java", "src/main/java/com/fasterxml/jackson/databind/node/BaseJsonNode.java", "src/main/java/com/fasterxml/jackson/databind/node/ObjectNode.java"]}
{"id": 24, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4087", "base_commit": "45e6fa63c412bb47e32693f7b0348b8bc7b246af", "patch": "", "problem_statement": "Fix #4082: add check for attempts to ser/deser Java 8 optionals without module registered\n…ut module", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/util/BeanUtil.java"]}
{"id": 25, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4072", "base_commit": "7d112ec1d74fd50c2ea5a71345e0dedf6e8704a9", "patch": "", "problem_statement": "Ignore `\"message\"` property for deserialization of custom `Throwable`\nresolves #4071 ", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/std/ThrowableDeserializer.java"]}
{"id": 26, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4050", "base_commit": "035fba39559386e16a5017060a6047e733b18599", "patch": "", "problem_statement": "Fix #4047\nFixes #4047 ", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/ObjectMapper.java"]}
{"id": 27, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4048", "base_commit": "5e94cb1b29a5948737d86f5fe7eaeda318b74910", "patch": "", "problem_statement": "Fix #3948: retain `transient` field for ignoral if annotated with `@JsonIgnoral` (or similar)\nSo, resolve a commonly reported (against 2.15) problem where behavior changed to basically make `@JsonIgnore` annotation ignored on `transient` fields (due to 2.15.0 fix for #3862).\r\n\r\nNot backported in 2.15 patch due to chance of regressions.\r\n\r\n", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/MapperFeature.java", "src/main/java/com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector.java"]}
{"id": 28, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4015", "base_commit": "9684204f3073580e711320c3531a95bcaffa63ef", "patch": "", "problem_statement": "Fix #4009: Locale deserialization of empty string (\"\") for as Locale.Root\nNone", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/DeserializationFeature.java", "src/main/java/com/fasterxml/jackson/databind/cfg/CoercionConfigs.java"]}
{"id": 29, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-4013", "base_commit": "badad566edcfb91dfb4c2ba7e2d20b23520e6f6c", "patch": "", "problem_statement": "Fix #4011: add maximum length, nesting limits for canonical type definitions\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/type/TypeParser.java"]}
{"id": 30, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3860", "base_commit": "158a68bf0d03eec407922f1c130816c17e1535ef", "patch": "", "problem_statement": "Enhance `StdNodeBasedDeserializer` to facilitate usage with `ObjectMapper#readerForUpdating`\n- resolves #3814\r\n\r\n## Description\r\n\r\nThis PR enhances `StdNodeBasedDeserializer` to simplify the usage of `ObjectMapper#readerForUpdating` by eliminating the need to manually convert the value to a JsonNode. \r\n\r\n### Changes Made\r\n\r\n- Adds a new `convert(JsonNode, DeserializationContext, T)` method that supports the readerForUpdating method.\r\n- Overrides `deserialize(JsonParser, DeserializationContext, T)` method to support the new convert method.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/std/StdNodeBasedDeserializer.java"]}
{"id": 31, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3716", "base_commit": "0020fcbe578f40810f8e6dea1c89ad48f5e70c15", "patch": "", "problem_statement": "Fix #3711: type id for DEDUCTION case was not working for scalar values\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/jsontype/impl/AsDeductionTypeSerializer.java", "src/main/java/com/fasterxml/jackson/databind/jsontype/impl/StdTypeResolverBuilder.java"]}
{"id": 32, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3701", "base_commit": "dd733c4c5c48e49c4ee7f0bebce3ff939d0bcf03", "patch": "", "problem_statement": "Allow custom JsonNode implementations\nFixes #3699", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/node/NodeCursor.java"]}
{"id": 33, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3666", "base_commit": "960b91c981fed3ea3ce9901e31954b76809ead2f", "patch": "", "problem_statement": "Fix string-based creators with UNWRAP_SINGLE_VALUE_ARRAYS\n- it seems that the DoS fix in `BeanDeserializer` incorrectly advanced the parser. I've fixed this, but this is potentially security-sensitive, so please take a careful look if this is right :)\r\n- I also changed `FactoryBasedEnumDeserializer` to handle unwrapping of strings properly. I haven't touched  the error handling when there is another token, that is handled by the branch with the comment `Could argue we should throw an exception but...`. imo that should throw an exception, can we change that in a minor release?\r\n\r\nFixes #3655", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/BeanDeserializer.java", "src/main/java/com/fasterxml/jackson/databind/deser/std/FactoryBasedEnumDeserializer.java"]}
{"id": 34, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3626", "base_commit": "4b03c469e5d28d6e20d3bb4d0b26123ef5c30c19", "patch": "", "problem_statement": "Implementation for \"Provide method ObjectMapper.copyWith(JsonFactory)\" Closes  #3212\nNot sure about this implementation. Some feedback would be appreciated until I have time to work on it again soon.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/ObjectMapper.java"]}
{"id": 35, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3625", "base_commit": "070cf688be7ba91446c897f4a9861eb612b2d86b", "patch": "", "problem_statement": "fix #3624 ALLOW_COERCION_OF_SCALARS allows int->float coercion\nIssue reported here: #3624\r\n\r\nExisting code which disables `MapperFeature.ALLOW_COERCION_OF_SCALARS` unexpectedly impacted by #3509 / #3503 which added support for coercionconfig converting from integer-shaped data into float-shaped data. I agree that the ability to control such facets of coercion is fantastic, but I'm not sure that the feature should impact `MapperFeature.ALLOW_COERCION_OF_SCALARS` for a case that can be considered a valid format in JSON (`1` vs `1.0`, I would argue both are valid representations of `(float) 1`).\r\n\r\nIn an ideal world, I would use the new coercion configuration type, however this is not always possible due to cross-version compatibility requirements. Dependency resolution from 2.13.x to 2.14.0 will potentially cause deserialization to fail unexpectedly.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/cfg/CoercionConfigs.java"]}
{"id": 36, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3621", "base_commit": "7690a33de90f0c24f21fdac071f7cc0c5a94b825", "patch": "", "problem_statement": "[2.13.x] Add check in primitive value deserializers to avoid deep wrapper array nesting wrt UNWRAP_SINGLE_VALUE_ARRAYS [CVE-2022-42003]\n# What does this PR do?\r\n\r\nAs discussed in https://github.com/FasterXML/jackson-databind/issues/3590 \r\n\r\nHere is a PR with \r\n\r\n- a cherry pick of the related changes \r\n- updates release notes for a potential 2.13.4.1", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/deser/std/StdDeserializer.java"]}
{"id": 37, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3560", "base_commit": "7f1a3db2ddc48addc3f6bddf065f06eedd0ac370", "patch": "", "problem_statement": "Fix #3559: Support basic JDK `Map` types for @JsonAnySetter on `null` field\nNone", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/databind/JsonMappingException.java", "src/main/java/com/fasterxml/jackson/databind/deser/SettableAnyProperty.java"]}
{"id": 38, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3509", "base_commit": "e7ab4559e75c38eae89adcc74b8c54bd053a049f", "patch": "", "problem_statement": "Fix #3503 - Implement Integer to Float coercion config\n### Description\r\nThis pull request proposes to update the `float`, `Float`, and `BigDecimal` deserializing logic to take into account the coercion config for integer JSON inputs. Currently, this configuration is being ignored.\r\n\r\n### Issue\r\n#3503 \r\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/std/NumberDeserializers.java", "src/main/java/com/fasterxml/jackson/databind/deser/std/StdDeserializer.java", "src/main/java/com/fasterxml/jackson/databind/type/LogicalType.java"]}
{"id": 39, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3371", "base_commit": "ef6564c5cf03144ad9689b1444d3654c6f18eb15", "patch": "", "problem_statement": "Fix #2541 (support merge polymorphic property)\nHi tatu, I have add the test cases for merging polymorphic property, please review, thanks.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/SettableBeanProperty.java"]}
{"id": 40, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-2036", "base_commit": "bfeb1fa9dc4c889f8027b80abb2f77996efd9b70", "patch": "", "problem_statement": "Fix #955. Added DeserializationFeature.USE_BASE_TYPE_AS_DEFAULT\nAdded possibility to define concrete class as a default for deserialization.", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/DeserializationFeature.java", "src/main/java/com/fasterxml/jackson/databind/jsontype/impl/StdTypeResolverBuilder.java"]}
{"id": 41, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-1923", "base_commit": "5d4eb514820a7cfc7135e4b515dd9531ebdd523a", "patch": "", "problem_statement": "Fix #1872\nbackport Fix #1872 to 2.7 branch", "edit_functions": ["release-notes/VERSION", "src/main/java/com/fasterxml/jackson/databind/jsontype/impl/SubTypeValidator.java"]}
{"id": 42, "repo": "fasterxml/jackson-databind", "instance_id": "fasterxml__jackson-databind-3851", "base_commit": "cf7c15a3ddf8fa6df5c8961cb57e97e12ee9728a", "patch": "", "problem_statement": "Fix `Enum` deserialization with `JsonFormat.Shape.OBJECT` using both `DELEGATING` and `PROPERTIES` creator modes\nresolves #3566 ", "edit_functions": ["src/main/java/com/fasterxml/jackson/databind/deser/std/FactoryBasedEnumDeserializer.java"]}
