{"id": 1, "repo": "googlecontainertools/jib", "instance_id": "googlecontainertools__jib-4144", "base_commit": "8df72a1ab4d60cf4e5800963c787448e1b9c71b3", "patch": "", "problem_statement": "Add defaut base image for Java 21\nI looked at all occurrences of \"eclipse-temurin\", I don't think I missed other places to update.\r\n\r\nThank you for your interest in contributing! For general guidelines, please refer to\r\nthe [contributing guide](https://github.com/GoogleContainerTools/jib/blob/master/CONTRIBUTING.md).\r\n\r\nBefore filing a pull request, make sure to do the following:\r\n\r\n- [x] Create a new issue at https://github.com/GoogleContainerTools/jib/issues/new/choose.\r\n- [ ] Ensure that your implementation plan is approved by the team.\r\n- [x] Verify that integration tests and unit tests are passing after the change.\r\n- [x] Address all checkstyle issues. Refer to\r\n  the [style guide](https://github.com/GoogleContainerTools/jib/blob/master/STYLE_GUIDE.md).\r\n\r\nThis helps to reduce the chance of having a pull request rejected.\r\n\r\nFixes #4137 🛠️", "edit_functions": ["docs/google-cloud-build.md", "jib-cli/src/main/java/com/google/cloud/tools/jib/cli/jar/JarFiles.java", "jib-gradle-plugin/README.md", "jib-maven-plugin/README.md", "jib-plugins-common/src/main/java/com/google/cloud/tools/jib/plugins/common/PluginConfigurationProcessor.java"]}
{"id": 2, "repo": "googlecontainertools/jib", "instance_id": "googlecontainertools__jib-4035", "base_commit": "934814cc5a2f8d22af8644aabe0d2a2e803818cd", "patch": "", "problem_statement": "fix: fix WWW-Authenticate header parsing for Basic authentication\nFixes #4032 🛠️\r\n\r\nThis accepts the Basic auth scheme without realm.\r\n", "edit_functions": ["jib-core/src/main/java/com/google/cloud/tools/jib/registry/RegistryAuthenticator.java"]}
{"id": 3, "repo": "googlecontainertools/jib", "instance_id": "googlecontainertools__jib-2542", "base_commit": "34a757b0d64f19c47c60fcb56e705e14c2a4e0c8", "patch": "", "problem_statement": "Solved : NPE if the server doesn't provide any HTTP content for an error\nFixes #2532 \r\n", "edit_functions": ["jib-core/src/main/java/com/google/cloud/tools/jib/registry/RegistryEndpointCaller.java"]}
{"id": 4, "repo": "googlecontainertools/jib", "instance_id": "googlecontainertools__jib-2536", "base_commit": "cb78087f2738ab214af739b915e7279b4fcf6aa1", "patch": "", "problem_statement": "Fix NPE when reading \"auths\" section in ~/.docker/config.json\nFixes #2535.", "edit_functions": ["jib-core/CHANGELOG.md", "jib-core/src/main/java/com/google/cloud/tools/jib/registry/credentials/DockerConfigCredentialRetriever.java", "jib-gradle-plugin/CHANGELOG.md", "jib-maven-plugin/CHANGELOG.md"]}
{"id": 5, "repo": "googlecontainertools/jib", "instance_id": "googlecontainertools__jib-2688", "base_commit": "7b36544eca5e72aba689760118b98419ef4dd179", "patch": "", "problem_statement": "Fix NPE when Spring Boot Maven Plugin doesn't have <configuration>\nFixes #2687.", "edit_functions": ["jib-maven-plugin/CHANGELOG.md", "jib-maven-plugin/src/main/java/com/google/cloud/tools/jib/maven/MavenProjectProperties.java"]}
