{"id": 1, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3424", "base_commit": "87e4a4fa85c84cbd09420c2c8e73bab3627708a7", "patch": "", "problem_statement": "Fixes #3419: Disable mocks with an error message\nThis allows us to avoid the memory leak issues addressed by clearInlineMocks, but track how a mock object might leak out of its creating test, as referenced in https://github.com/mockito/mockito/issues/3419\r\n\r\nA follow-up should likely create a JUnit 4 rule that allows this to be called with a message that automatically includes the name of the test that created the mock.\r\n\r\n## Checklist\r\n\r\n - [ x ] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)\r\n - [ x ] PR should be motivated, i.e. what does it fix, why, and if relevant how\r\n - [ x ] If possible / relevant include an example in the description, that could help all readers\r\n       including project members to get a better picture of the change\r\n - [ x ] Avoid other runtime dependencies\r\n - [ x ] Meaningful commit history ; intention is important please rebase your commit history so that each\r\n       commit is meaningful and help the people that will explore a change in 2 years\r\n - [ x ] The pull request follows coding style\r\n - [ x ] Mention `Fixes #<issue number>` in the description _if relevant_\r\n - [ x ] At least one commit should mention `Fixes #<issue number>` _if relevant_\r\n\r\n", "edit_functions": ["src/main/java/org/mockito/MockitoFramework.java", "src/main/java/org/mockito/exceptions/misusing/DisabledMockException.java", "src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java", "src/main/java/org/mockito/internal/framework/DisabledMockHandler.java", "src/main/java/org/mockito/plugins/InlineMockMaker.java"]}
{"id": 2, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3220", "base_commit": "a0214364c36c840b259a4e5a0b656378e47d90df", "patch": "", "problem_statement": "Fixes #3219: Add support for static mocks on DoNotMockEnforcer\nFixes #3219\r\nFix mockStatic bypassing DoNotMockEnforcer\r\nAdd (optional) method on DoNotMockEnforcer for static mocks\r\n\r\n<!-- Hey,\r\nThanks for the contribution, this is awesome.\r\nAs you may have read, project members have somehow an opinionated view on what and how should be\r\nMockito, e.g. we don't want mockito to be a feature bloat.\r\nThere may be a thorough review, with feedback -> code change loop.\r\n-->\r\n<!--\r\nIf you have a suggestion for this template you can fix it in the .github/PULL_REQUEST_TEMPLATE.md file\r\n-->\r\n## Checklist\r\n\r\n - [X] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)\r\n - [X] PR should be motivated, i.e. what does it fix, why, and if relevant how\r\n - [X] If possible / relevant include an example in the description, that could help all readers\r\n       including project members to get a better picture of the change\r\n - [X] Avoid other runtime dependencies\r\n - [X] Meaningful commit history ; intention is important please rebase your commit history so that each\r\n       commit is meaningful and help the people that will explore a change in 2 years\r\n - [X] The pull request follows coding style\r\n - [X] Mention `Fixes #<issue number>` in the description _if relevant_\r\n - [X] At least one commit should mention `Fixes #<issue number>` _if relevant_\r\n\r\n", "edit_functions": ["src/main/java/org/mockito/internal/MockitoCore.java", "src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java", "src/main/java/org/mockito/internal/configuration/plugins/PluginRegistry.java", "src/main/java/org/mockito/internal/configuration/plugins/Plugins.java", "src/main/java/org/mockito/internal/creation/MockSettingsImpl.java", "src/main/java/org/mockito/internal/creation/settings/CreationSettings.java", "src/main/java/org/mockito/internal/util/MockNameImpl.java", "src/main/java/org/mockito/mock/MockCreationSettings.java", "src/main/java/org/mockito/mock/MockType.java", "src/main/java/org/mockito/plugins/DoNotMockEnforcer.java", "src/main/java/org/mockito/plugins/DoNotMockEnforcerWithType.java"]}
{"id": 3, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3173", "base_commit": "bfee15dda7acc41ef497d8f8a44c74dacce2933a", "patch": "", "problem_statement": "Fixes #3160 : Fix interference between spies when spying on records.\nThis fixes #3160. This is a bug where spied records end up having all their fields null. Here's a reproducer of the bug:\r\n\r\n```java\r\n@Test\r\nvoid myTest() {\r\n    spy(List.of(\"something\"));\r\n\r\n    record MyRecord(String name) {}\r\n    MyRecord spiedRecord = spy(new MyRecord(\"something\"));\r\n    assertEquals(\"something\", spiedRecord.name()); // fails because spiedRecord.name() is null\r\n}\r\n```\r\n\r\nThe issue only occurs when spying records if `AbstractCollection` (or one of its children) is also spied. This is why this reproducer passes if the first line is commented out.\r\n\r\nThe problem happens because all superclasses and interfaces of `java.util.ImmutableCollections.List12` (the implementation returned by `List.of()` are transformed by `ByteBuddyAgent` and `InlineDelegateByteBuddyMockMaker` (see `InlineBytecodeGenerator::triggerRetransformation`. However, one of the superclasses, `AbstractCollection`, happens to also be used by `MethodHandle`, and when it does, Mockito trips over itself and its fallback strategy also fails for records.\r\n\r\nIn other words, in the process of constructing a record for spying, `InstrumentationMemberAccessor::newInstance` calls `MethodHandle::invokeWithArguments`, which uses a collection. Since the `AbstractCollection` class was instrumented during the earlier spy creation, the construction is intercepted and calls `isMockConstruction` (in `InlineDelegateByteBuddyMockMaker`). Since the `mockitoConstruction` boolean is `true`, because there is indeed an ongoing mock construction, Mockito thinks that the current constructor `AbstractCollection()` is the correct constructor to implement, and `isMockConstruction` returns `true`. `onConstruction` then does one last check that the type of the constructor matches the object to spy, and when it doesn't, it throws an exception (`InlineDelegateByteBuddyMockMaker` line 287).\r\n\r\nMockito then considers that the spy creation has failed and falls back on creating a mock and then copying fields from the object to spy to the newly created mock (`MockUtil::createMock`). This strategy fails for records, because their fields cannot be set by `MethodHandles::unreflectSetter` (see the javadoc on the method), as opposed to classes, where even final fields can be set. This failure is then ignored, and fields are left uninitialized (see `LenientCopyTool::copyValues`). The interference betwen spies at the root of this issue also occurs for classes, but because the fallback can successfully copy the fields, the issue probably went unnoticed.\r\n\r\n**Testing**: I was unable to add a test for this, because the language level is set to 11, before records existed. All existing tests are still passing, though, and the reproducer above fails on the master branch but passes on mine.\r\n", "edit_functions": ["src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java"]}
{"id": 4, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3167", "base_commit": "b6554b29ed6c204a0dd4b8a670877fe0ba2e808b", "patch": "", "problem_statement": "Deep Stubs Incompatible With Mocking Enum\nMockito can't mock abstract enums in Java 15 or later because they are now marked as sealed.\r\nSo Mockito reports that now with a better error message.\r\n\r\nIf a deep stub returns an abstract enum, it uses in the error case now the first enum literal of the real enum.\r\n\r\nFixes #2984 \r\n\r\n\r\n", "edit_functions": ["src/main/java/org/mockito/internal/creation/bytebuddy/InlineDelegateByteBuddyMockMaker.java"]}
{"id": 5, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3133", "base_commit": "edc624371009ce981bbc11b7d125ff4e359cff7e", "patch": "", "problem_statement": "Fixes #1382 Jupiter Captor annotation support\n## Checklist\r\n\r\n - [X] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)\r\n - [X] PR should be motivated, i.e. what does it fix, why, and if relevant how\r\n - [X] If possible / relevant include an example in the description, that could help all readers\r\n       including project members to get a better picture of the change\r\n - [X] Avoid other runtime dependencies\r\n - [X] Meaningful commit history ; intention is important please rebase your commit history so that each\r\n       commit is meaningful and help the people that will explore a change in 2 years\r\n - [X] The pull request follows coding style\r\n - [X] Mention `Fixes #<issue number>` in the description _if relevant_\r\n - [X] At least one commit should mention `Fixes #<issue number>` _if relevant_\r\n\r\nFixes #1382. \r\nIntroducing new ParameterResolver for @Captor annotation for MockitoExtension, allowing initialization of ArgumentCaptors with annotation.", "edit_functions": ["src/main/java/org/mockito/Captor.java", "src/main/java/org/mockito/internal/configuration/CaptorAnnotationProcessor.java", "src/main/java/org/mockito/internal/util/reflection/GenericMaster.java", "subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/MockitoExtension.java", "subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CaptorParameterResolver.java", "subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/CompositeParameterResolver.java", "subprojects/junit-jupiter/src/main/java/org/mockito/junit/jupiter/resolver/MockParameterResolver.java"]}
{"id": 6, "repo": "mockito/mockito", "instance_id": "mockito__mockito-3129", "base_commit": "edc624371009ce981bbc11b7d125ff4e359cff7e", "patch": "", "problem_statement": "Make MockUtil.getMockMaker() public Mockito API\nThe MockitoPlugins interface now provides access to the `MockUtil.getMockMaker()` method.\r\n\r\nFixes #3128\r\n\r\n## Checklist\r\n\r\n - [x] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)\r\n - [x] PR should be motivated, i.e. what does it fix, why, and if relevant how\r\n - [x] If possible / relevant include an example in the description, that could help all readers\r\n       including project members to get a better picture of the change\r\n - [x] Avoid other runtime dependencies\r\n - [x] Meaningful commit history ; intention is important please rebase your commit history so that each\r\n       commit is meaningful and help the people that will explore a change in 2 years\r\n - [x] The pull request follows coding style\r\n - [x] Mention `Fixes #<issue number>` in the description _if relevant_\r\n - [x] At least one commit should mention `Fixes #<issue number>` _if relevant_\r\n\r\n", "edit_functions": ["src/main/java/org/mockito/internal/configuration/plugins/DefaultMockitoPlugins.java", "src/main/java/org/mockito/internal/util/MockUtil.java", "src/main/java/org/mockito/plugins/MockitoPlugins.java"]}
