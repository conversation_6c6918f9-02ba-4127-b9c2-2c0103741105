{"id": 1, "repo": "daip-patcher", "instance_id": "daip-patcher_001", "base_commit": "3bb28f08d68c80ec70e8c965749b51df61a23ad1", "patch": "", "problem_statement": "用户故事： \r\n用户希望能够批量删除补丁，界面选择多个补丁，点击删除能全部删除。 \r\n验收准则： \r\nScenario 1：多选补丁功能 \r\nGiven 用户打开补丁管理界面 \r\nWhen 用户勾选多个补丁的复选框（或使用“全选”功能） \r\nThen 系统应正确标记所选补丁，并在界面上显示已选数量（如“已选 3 个补丁”） \r\nScenario 2：批量删除操作 \r\nGiven 用户已选择多个补丁 \r\nWhen 用户点击“删除”按钮 \r\nThen 系统应弹出确认对话框，显示提示信息（如“确认删除 3 个补丁？”） \r\nScenario 3：确认删除并完成操作 \r\nGiven 用户已确认删除操作 \r\nWhen 系统执行批量删除 \r\nThen \r\n所有选中的补丁应从列表中移除 \r\n界面应显示成功提示（如“成功删除 3 个补丁”） \r\n补丁列表应自动刷新，不再显示已删除的补丁 \r\nScenario 4：取消删除操作 \r\nGiven 用户已选择多个补丁并点击“删除”按钮 \r\nWhen 用户在确认对话框中选择“取消” \r\nThen \r\n系统应关闭对话框，不执行删除操作 \r\n补丁列表应保持原样，所有选中状态不变", "edit_functions": ["daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchDeleteHandler.java", "daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/src/main/java/com/zte/daip/manager/patcher/handler/impl/paas/impl/PatchDeleteHandlerImpl.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/delete/PatchDeleteService.java", "daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java"]}
{"id": 2, "repo": "daip-patcher", "instance_id": "daip-patcher_002", "base_commit": "5372e5e4be693ee5594451bff53bb245325d5cee", "patch": "", "problem_statement": "用户故事 \r\n场景1：升级一个服务的补丁时，如果本次所有要升级的补丁都是热补丁时，不需要停止、启动该服务； \r\n场景2：只要有一个补丁非热补丁，就需要停止、启动该服务； \r\n验收准则 \r\nScenario 1：升级一个服务的补丁，只有一个补丁，且是热补丁 \r\nGIVEN: 系统运行正常，服务a正常，服务a有补丁需要升级，只有一个补丁，且是热补丁 \r\nWHEN: 执行补丁升级操作 \r\nTHEN: 升级过程中没有停止、启动该服务 \r\nScenario 2：升级一个服务的补丁，所有补丁都是热补丁 \r\nGIVEN: 系统运行正常，服务a正常，服务a有补丁需要升级，有多个补丁，所有补丁都是热补丁 \r\nWHEN: 执行补丁升级操作 \r\nTHEN: 升级过程中没有停止、启动该服务 \r\nScenario 3：升级一个服务的补丁，只有一个补丁，且是非热补丁 \r\nGIVEN: 系统运行正常，服务a正常，服务a有补丁需要升级，只有一个补丁，且是非热补丁 \r\nWHEN: 执行补丁升级操作 \r\nTHEN: 升级过程中有停止、启动该服务的步骤 \r\nScenario 4：升级一个服务的补丁，有多个补丁，且都是非热补丁 \r\nGIVEN: 系统运行正常，服务a正常，服务a有补丁需要升级，有多个补丁，且都是非热补丁 \r\nWHEN: 执行补丁升级操作 \r\nTHEN: 升级过程中有停止、启动该服务的步骤 \r\nScenario 5：升级一个服务的补丁，有多个补丁，有部分热补丁、部分非热补丁 \r\nGIVEN: 系统运行正常，服务a正常，服务a有补丁需要升级，有多个补丁，部分热补丁、部分非热补丁 \r\nWHEN: 执行补丁升级操作 \r\nTHEN: 升级过程中有停止、启动该服务的步骤", "edit_functions": ["daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchTaskHandler.java", "daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/src/main/java/com/zte/daip/manager/patcher/handler/impl/paas/impl/PatchTaskHandlerImpl.java", "daip-patcher-service/daip-patcher-application/src/main/java/com/zte/daip/manager/patcher/application/service/PatchTaskAppService.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/PatchTaskOperateService.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/update/PatchUpdateService.java", "daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchTaskController.java"]}
{"id": 3, "repo": "daip-patcher", "instance_id": "daip-patcher_003", "base_commit": "734e03fd596f2b9ad8d96bb05682cfeab3bdc707", "patch": "", "problem_statement": "特性描述： \r\n补丁升级后，如发现有问题需要回退到补丁升级前状态； \r\nschema补丁自动批量升级时自动记录回退点，升级成功后可在界面选择对应的升级任务，其中可以进行回退操作； \r\n验收准则： \r\n1. schema补丁回退 \r\nGiven: 系统运行正常，schema补丁已升级 \r\nWhen: 执行schema补丁回退 \r\nThen: schema补丁回退成功，系统运行正常", "edit_functions": ["daip-patcher-service/daip-patcher-application/src/main/java/com/zte/daip/manager/patcher/application/event/listener/UpdateSchemaListener.java", "daip-patcher-service/daip-patcher-application/src/main/java/com/zte/daip/manager/patcher/application/service/UpdateSchemaPatchService.java", "daip-patcher-service/daip-patcher-application/src/main/java/com/zte/daip/manager/patcher/application/service/monitor/schedule/UpdateSchemaPatchSchedule.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/schema/UpdateSchemaPatch.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/schema/UpdateSchemaPatchNew.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/schema/adapter/SchemaPatchApi.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/schema/utils/SchemaPatchUtils.java", "daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/src/main/java/com/zte/daip/manager/patcher/impl/paas/infrastructure/SchemaPatchImpl.java"]}
{"id": 4, "repo": "daip-patcher", "instance_id": "daip-patcher_004", "base_commit": "320a4209bf10ab6597434858d9765ac40e20de3e", "patch": "", "problem_statement": "用户故事\n作为运维人员，我希望能够通过新增的API接口删除指定名称的单个补丁\n验收准则：\n    Scenario 1: 成功删除未应用的补丁\n    Given: 存在一个名为 patch_v1.的补丁\n    And: 该补丁未出现在补丁历史记录中 (未应用过)\n    When: 调用删除单个补丁接口\n    Then:删除补丁详情表中指定补丁\n    And: 删除补丁分发表中指定补丁\n    Scenario 2: 无法删除已应用的补丁\n    Given: 存在一个名为patch_v2.0的补丁\n    And: 该补丁已出现在补丁历史记录中 (已应用过)\n    When: 调用删除单个补丁接口\n    Then: 抛出异常，提示patch has updated, patchName=patch_v2.0\n    And: 所有服务中的补丁信息保持不变\n业务流程\n@startuml\nactor 用户 as user\nparticipant 补丁删除服务 as patcherService\nparticipant 数据库 as Database\nuser -> patcherService: 调用删除补丁接口\ngroup 前置校验\n    patcherService -> Database: 查询补丁历史记录\n    Database --> patcherService: 返回历史记录\n    alt 补丁已应用\n        patcherService --> user: 抛出异常(补丁已升级)\n    else 未升级\n         patcherService -> Database: 删除补丁详情记录\n         patcherService -> Database: 删除补丁分发记录\n    end\nend\npatcherService --> user: 返回操作结果\n@enduml\n接口定义json\n{\n  \"swagger\": \"2.0\",\n  \"info\": {\n    \"version\": \"1.0.0\",\n    \"title\": \"补丁删除API\",\n    \"description\": \"提供补丁删除操作的接口\"\n  },\n  \"host\": \"api.example.com\",\n  \"basePath\": \"/\",\n  \"schemes\": [\n    \"https\"\n  ],\n  \"consumes\": [\n    \"application/json\"\n  ],\n  \"produces\": [\n    \"application/json\"\n  ],\n  \"paths\": {\n    \"/patches/delete/singlePatch\": {\n      \"post\": {\n        \"tags\": [\n          \"补丁删除\"\n        ],\n        \"summary\": \"删除单个补丁\",\n        \"description\": \"删除单个补丁\",\n        \"operationId\": \"deleteSinglePatch\",\n        \"parameters\": [\n          {\n            \"name\": \"patchName\",\n            \"in\": \"query\",\n            \"description\": \"补丁名称\",\n            \"required\": true,\n            \"type\": \"string\",\n            \"example\": \"patch-1.0.0\"\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"删除成功\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"success\"\n            }\n          },\n          \"400\": {\n            \"description\": \"参数错误\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"参数错误\"\n            }\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"服务器内部错误\"\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "edit_functions": ["daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDispatchService.java","daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchHistoryService.java","daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java","daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java","daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java"]}
{"id": 5, "repo": "daip-deployer", "instance_id": "daip-deployer_001", "base_commit": "f08bd851973a6eea37fa3674dc9a45d3227127de", "patch": "", "problem_statement": "通过界面可以开启和关闭cgroup，配置cpu百分比，DAP处理数据保存到数据库，并同步给Agent。组件重启服务，下发配置过程中通过接口查询cgroup开启及cpu百分比，根据已下发的cgroup配置项，写入cgroup和pid配置，实现cgroup资源分配。 \r\n验收准则： \r\n \r\n场景一：提供界面配置cgroup能力 \r\nGiven：系统运行正常 \r\nWhen：在cgroup页面进行开启cgroup和设置服务cpu配额操作 \r\nThen：操作成功，再次打开页面可看到之前的设置 \r\n \r\n场景二：提供接口配置cgroup能力 \r\nGiven：系统运行正常 \r\nWhen：调用openapi接口进行开启cgroup和设置服务cpu配额 \r\nThen：操作成功，打开页面可看到通过接口设置的值 \r\n \r\n场景三：通过界面开启/关闭一个或多个主机的Cgroup开关。 \r\nGiven：系统运行正常 \r\nWhen：主机配置页面开启/关闭一个或多个主机的Cgroup开关 \r\nThen：操作成功，再次打开页面可看到通过接口设置的值 \r\n \r\n场景四：通过接口开启/关闭一个或多个主机的Cgroup开关。 \r\nGiven：系统运行正常 \r\nWhen：用openapi接口进行开启/关闭一个或多个主机的Cgroup开关 \r\nThen：操作成功，打开页面可看到通过接口设置的值 \r\n \r\n场景五：提供agent侧查询cgroup配置功能 \r\nGiven：系统运行正常 \r\nWhen：在agent节点上调用agent的查询cgroup信息接口 \r\nThen：返回正确的信息 \r\n \r\n场景六：从物理机版本升级到tcf版本时相关数据不能丢失； \r\nGiven：物理机已开启cgroup，并且配置了服务的cpu配额 \r\nWhen：执行迁移升级到H3版本的操作 \r\nThen：执行成功，cgroup相关信息没有丢失", "edit_functions": ["daip-deployer-common/src/main/java/com/zte/daip/manager/deployer/common/cgroup/check/CgroupDefaultCheck.java", "daip-deployer-common/src/main/java/com/zte/daip/manager/deployer/common/cgroup/check/CgroupHostsConfigCheck.java", "daip-deployer-common/src/main/java/com/zte/daip/manager/deployer/common/cgroup/service/CgroupSwitchModify.java", "daip-deployer-handler/daip-deployer-handler-impl/daip-deployer-handler-impl-paas/src/main/java/com/zte/daip/manager/deployer/handler/cgroup/CgroupHandler.java", "daip-deployer-service/daip-deployer-impl/daip-deployer-impl-paas/src/main/java/com/zte/daip/manager/deployer/server/controller/CgroupResourceController.java"]}
{"id": 6, "repo": "daip-deployer", "instance_id": "daip-deployer_002", "base_commit": "0da6b44a5173af96291fd33a44917b5f613db7e2", "patch": "", "problem_statement": "需要一次性启动单节点所有角色、和停止所有角色的功能； \r\n可通过界面停止一个受控主机上部署的所有角色 \r\n可通过界面启动一个受控主机上部署的所有角色。 \r\n场景一：停止一个受控主机上部署的所有角色 \r\nAC： \r\n通过界面停止一个受控主机上部署的所有角色，该节点上所有部署的角色全部停止成功 \r\n \r\n场景二：启动一个受控主机上部署的所有角色 \r\nAC： \r\n通过界面启动一个受控主机上部署的所有角色，该节点上所有部署的角色全部启动成功", "edit_functions": ["daip-deployer-common/src/main/java/com/zte/daip/manager/deployer/common/role/service/HostRoleService.java", "daip-deployer-handler/daip-deployer-handler-impl/daip-deployer-handler-impl-paas/src/main/java/com/zte/daip/manager/deployer/handler/role/HostRoleHandler.java"]}
{"id": 7, "repo": "daip-deployer", "instance_id": "daip-deployer_003", "base_commit": "7cc0ad58bf528488dd984e468c714e32e8bfecca", "patch": "", "problem_statement": "特性描述： \r\n补丁升级后，如发现有问题需要回退到补丁升级前状态； \r\nschema补丁当前不具体回退功能，需要加强； \r\n验收准则： \r\n场景一： schema补丁升级 \r\nGiven: 系统运行正常 \r\nWhen: 上传某一服务的schema补丁 \r\nThen: schema补丁升级成功，系统运行正常 \r\n \r\n场景二：schema补丁回退 \r\nGiven: 系统运行正常，schema补丁已升级 \r\nWhen: 执行schema补丁回退 \r\nThen: schema补丁回退成功，系统运行正常", "edit_functions": ["daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/OperateSchemaFileService.java", "daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/OperateSchemaPatchService.java", "daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/RollbackSchemaPatchService.java", "daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/type/OperateSchemaPatchType.java", "daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/type/OperateServiceModelSchema.java", "daip-deployer-model/src/main/java/com/zte/daip/manager/deployer/model/schema/service/utils/OperateProductSchemaUtil.java"]}
