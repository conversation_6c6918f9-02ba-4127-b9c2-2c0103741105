{"id": 1, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_001", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/文件函数级\n我想理解向节点发送http请求，校验响应头和响应码的全流程代码实现逻辑，方便我改造请求方式", "edit_functions": ["WebCache_TestLibrary/UserLibrary/app.py"]}
{"id": 2, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_002", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/文件函数级\n我想理解OPS网元升级工具的流程逻辑，方便我更好的填写配置项，成功完成slb网元和webcache网元的升级", "edit_functions": ["WebCache_TestLibrary/OPS_planning_update_Interface.py"]}
{"id": 3, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_003", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/文件函数级\n我想理解通过发送API接口修改域名高级配置项的业务逻辑，方便我拼装关键字", "edit_functions": ["WebCache_TestLibrary/OMSLibrary/app/app.py"]}
{"id": 4, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_004", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/文件函数级\n我想理解内容注入与内容刷新的业务逻辑，方便我开发测试用例", "edit_functions": ["WebCache_TestLibrary/OMSLibrary/domain/model.py"]}
{"id": 5, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_005", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/模块级\n我想理解节点根据url校验缓存路径是否存在的关键字实现逻辑，方便我自己手工计算缓存路径", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/ServiceLibrary/domain/device.py"]}
{"id": 6, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_006", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/模块级\n我现在想了解发送请求的关键字实现方式，方便我改造这个函数增加一个入参", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/UserLibrary/app/httpuserapp.py"]}
{"id": 7, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_007", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/模块级\n我想了解webcache网元的话单校验实现逻辑，方便我修改补充校验点", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/ServiceLibrary/domain/device.py"]}
{"id": 8, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_008", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/模块级\n我想了解回源头校验的实现逻辑，方便我排查回源抓包没有找到回源头的原因", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/InfrastructureLibrary/envpara.py"]}
{"id": 9, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_009", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/项目级\n我想理解将CI执行结果xml文件远程传输到AItest平台，并调用Ai自动分析工具实现分析的步骤", "edit_functions": ["upload_to_AITest.py", "AITest_upload_xml.py"]}
{"id": 10, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_010", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/项目级\n我想了解自动从版本仓库链接下载版本，上传OPS的版本仓库，发送接口消息实现版本注册的逻辑", "edit_functions": ["WebCache_TestLibrary/OPS_planning_update_Interface.py", "WebCache_TestLibrary/ops_register_version.py"]}
{"id": 11, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_011", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/项目级\n我想理解OPS配置管理实现网元级节点级的配置下发，配置核查，配置校验的逻辑", "edit_functions": ["WebCache_TestLibrary/OPSLibrary/app/app.py", "WebCache_TestLibrary/OPSLibrary/domain/model.py"]}
{"id": 12, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_012", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码库理解/项目级\n我想了解基于模板和TDM测试数据管理平台上的测试数据生成测试用例的逻辑", "edit_functions": ["WebCache_TestLibrary/TestCaseLibrary/__init__.py", "WebCache_TestLibrary/TDMLibrary/domain/domain.py"]}
{"id": 13, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_013", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/文件函数级\n向节点发送http请求，校验响应头和响应码的全流程代码", "edit_functions": ["WebCache_TestLibrary/UserLibrary/app"]}
{"id": 14, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_014", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/文件函数级\n模拟通过发送API接口修改域名高级配置项的业务", "edit_functions": ["WebCache_TestLibrary/OMSLibrary/app/app.py"]}
{"id": 15, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_015", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/文件函数级\n实现OPS网元升级工具，成功完成slb网元和webcache网元的升级", "edit_functions": ["WebCache_TestLibrary/OPS_planning_update_Interface.py"]}
{"id": 16, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_016", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/文件函数级\n实现内容注入与内容刷新的业务逻辑，", "edit_functions": ["WebCache_TestLibrary/OMSLibrary/domain/model.py"]}
{"id": 17, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_017", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/模块级\n我想实现webcache网元的话单校", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/ServiceLibrary/domain/device.py"]}
{"id": 18, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_018", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/模块级\n我想实现回源头抓包校验", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/InfrastructureLibrary/envpara.py"]}
{"id": 19, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_019", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/模块级\n我想开发发送请求的关键字", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/UserLibrary/app/httpuserapp.py"]}
{"id": 20, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_020", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/模块级\n我想实现节点根据url校验缓存路径是否存在的关键字实现逻辑，", "edit_functions": ["WebCache_TestLibrary/ServiceLibrary/app/app.py", "WebCache_TestLibrary/ServiceLibrary/domain/device.py"]}
{"id": 21, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_021", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/项目级\n基于模板和TDM测试数据管理平台上的测试数据生成测试用例", "edit_functions": ["WebCache_TestLibrary/TestCaseLibrary/__init__.py", "WebCache_TestLibrary/TDMLibrary/domain/domain.py"]}
{"id": 22, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_022", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/项目级\n自动从版本仓库链接下载版本，上传OPS的版本仓库，发送接口消息实现版本注册", "edit_functions": ["WebCache_TestLibrary/ops_register_version.py", "WebCache_TestLibrary/OPS_planning_update_Interface.py"]}
{"id": 23, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_023", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/项目级\nOPS配置管理实现网元级节点级的配置下发，配置核查，配置校验", "edit_functions": ["WebCache_TestLibrary/OPSLibrary/app/app.py", "WebCache_TestLibrary/OPSLibrary/domain/model.py"]}
{"id": 24, "repo": "ZXCDN/CACHE/ST", "instance_id": "ST_024", "base_commit": "d2c46e94789b943f2e4a0e7dc8dd3d03b950a137", "patch": "", "problem_statement": "代码生成/项目级\n将CI执行结果xml文件远程传输到AItest平台，并调用Ai自动分析工具实现分析", "edit_functions": ["upload_to_AITest.py", "AITest_upload_xml.py"]}
