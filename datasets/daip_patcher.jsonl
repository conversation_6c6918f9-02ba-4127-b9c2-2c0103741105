{"id": 1, "repo": "DAP/MANAGER/PATCHER", "instance_id": "patcher_001", "base_commit": "d42ad3b60f325a1dd77bcf6edbfa2788578b66b2", "patch": "", "problem_statement": "完成用户故事\n\n标题：作为运维人员，我希望能够删除单个补丁\n场景1：成功删除单个补丁。\n场景2：如果补丁已应用，则删除失败。\n\n# 方案设计\n\n\n ```\n@startuml\n\nparticipant \"PatchDeleteController\" as AppService\nparticipant \"PatchDeleteService\" as DomainService\nparticipant \"数据库\" as Database\n\nAppService -> DomainService: deleteSinglePatch(patchName)\n\ngroup 前置校验\n    DomainService -> Database: 查询补丁历史记录\n    Database --> DomainService: 返回历史记录\n    alt 补丁已应用\n        DomainService --> AppService: 抛出异常(\"补丁已升级\")\n    else 未升级\n         DomainService -> Database: 删除补丁信息记录\n         DomainService -> Database: 删除补丁分发记录\n    end\nend\n\nDomainService --> AppService: 返回操作结果\n@enduml\n ```\n\n# 接口定义\n\n```json\n{\n  \"swagger\": \"2.0\",\n  \"info\": {\n    \"version\": \"1.0.0\",\n    \"title\": \"补丁删除API\",\n    \"description\": \"提供补丁删除操作的接口\"\n  },\n  \"host\": \"api.example.com\",\n  \"basePath\": \"/\",\n  \"schemes\": [\n    \"https\"\n  ],\n  \"consumes\": [\n    \"application/json\"\n  ],\n  \"produces\": [\n    \"application/json\"\n  ],\n  \"paths\": {\n    \"/patches/delete/singlePatch\": {\n      \"post\": {\n        \"tags\": [\n          \"补丁删除\"\n        ],\n        \"summary\": \"删除单个补丁\",\n        \"description\": \"删除单个补丁\",\n        \"operationId\": \"deleteSinglePatch\",\n        \"parameters\": [\n          {\n            \"name\": \"patchName\",\n            \"in\": \"query\",\n            \"description\": \"补丁名称\",\n            \"required\": true,\n            \"type\": \"string\",\n            \"example\": \"patch-1.0.0\"\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"删除成功\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"success\"\n            }\n          },\n          \"400\": {\n            \"description\": \"参数错误\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"参数错误\"\n            }\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"example\": \"服务器内部错误\"\n            }\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n## 验收准则\n\n**Scenario 1: 成功删除未应用的补丁**\n\n- Given: 存在一个名为 \"patch_v1.0\" 的补丁\n- And: 该补丁未出现在补丁历史记录中 (未应用过)\n- When: 调用删除单个补丁接口\n- Then: 补丁信息从 补丁信息表 中被删除\n- And: 补丁信息从 补丁分发表 中被删除\n\n**Scenario 2: 尝试删除已升级的补丁**\n\n- Given: 存在一个名为 \"patch_v2.0\" 的补丁\n- And: 该补丁已出现在补丁历史记录中 (已应用过)\n- When: 调用删除单个补丁接口\n- Then: 抛出 PatchException 异常，提示 \"patch has updated,patchName=patch_v2.0\"\n- And: 所有服务中的补丁信息保持不变\n", "edit_functions": ["daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchHistoryService.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchInfoService.java", "daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDispatchService.java"]}