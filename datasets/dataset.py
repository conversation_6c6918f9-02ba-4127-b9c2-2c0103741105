import os
import re
import json


def extract_path(path: str, repo: str) -> str:
    """
    从给定的path中截取repo之后的路径部分

    参数:
        path: 原始路径字符串，例如 'aaa/bbb/xxx/yyy/zzz'
        repo: 仓库名称，例如 'xxx'

    返回:
        截取后的路径字符串，例如 'yyy/zzz'
    """
    parts = path.split("/")
    try:
        # 找到repo在路径中的位置
        repo_index = parts.index(repo.split("/")[-1])
        # 返回repo之后的部分并用'/'连接
        return "/".join(parts[repo_index + 1 :])
    except ValueError as e:
        # 如果repo不在路径中，返回空字符串或者可以根据需求返回整个路径
        print(f"ValueError: {e}")
        return path


def normalize_path(path: str, repo: str) -> str:
    """
    统一路径格式为以 mmvsops 开头
    例如:
    "ivamp/zxoms/mmvsops/xxx" -> "mmvsops/xxx"
    "mmvsops/xxx" -> "mmvsops/xxx"
    """
    index = path.find(repo.split("/")[-1])
    if index >= 0:
        return path[index:]
    return path


def modify_json(json_data, filename: str):
    """
    实现特定的 JSON 转换逻辑
    :param json_data: 从文件读取的原始 JSON 数据（字典）
    :param filename: 当前处理的文件名（用于提取 instance_id）
    :return: 转换后的 JSON 数据（字典）
    """
    try:
        # 从文件名提取 id (mmvsops_case030.json -> 30)
        id = int(re.sub(r".*_case(\d+)\.json$", r"\1", filename))

        # 从文件名提取 instance_id (mmvsops_case030.json -> mmvsops_030)
        instance_id = re.sub(r"_case(\d+)\.json$", r"_\1", filename)

        # 合并 type 和 task 字段到 problem_statement，用换行符分隔
        problem_statement = json_data.get("type", "") + "\n" + json_data.get("task", "")

        # 处理 target_codes 转换为 edit_functions
        edit_functions = []
        for target in json_data.get("target_codes", []):
            raw_path = target.get("path", "")
            path = extract_path(raw_path, json_data.get("repo", ""))
            classes = target.get("class", [])
            methods_list = target.get("methods", [])

            edit_functions.append(path)

            # if not classes and not methods_list:
            #     edit_functions.append(path)
            #     continue

            # if not classes and methods_list:
            #     for method in methods_list:
            #         edit_functions.append(f"{path}:{method}")
            #     continue

            # if classes and not methods_list:
            #     for class_name in classes:
            #         edit_functions.append(f"{path}:{class_name}")
            #     continue

            # for class_name in classes:
            #     for method in methods_list:
            #         edit_functions.append(f"{path}:{class_name}.{method}")

        return {
            "id": id,
            "repo": json_data.get("repo", ""),
            "instance_id": instance_id,
            "base_commit": json_data.get("commit_id", ""),
            "patch": "",
            "problem_statement": problem_statement,
            "edit_functions": edit_functions,
        }
    except Exception as e:
        print(
            f"An error occurred when processing the JSON data of the file {filename} : {str(e)}"
        )
        return None


def merge_json_to_jsonl(input_dir: str, output_file: str):
    """
    将目录下的所有JSON文件合并为一个JSONL文件
    :param input_dir: 包含JSON文件的输入目录
    :param output_file: 输出的JSONL文件路径
    """
    # 获取目录下所有.json文件
    json_files = [f for f in os.listdir(input_dir) if f.endswith(".json")]

    if not json_files:
        print(f"Warning: The JSON file was not found in the directory {input_dir}")
        return

    processed_count = 0
    with open(output_file, "w", encoding="utf-8") as outfile:
        for json_file in json_files:
            file_path = os.path.join(input_dir, json_file)
            try:
                with open(file_path, "r", encoding="utf-8") as infile:
                    data = json.load(infile)

                modified_data = modify_json(data, json_file)
                if modified_data is None:
                    continue

                json.dump(modified_data, outfile, ensure_ascii=False)
                outfile.write("\n")
                processed_count += 1

            except json.JSONDecodeError as e:
                print(f"The file {json_file} is not in valid JSON format: {str(e)}")
            except Exception as e:
                print(
                    f"An unexpected error occurred when processing the file {json_file} : {str(e)}"
                )

    print(
        f"Processing completed. Successfully merged {processed_count}/{len(json_files)} JSON files to {output_file}"
    )


def merge_json_to_jsonl_sort(input_dir: str, output_file: str, sort_by_id: bool = True):
    """
    将目录下的所有JSON文件合并为一个JSONL文件，并可选择按id排序

    :param input_dir: 包含JSON文件的输入目录
    :param output_file: 输出的JSONL文件路径
    :param sort_by_id: 是否按id字段排序，默认为True
    """
    # 获取目录下所有.json文件
    json_files = [f for f in os.listdir(input_dir) if f.endswith(".json")]

    if not json_files:
        print(f"Warning: The JSON file was not found in the directory {input_dir}")
        return

    all_data = []  # 用于存储所有数据以便排序

    # 首先收集所有数据
    for json_file in json_files:
        file_path = os.path.join(input_dir, json_file)
        try:
            with open(file_path, "r", encoding="utf-8") as infile:
                data = json.load(infile)

            modified_data = modify_json(data, json_file)
            if modified_data is None:
                print(f"modify_json failed for file {json_file}")
                continue

            all_data.append(modified_data)

        except json.JSONDecodeError as e:
            print(f"The file {json_file} is not in valid JSON format: {str(e)}")
        except Exception as e:
            print(
                f"An unexpected error occurred when processing the file {json_file}: {str(e)}"
            )

    # 如果需要按id排序
    if sort_by_id and all_data:
        try:
            # 尝试按数字排序
            all_data.sort(key=lambda x: int(x["id"]))
        except (KeyError, ValueError):
            try:
                # 如果id不是数字或不存在，尝试按字符串排序
                all_data.sort(key=lambda x: str(x.get("id", "")))
            except Exception as e:
                print(f"Failed to sort by id: {str(e)}")
                # 如果排序失败，保持原始顺序

    # 写入输出文件
    with open(output_file, "w", encoding="utf-8") as outfile:
        for data in all_data:
            json.dump(data, outfile, ensure_ascii=False)
            outfile.write("\n")

    print(
        f"Processing completed. Successfully merged {len(all_data)}/{len(json_files)} JSON files to {output_file}"
    )


if __name__ == "__main__":
    # 替换为你的JSON文件目录
    input_directory = "/home/<USER>/Aini/testRepo/case/cdn/ST"
    # input_directory = "/home/<USER>/Aini/ZeroAgentCode/LocAgent/aini_test/dataset"
    # 替换为你想要的输出文件路径
    output_jsonl = "/home/<USER>/Aini/testRepo/case/cdn/ST_case.jsonl"
    # output_jsonl = "/home/<USER>/Aini/ZeroAgentCode/LocAgent/aini_test/dataset/mmvsops_case.jsonl"

    os.makedirs(os.path.dirname(output_jsonl), exist_ok=True)
    merge_json_to_jsonl_sort(input_directory, output_jsonl)
