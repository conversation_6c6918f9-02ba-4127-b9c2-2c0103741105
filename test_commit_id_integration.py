#!/usr/bin/env python3
"""
测试脚本：验证run.sh能够正确获取commit id并传递给auto_search_main.py
"""

import subprocess
import json
import os
import tempfile
import sys

def test_commit_id_extraction():
    """测试从Git获取commit id的功能"""
    print("测试Git commit id获取...")
    
    try:
        # 检查是否在Git仓库中
        result = subprocess.run(['git', 'rev-parse', '--git-dir'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 不在Git仓库中，无法测试commit id获取")
            return False
        
        # 获取当前commit id
        result = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 无法获取commit id")
            return False
        
        commit_id = result.stdout.strip()
        print(f"✅ 成功获取commit id: {commit_id}")
        
        # 验证commit id格式（应该是40个字符的十六进制字符串）
        if len(commit_id) == 40 and all(c in '0123456789abcdef' for c in commit_id.lower()):
            print("✅ commit id格式正确")
            return True
        else:
            print(f"❌ commit id格式错误: {commit_id}")
            return False
            
    except Exception as e:
        print(f"❌ 获取commit id时出错: {e}")
        return False

def test_run_sh_script():
    """测试run.sh脚本中的commit id获取逻辑"""
    print("\n测试run.sh脚本中的commit id获取逻辑...")
    
    try:
        # 创建一个简化的测试脚本来模拟run.sh中的逻辑
        test_script = '''#!/bin/bash
# 获取当前commit id
current_commit_id=""
if git rev-parse --git-dir > /dev/null 2>&1; then
    current_commit_id=$(git rev-parse HEAD)
    echo "Current commit ID: $current_commit_id"
else
    echo "Warning: Not in a Git repository. Cannot get commit ID."
    current_commit_id="unknown"
fi

echo "commit_id=$current_commit_id"
'''
        
        # 写入临时文件并执行
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(test_script)
            temp_script = f.name
        
        os.chmod(temp_script, 0o755)
        
        result = subprocess.run(['bash', temp_script], 
                              capture_output=True, text=True)
        
        # 清理临时文件
        os.unlink(temp_script)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"✅ 脚本执行成功:")
            for line in output.split('\n'):
                print(f"   {line}")
            
            # 检查是否包含commit_id
            if 'commit_id=' in output:
                commit_line = [line for line in output.split('\n') if line.startswith('commit_id=')][0]
                commit_id = commit_line.split('=', 1)[1]
                if commit_id and commit_id != "unknown":
                    print("✅ commit id获取成功")
                    return True
                else:
                    print("⚠️  commit id为空或unknown（可能不在Git仓库中）")
                    return True  # 这种情况也是正常的
            else:
                print("❌ 输出中没有找到commit_id")
                return False
        else:
            print(f"❌ 脚本执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试脚本执行时出错: {e}")
        return False

def test_auto_search_main_args():
    """测试auto_search_main.py是否正确接受commit_id参数"""
    print("\n测试auto_search_main.py参数解析...")
    
    try:
        # 创建一个简化的测试脚本来检查参数解析
        test_script = '''
import sys
import argparse
import json
import tempfile
import os

# 模拟auto_search_main.py中的参数解析部分
parser = argparse.ArgumentParser()
parser.add_argument("--commit_id", required=True, type=str)
parser.add_argument("--dataset", type=str, default="test")
parser.add_argument("--output_folder", type=str, default="/tmp")

try:
    args = parser.parse_args()
    print(f"✅ 成功解析commit_id参数: {args.commit_id}")
    
    # 测试保存到JSON
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(vars(args), f, indent=4)
        temp_json = f.name
    
    # 读取并验证JSON
    with open(temp_json, 'r') as f:
        saved_args = json.load(f)
    
    os.unlink(temp_json)
    
    if 'commit_id' in saved_args:
        print(f"✅ commit_id已保存到args.json: {saved_args['commit_id']}")
        print("✅ 参数解析和保存测试通过")
    else:
        print("❌ commit_id未保存到args.json")
        
except Exception as e:
    print(f"❌ 参数解析失败: {e}")
    sys.exit(1)
'''
        
        # 获取当前commit id作为测试参数
        result = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                              capture_output=True, text=True)
        test_commit_id = result.stdout.strip() if result.returncode == 0 else "test_commit_123"
        
        # 执行测试脚本
        result = subprocess.run([
            'python', '-c', test_script,
            '--commit_id', test_commit_id,
            '--dataset', 'test_dataset'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ auto_search_main.py参数解析测试通过")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print(f"❌ 参数解析测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 参数解析测试时出错: {e}")
        return False

def test_run_sh_syntax():
    """测试run.sh脚本语法是否正确"""
    print("\n测试run.sh脚本语法...")
    
    try:
        result = subprocess.run(['bash', '-n', 'scripts/run.sh'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ run.sh脚本语法检查通过")
            return True
        else:
            print(f"❌ run.sh脚本语法错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 语法检查时出错: {e}")
        return False

if __name__ == "__main__":
    print("开始测试commit id集成...")
    
    # 测试Git commit id获取
    git_test_passed = test_commit_id_extraction()
    
    # 测试run.sh脚本逻辑
    script_test_passed = test_run_sh_script()
    
    # 测试auto_search_main.py参数解析
    args_test_passed = test_auto_search_main_args()
    
    # 测试run.sh语法
    syntax_test_passed = test_run_sh_syntax()
    
    if git_test_passed and script_test_passed and args_test_passed and syntax_test_passed:
        print("\n🎉 所有测试通过! commit id集成功能正常工作")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查上述错误")
        sys.exit(1)
