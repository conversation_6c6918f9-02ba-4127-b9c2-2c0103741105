#!/usr/bin/env python3
"""
测试脚本：验证eval_metric.py中torch替换为NumPy是否正确工作
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_numpy_replacement():
    """测试NumPy替换是否正确工作"""
    print("测试eval_metric.py中的NumPy替换...")
    
    try:
        # 导入修改后的模块
        from evaluation.eval_metric import (
            _dcg, div_no_nan, normalized_dcg, recall_at_k, 
            acc_at_k, precision_at_k, average_precision_at_k
        )
        print("✅ 成功导入所有函数")
        
        # 创建测试数据
        pred_target = np.array([[1, 0, 1, 0, 1], [0, 1, 1, 0, 0]])
        ideal_target = np.array([[1, 1, 1, 0, 0], [1, 1, 0, 0, 0]])
        
        print("\n测试数据:")
        print(f"pred_target: {pred_target}")
        print(f"ideal_target: {ideal_target}")
        
        # 测试各个函数
        print("\n测试各个函数:")
        
        # 测试 _dcg
        dcg_result = _dcg(pred_target)
        print(f"✅ _dcg: {dcg_result}")
        
        # 测试 div_no_nan
        a = np.array([1.0, 2.0, 3.0])
        b = np.array([2.0, 0.0, 3.0])
        div_result = div_no_nan(a, b)
        print(f"✅ div_no_nan: {div_result}")
        
        # 测试 normalized_dcg
        ndcg_result = normalized_dcg(pred_target, ideal_target, k=3)
        print(f"✅ normalized_dcg@3: {ndcg_result}")
        
        # 测试 recall_at_k
        recall_result = recall_at_k(pred_target, ideal_target, k=3)
        print(f"✅ recall_at_k@3: {recall_result}")
        
        # 测试 acc_at_k
        acc_result = acc_at_k(pred_target, ideal_target, k=3)
        print(f"✅ acc_at_k@3: {acc_result}")
        
        # 测试 precision_at_k
        precision_result = precision_at_k(pred_target, ideal_target, k=3)
        print(f"✅ precision_at_k@3: {precision_result}")
        
        # 测试 average_precision_at_k
        map_result = average_precision_at_k(pred_target, ideal_target, k=3)
        print(f"✅ average_precision_at_k@3: {map_result}")
        
        print("\n🎉 所有函数测试通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

def test_data_types():
    """测试返回的数据类型是否正确"""
    print("\n测试数据类型...")
    
    try:
        from evaluation.eval_metric import normalized_dcg, recall_at_k
        
        pred_target = np.array([[1, 0, 1], [0, 1, 1]])
        ideal_target = np.array([[1, 1, 1], [1, 1, 0]])
        
        ndcg_result = normalized_dcg(pred_target, ideal_target, k=3)
        recall_result = recall_at_k(pred_target, ideal_target, k=3)
        
        print(f"✅ NDCG结果类型: {type(ndcg_result)}, 值: {ndcg_result}")
        print(f"✅ Recall结果类型: {type(recall_result)}, 值: {recall_result}")
        
        # 检查是否为NumPy类型
        assert isinstance(ndcg_result, (np.ndarray, np.floating, float)), f"NDCG结果类型错误: {type(ndcg_result)}"
        assert isinstance(recall_result, (np.ndarray, np.floating, float)), f"Recall结果类型错误: {type(recall_result)}"
        
        print("✅ 数据类型检查通过!")
        return True
        
    except Exception as e:
        print(f"❌ 数据类型测试失败: {e}")
        return False

def test_import_check():
    """检查是否还有torch导入"""
    print("\n检查torch导入...")
    
    try:
        with open('evaluation/eval_metric.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'import torch' in content or 'from torch' in content:
            print("❌ 仍然存在torch导入")
            return False
        
        if 'torch.' in content:
            print("❌ 仍然存在torch调用")
            return False
            
        print("✅ 已完全移除torch依赖")
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试eval_metric.py的NumPy替换...")
    
    # 测试导入检查
    import_test_passed = test_import_check()
    
    # 测试函数功能
    function_test_passed = test_numpy_replacement()
    
    # 测试数据类型
    type_test_passed = test_data_types()
    
    if import_test_passed and function_test_passed and type_test_passed:
        print("\n🎉 所有测试通过! torch已成功替换为NumPy")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查上述错误")
        sys.exit(1)
