from plugins.location_tools.repo_ops.repo_ops import Index, RepoOps

index = Index.build(repo_dir="/home/<USER>/Codebase/gerrit/PATCHER/")
repo_ops = RepoOps(index)

files, classes, funcs = repo_ops.get_current_repo_modules()
print(len(files))

print("==========================")
print(repo_ops.search_code_snippets(search_terms=["PatchDeleteController", "PatchDeleteService", "deleteSinglePatch"]))
print("==========================")
print(repo_ops.search_code_snippets(search_terms=["xxx"]))
print("==========================")
print(repo_ops.explore_tree_structure(start_entities=["/"]))
print("==========================")
print(repo_ops.get_entity_contents(entity_names=["daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/dispatch/HostInfoExeContext.java"]))