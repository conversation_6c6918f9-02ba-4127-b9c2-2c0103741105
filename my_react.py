import dspy
from typing import List
from auto_search_main import Localize
from util.prompts.pipelines.auto_search_prompt import TASK_INSTRUECTION
from plugins.location_tools.repo_ops.repo_ops import Index, RepoOps

dspy.enable_litellm_logging()

prompt_a = """You are a code retrieval specialist capable of analyzing requirements and searching for relevant implementations in a codebase using the ReAct (Reasoning and Acting) framework. You have access to tools for code search and file exploration.

    # Code Implementation Task Analysis Framework

    When processing code implementation tasks, you must clearly analyze the following key aspects:

    ## 1. Scenario Analysis
    First, determine which type of task you are dealing with:
    - **New Feature Addition**: Adding completely new functionality modules to the existing codebase
    - **Existing Feature Modification**: Enhancing existing functionality, fixing bugs, or refactoring based on current code

    ## 2. New Feature Addition Strategy
    When implementing new features:
    - Analyze the codebase structure to determine the optimal placement for new functionality
    - Identify existing components, utility classes, and infrastructure code needed for the new feature
    - Analyze similar functionality implementation patterns as reference for new feature development
    - Ensure new functionality maintains consistency with existing architecture and design patterns

    ## 3. Existing Feature Modification Strategy
    When modifying existing features:
    - First, precisely locate the target functionality within the codebase
    - Thoroughly analyze dependency relationships, including:
    - Direct dependencies on classes, methods, and modules
    - Which other components call or depend on this functionality
    - Related configuration files and data structures
    - Assess the potential impact scope of modifications on the entire system
    - Identify related code that needs synchronous updates

    ## 4. Perform Retrieval Tasks

    Follow this framework to perform retrieval tasks:

    # 1. Task Type and Requirement Analysis
    First, apply the Code Implementation Task Analysis Framework:

    ## Step 1: Determine Task Type
    - Analyze whether this is a **New Feature Addition** or **Existing Feature Modification**
    - Document your analysis reasoning

    ## Step 2: Extract Key Information
    When analyzing user requirements, extract the following key information:

    Core Functionality: Primary actions and targets
    Data Elements: Related entities, models, and data structures
    Interface Definitions: API paths, parameters, and response formats
    Business Rules: Validation logic, conditional checks, and processing flows
    Exception Scenarios: Error handling and exception messages

    ## Step 3: Apply Appropriate Strategy
    - **For New Features**: Focus on finding similar implementations, architectural patterns, and infrastructure components
    - **For Existing Features**: Prioritize locating the exact target functionality and its dependency network

    After completing the analysis, output a list of key search terms and technical elements.

    # 2. Task-Specific Retrieval Strategy Design
    Design a multi-level retrieval strategy based on task type:

    ## For New Feature Addition:
    - **Architecture Retrieval**: Search for similar functionality patterns and architectural components
    - **Infrastructure Retrieval**: Locate utility classes, base classes, and shared infrastructure
    - **Pattern Retrieval**: Find implementation patterns that can serve as templates
    - **Integration Retrieval**: Identify integration points and configuration requirements

    ## For Existing Feature Modification:
    - **Target Retrieval**: Precisely locate the specific functionality to be modified
    - **Dependency Retrieval**: Map all direct and indirect dependencies
    - **Impact Retrieval**: Identify all components that might be affected by changes
    - **Related Retrieval**: Find configuration files, data structures, and related components

    ## Common Retrieval Levels:
    - Broad Retrieval: Search for related components, modules, and foundational structures
    - Functional Retrieval: Locate implementation code for core functionality
    - Data Retrieval: Find data models and storage operations
    - Interface Retrieval: Identify API definitions and processing logic
    - Exception Retrieval: Discover error handling and validation logic

    Construct an initial search query containing core functionality and key technical elements.

    # 3. Iterative Analysis and Optimization
    After each retrieval:

    Extract newly discovered class names, method names, and file paths
    Identify calling relationships between code components
    Determine information gaps and areas requiring deeper exploration
    Build more precise follow-up queries

    Use the following criteria to decide whether to continue searching:

    Whether the core functionality has been located
    Whether key components are complete
    Whether the execution flow is clear
    Whether new searches can provide valuable information

    # 4. Retrieval Termination and Result Consolidation
    Stop retrieval when any of the following conditions are met:

    All core functionalities have corresponding implementations
    A complete execution flow can be described
    The user’s specific question has been answered
    A functionality is confirmed as unimplemented or incomplete
    Successive retrievals yield diminishing returns
    Consolidate the retrieval results and present them categorized by functionality, data, interfaces, and exception handling.

    5. Codebase Adaptation
    Adjust the retrieval strategy based on code characteristics:

    Programming Language Features: Adapt search focus according to language idioms
    Framework Specifics: Pay attention to framework-specific components and configurations
    Architecture Patterns: Adjust search paths based on different architectures
    Project Scale: For large projects, focus on module boundaries; for small projects, examine file organization

    # Execution Instructions
    Upon receiving user requirements, perform retrieval following the steps above, ensuring:

    1. **Apply Task Analysis Framework**: First determine if this is new feature addition or existing feature modification
    2. **Maintain Language Consistency**: **CRITICAL** - Use the same language as user input for all search queries to avoid translation errors
    3. **Analyze requirements and extract key elements**: Use the appropriate strategy based on task type
    4. **Design task-specific retrieval strategy**: Choose between new feature or modification approach
    5. **Execute initial retrieval**: Use targeted search queries based on task analysis, maintaining original language
    6. **Analyze results and optimize subsequent queries**: Focus on gaps identified by task type analysis
    7. **Determine whether further retrieval is necessary**: Consider completeness criteria for the specific task type
    8. **Consolidate results and adapt to codebase characteristics**: Present findings organized by task requirements

    **Language Processing Priority**: Always preserve the original language and terminology from user input in search queries. Translation can introduce semantic errors and cause missed matches for exact technical terms, API names, and domain-specific vocabulary.

    Clearly document the task type analysis and retrieval process at each step to ensure transparent and traceable retrieval.
    """

prompt_b = """Given the following task, your objective is to localize the specific files, classes or functions, and lines of code that need modification or contain key information to resolve the task."""

if __name__ == "__main__":
    lm = dspy.LM(
        "openai/DeepSeek-V3",
        cache=False,
        num_retries=15,
        api_key="7d6c438d2b0441bd96c79d885fb06503",
        base_url="https://maas-apigateway.dt.zte.com.cn/STREAM/deepseek-v3-api/v1",
    )
    dspy.configure(lm=lm)
    start = len(dspy.settings.lm.history)
    index = Index.get(repo_dir="/home/<USER>/Codebase/gerrit/PATCHER/")
    repo_ops = RepoOps(index)
    react = dspy.ReAct(
        Localize,
        max_iters=10,
        tools=[
            repo_ops.search_code_snippets,
            repo_ops.explore_tree_structure,
            repo_ops.get_entity_contents,
        ],
    )

    problem_statement = """# **用户故事**

    作为运维人员，我希望能够通过新增的API接口删除指定名称的单个补丁

    验收准则：

    ​    **Scenario 1: 成功删除未应用的补丁**

    ​    Given: 存在一个名为 "patch_v1.0" 的补丁

    ​    And: 该补丁未出现在补丁历史记录中 (未应用过)

    ​    When: 调用删除单个补丁接口

    ​    Then:删除补丁详情表中指定补丁

    ​    And: 删除补丁分发表中指定补丁



    ​    **Scenario 2: 无法删除已应用的补丁**

    ​    Given: 存在一个名为 "patch_v2.0" 的补丁

    ​    And: 该补丁已出现在补丁历史记录中 (已应用过)

    ​    When: 调用删除单个补丁接口

    ​    Then: 抛出异常，提示 "patch has updated, patchName=patch_v2.0"

    ​    And: 所有服务中的补丁信息保持不变


    # 业务流程


    ```
    @startuml
    actor 用户 as user
    participant "补丁删除服务" as patcherService
    participant "数据库" as Database

    user -> patcherService: 调用删除补丁接口

    group 前置校验
    patcherService -> Database: 查询补丁历史记录
    Database --> patcherService: 返回历史记录
    alt 补丁已应用
    patcherService --> user: 抛出异常("补丁已升级")
    else 未升级
    patcherService -> Database: 删除补丁详情记录
    patcherService -> Database: 删除补丁分发记录
    end
    end

    patcherService --> user: 返回操作结果
    @enduml
    ```

    # 接口定义

    ```json
    {
        "swagger": "2.0",
        "info": {
            "version": "1.0.0",
            "title": "补丁删除API",
            "description": "提供补丁删除操作的接口"
        },
        "host": "api.example.com",
        "basePath": "/",
        "schemes": [
            "https"
        ],
        "consumes": [
            "application/json"
        ],
        "produces": [
            "application/json"
        ],
        "paths": {
            "/patches/delete/singlePatch": {
                "post": {
                    "tags": [
                        "补丁删除"
                    ],
                    "summary": "删除单个补丁",
                    "description": "删除单个补丁",
                    "operationId": "deleteSinglePatch",
                    "parameters": [
                        {
                            "name": "patchName",
                            "in": "query",
                            "description": "补丁名称",
                            "required": true,
                            "type": "string",
                            "example": "patch-1.0.0"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "删除成功",
                            "schema": {
                                "type": "string",
                                "example": "success"
                            }
                        },
                        "400": {
                            "description": "参数错误",
                            "schema": {
                                "type": "string",
                                "example": "参数错误"
                            }
                        },
                        "500": {
                            "description": "服务器内部错误",
                            "schema": {
                                "type": "string",
                                "example": "服务器内部错误"
                            }
                        }
                    }
                }
            }
        }
    }
    ```

    # DAIP Patcher 代码库规范文档

    ## 项目定位
    DAIP Patcher 是数据智能分析平台的补丁管理系统，实现补丁全生命周期管理，包含以下核心功能：
    - 补丁上传
    - 补丁验证
    - 补丁分发
    - 补丁应用
    - 补丁回滚
    - 去重历史记录管理

    ## 术语映射表
    | 业务领域术语 | 代码英文表达 | 数据库表名 | API 路径 |
    |--------------|--------------|------------|----------|
    | 补丁分发     | PatchDispatch | dapmanager_patch_dispatch |  |
    | 补丁详情     | PatchInfo     | dapmanager_patch_detail_info |  |
    | 补丁历史     | PatchHistory  | dapmanager_patch_history |  |

    ## 代码架构体系
    采用DDD分层架构模式，模块交互关系如下：
    ```
    [Interfaces Layer] <-> [Application Layer] <-> [Domain Layer] <-> [Infrastructure Layer]
    ```

    ## 目录结构说明
    ```
    ${cwd.toPosix()}/daip-patcher-handler/            # 前端交互处理模块
    ${cwd.toPosix()}/daip-patcher-handler/daip-patcher-handler-api/    # 前端API接口定义
    ${cwd.toPosix()}/daip-patcher-handler/daip-patcher-handler-impl/  # 前端业务实现
    ${cwd.toPosix()}/daip-patcher-init/              # 系统初始化模块
    ${cwd.toPosix()}/daip-patcher-service/            # 后端服务核心模块
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-interfaces/    # 接口层：REST API定义
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-application/   # 应用层：业务流程协调
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain/         # 领域层：核心业务逻辑
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/              # 通用业务服务组件
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api/ # 基础设施层：外部交互
    ${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/        # 数据访问接口定义
    ```

    ## 模块说明
    ### 模块物理路径

    1. 接口层（Interfaces Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-interfaces`
    2. 应用层（Application Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-application`
    3. 领域层（Domain Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain`
    4. 基础设施层（Infrastructure Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api`

    **特别提醒**：上面接口层、应用层、领域层和基础设施层对应模块都在 `${cwd.toPosix()}/daip-patcher-service`目录下，请勿遗留`daip-patcher-service`

    ### daip-patcher-interfaces模块
    **核心职责**： 该模块作为接口层，主要负责处理外部请求与系统交互的入口，具体包括：
    - 定义RESTful API接口规范
    - 处理HTTP请求的路由与参数校验
    - 将外部请求转换为应用层可处理的指令

    ### daip-patcher-application模块
    **核心职责**： 该模块作为应用层，主要处理补丁管理系统的业务逻辑协调工作，具体包括：
    - 事件监听与消息消费功能，包括补丁分发事件监听、补丁上传事件监听、服务安装消息监听等
    - 事件驱动架构实现（处理补丁分发、回滚等事件）
    - 领域层与基础设施层的适配器

    ### daip-patcher-domain模块
    **核心职责**： 该模块作为领域层，主要承载补丁管理系统的核心业务逻辑与数据模型，具体包括：
    - 定义补丁生命周期的核心领域模型
    - 实现补丁操作与调度的核心业务规则
    - 提供领域服务接口与基础操作实现

    ### daip-patcher-infrastructure-api模块
    **核心职责**： 该模块作为基础设施层，主要负责系统与外部组件的交互及底层资源管理，具体包括：
    - 提供数据库访问接口，使用Spring-jpa做ORM持久化实现。
    - 管理与外部系统的通信（如环境配置、IP修改服务）
    - 实现缓存与状态存储机制\no think"""

    problem_statement = """# **用户故事**

    作为运维人员，我希望能够通过新增的API接口删除指定名称的单个补丁

    验收准则：

    ​    **Scenario 1: 成功删除未应用的补丁**

    ​    Given: 存在一个名为 "patch_v1.0" 的补丁

    ​    And: 该补丁未出现在补丁历史记录中 (未应用过)

    ​    When: 调用删除单个补丁接口

    ​    Then:删除补丁详情表中指定补丁

    ​    And: 删除补丁分发表中指定补丁



    ​    **Scenario 2: 无法删除已应用的补丁**

    ​    Given: 存在一个名为 "patch_v2.0" 的补丁

    ​    And: 该补丁已出现在补丁历史记录中 (已应用过)

    ​    When: 调用删除单个补丁接口

    ​    Then: 抛出异常，提示 "patch has updated, patchName=patch_v2.0"

    ​    And: 所有服务中的补丁信息保持不变


    # 业务流程


    ```
    @startuml
    actor 用户 as user
    participant "补丁删除服务" as patcherService
    participant "数据库" as Database

    user -> patcherService: 调用删除补丁接口

    group 前置校验
    patcherService -> Database: 查询补丁历史记录
    Database --> patcherService: 返回历史记录
    alt 补丁已应用
    patcherService --> user: 抛出异常("补丁已升级")
    else 未升级
    patcherService -> Database: 删除补丁详情记录
    patcherService -> Database: 删除补丁分发记录
    end
    end

    patcherService --> user: 返回操作结果
    @enduml
    ```

    # 接口定义

    ```json
    {
        "swagger": "2.0",
        "info": {
            "version": "1.0.0",
            "title": "补丁删除API",
            "description": "提供补丁删除操作的接口"
        },
        "host": "api.example.com",
        "basePath": "/",
        "schemes": [
            "https"
        ],
        "consumes": [
            "application/json"
        ],
        "produces": [
            "application/json"
        ],
        "paths": {
            "/patches/delete/singlePatch": {
                "post": {
                    "tags": [
                        "补丁删除"
                    ],
                    "summary": "删除单个补丁",
                    "description": "删除单个补丁",
                    "operationId": "deleteSinglePatch",
                    "parameters": [
                        {
                            "name": "patchName",
                            "in": "query",
                            "description": "补丁名称",
                            "required": true,
                            "type": "string",
                            "example": "patch-1.0.0"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "删除成功",
                            "schema": {
                                "type": "string",
                                "example": "success"
                            }
                        },
                        "400": {
                            "description": "参数错误",
                            "schema": {
                                "type": "string",
                                "example": "参数错误"
                            }
                        },
                        "500": {
                            "description": "服务器内部错误",
                            "schema": {
                                "type": "string",
                                "example": "服务器内部错误"
                            }
                        }
                    }
                }
            }
        }
    }
    ```
    """

    pred = react(problem_statement=problem_statement)

    end = len(dspy.settings.lm.history)
    print(dspy.settings.lm.inspect_history(end - start))
    print(pred)
