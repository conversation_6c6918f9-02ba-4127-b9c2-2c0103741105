# **用户故事**

作为运维人员，我希望能够通过新增的API接口删除指定名称的单个补丁

验收准则：

​    **Scenario 1: 成功删除未应用的补丁**

​    Given: 存在一个名为 "patch_v1.0" 的补丁

​    And: 该补丁未出现在补丁历史记录中 (未应用过)

​    When: 调用删除单个补丁接口

​    Then:删除补丁详情表中指定补丁

​    And: 删除补丁分发表中指定补丁



​    **Scenario 2: 无法删除已应用的补丁**

​    Given: 存在一个名为 "patch_v2.0" 的补丁

​    And: 该补丁已出现在补丁历史记录中 (已应用过)

​    When: 调用删除单个补丁接口

​    Then: 抛出异常，提示 "patch has updated, patchName=patch_v2.0"

​    And: 所有服务中的补丁信息保持不变


# 业务流程


```
@startuml
actor 用户 as user
participant "补丁删除服务" as patcherService
participant "数据库" as Database

user -> patcherService: 调用删除补丁接口

group 前置校验
patcherService -> Database: 查询补丁历史记录
Database --> patcherService: 返回历史记录
alt 补丁已应用
patcherService --> user: 抛出异常("补丁已升级")
else 未升级
patcherService -> Database: 删除补丁详情记录
patcherService -> Database: 删除补丁分发记录
end
end

patcherService --> user: 返回操作结果
@enduml
```

# 接口定义

```json
{
    "swagger": "2.0",
    "info": {
        "version": "1.0.0",
        "title": "补丁删除API",
        "description": "提供补丁删除操作的接口"
    },
    "host": "api.example.com",
    "basePath": "/",
    "schemes": [
        "https"
    ],
    "consumes": [
        "application/json"
    ],
    "produces": [
        "application/json"
    ],
    "paths": {
        "/patches/delete/singlePatch": {
            "post": {
                "tags": [
                    "补丁删除"
                ],
                "summary": "删除单个补丁",
                "description": "删除单个补丁",
                "operationId": "deleteSinglePatch",
                "parameters": [
                    {
                        "name": "patchName",
                        "in": "query",
                        "description": "补丁名称",
                        "required": true,
                        "type": "string",
                        "example": "patch-1.0.0"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "type": "string",
                            "example": "success"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "type": "string",
                            "example": "参数错误"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "type": "string",
                            "example": "服务器内部错误"
                        }
                    }
                }
            }
        }
    }
}
```

# DAIP Patcher 代码库规范文档

## 项目定位
DAIP Patcher 是数据智能分析平台的补丁管理系统，实现补丁全生命周期管理，包含以下核心功能：
- 补丁上传
- 补丁验证
- 补丁分发
- 补丁应用
- 补丁回滚
- 去重历史记录管理

## 术语映射表
| 业务领域术语 | 代码英文表达 | 数据库表名 | API 路径 |
|--------------|--------------|------------|----------|
| 补丁分发     | PatchDispatch | dapmanager_patch_dispatch |  |
| 补丁详情     | PatchInfo     | dapmanager_patch_detail_info |  |
| 补丁历史     | PatchHistory  | dapmanager_patch_history |  |

## 代码架构体系
采用DDD分层架构模式，模块交互关系如下：
```
[Interfaces Layer] <-> [Application Layer] <-> [Domain Layer] <-> [Infrastructure Layer]
```

## 目录结构说明
```
${cwd.toPosix()}/daip-patcher-handler/            # 前端交互处理模块
${cwd.toPosix()}/daip-patcher-handler/daip-patcher-handler-api/    # 前端API接口定义
${cwd.toPosix()}/daip-patcher-handler/daip-patcher-handler-impl/  # 前端业务实现
${cwd.toPosix()}/daip-patcher-init/              # 系统初始化模块
${cwd.toPosix()}/daip-patcher-service/            # 后端服务核心模块
${cwd.toPosix()}/daip-patcher-service/daip-patcher-interfaces/    # 接口层：REST API定义
${cwd.toPosix()}/daip-patcher-service/daip-patcher-application/   # 应用层：业务流程协调
${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain/         # 领域层：核心业务逻辑
${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/              # 通用业务服务组件
${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api/ # 基础设施层：外部交互
${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/        # 数据访问接口定义
```

## 模块说明
### 模块物理路径

1. 接口层（Interfaces Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-interfaces`
2. 应用层（Application Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-application`
3. 领域层（Domain Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-domain`
4. 基础设施层（Infrastructure Layer）: `${cwd.toPosix()}/daip-patcher-service/daip-patcher-infrastructure-api`

**特别提醒**：上面接口层、应用层、领域层和基础设施层对应模块都在 `${cwd.toPosix()}/daip-patcher-service`目录下，请勿遗留`daip-patcher-service`

### daip-patcher-interfaces模块
**核心职责**： 该模块作为接口层，主要负责处理外部请求与系统交互的入口，具体包括：
- 定义RESTful API接口规范
- 处理HTTP请求的路由与参数校验
- 将外部请求转换为应用层可处理的指令

### daip-patcher-application模块
**核心职责**： 该模块作为应用层，主要处理补丁管理系统的业务逻辑协调工作，具体包括：
- 事件监听与消息消费功能，包括补丁分发事件监听、补丁上传事件监听、服务安装消息监听等
- 事件驱动架构实现（处理补丁分发、回滚等事件）
- 领域层与基础设施层的适配器

### daip-patcher-domain模块
**核心职责**： 该模块作为领域层，主要承载补丁管理系统的核心业务逻辑与数据模型，具体包括：
- 定义补丁生命周期的核心领域模型
- 实现补丁操作与调度的核心业务规则
- 提供领域服务接口与基础操作实现

### daip-patcher-infrastructure-api模块
**核心职责**： 该模块作为基础设施层，主要负责系统与外部组件的交互及底层资源管理，具体包括：
- 提供数据库访问接口，使用Spring-jpa做ORM持久化实现。
- 管理与外部系统的通信（如环境配置、IP修改服务）
- 实现缓存与状态存储机制

trajectory={'thought_0': 'To implement the patch deletion functionality, we need to locate the relevant code in the codebase. Based on the project structure and the business process described, the following components need to be examined:

1. **Interface Layer**: The REST API endpoint for deleting a patch should be defined in the `daip-patcher-interfaces` module.
2. **Application Layer**: The business logic for coordinating the deletion process should be in the `daip-patcher-application` module.
3. **Domain Layer**: The core business logic for checking if a patch is applied or not should be in the `daip-patcher-domain` module.
4. **Infrastructure Layer**: The database operations for deleting patch details, dispatch records, and querying history should be in the `daip-patcher-infrastructure-api` module.

We will start by searching for the API endpoint in the `daip-patcher-interfaces` module.', 'tool_name_0': 'search_code_snippets', 'tool_args_0': {'search_terms': ['daip-patcher-interfaces', 'deleteSinglePatch'], 'file_path_or_pattern': '**/daip-patcher-service/daip-patcher-interfaces/**/*.java'}, 'observation_0': 'No files found for file pattern \'**/daip-patcher-service/daip-patcher-interfaces/**/*.java\'. Will search all files.
...
##Searching for term "daip-patcher-interfaces", "daip-patcher-interfaces deleteSinglePatch"...
### Search Result:
Found method `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchSeedController.java:PatchSeedController.queryAll`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchOfflineRollbackController.java:PatchOfflineRollbackController.deleteRollbackServicePatchHistory`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDispatchController.java:PatchDispatchController.updateDispatchResult`.
Source: Retrieved code content using keyword search (bm25).

Found class `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchOfflineRollbackController.java:PatchOfflineRollbackController`.
Source: Retrieved code content using keyword search (bm25).
```
14 | @Slf4j
15 | @RestController
16 | public class PatchOfflineRollbackController implements PatchOfflineRollbackControllerApi {
17 | 
18 |     @Autowired
19 |     private PatchRollbackCacheQueue patchRollbackCacheQueue;
20 |     @Autowired
21 |     private SpringAppCsrfProtector springAppCsrfProtector;
22 | 
23 |     @Override
24 |     public void deleteRollbackServicePatchHistory(List<PatchServiceParam> patchServiceParams) throws DaipBaseException {
25 |         springAppCsrfProtector.validRequest();
26 |         patchRollbackCacheQueue.add(patchServiceParams);
27 |     }
28 | }
```

Found class `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDispatchController.java:PatchDispatchController`.
Source: Retrieved code content using keyword search (bm25).
```
79 | @RestController
80 | @Slf4j
81 | public class PatchDispatchController implements PatchDispatchControllerApi
82 | {
83 |     @Autowired
84 |     private PatchDispatchCacheService patchDispatchCacheService;
85 | 
86 |     @Autowired
87 |     private SpringAppCsrfProtector springAppCsrfProtector;
88 | 
89 |     @Override
90 |     public void updateDispatchResult(@RequestBody List<PatchDispatchResult> dispatchResults)
91 |     {
92 |         springAppCsrfProtector.validRequest();
93 |         patchDispatchCacheService.add(dispatchResults);
94 |     }
95 | }
```

Found file `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatcherUpdateController.java`.
Found file `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatcherRollUpdateController.java`.
Found file `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatcherUploadController.java`.
Found file `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchRollbackController.java`.
Found file `daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDispatchController.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.



##Searching for term "deleteSinglePatch"...
### Search Result:
Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.generateDistributeHosts`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.getListener`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.rollback`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.notInHistory`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.queryPatchHostByServiceAndPatch`.
Source: Retrieved code content using keyword search (bm25).

Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatcherUpdateHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/dto/PatchPageTableDto.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.', 

'thought_1': 'The search in the `daip-patcher-interfaces` module did not find a method directly related to deleting a single patch. However, the business process UML mentions a "补丁删除服务" (Patch Deletion Service), which suggests the API might be implemented in a dedicated controller class. Let\'s search for the API path `/patches/delete/singlePatch` in the `daip-patcher-interfaces` module to locate the endpoint definition.', 'tool_name_1': 'search_code_snippets', 'tool_args_1': {'search_terms': ['/patches/delete/singlePatch'], 'file_path_or_pattern': '**/daip-patcher-service/daip-patcher-interfaces/**/*.java'}, 'observation_1': 'No files found for file pattern \'**/daip-patcher-service/daip-patcher-interfaces/**/*.java\'. Will search all files.
...
##Searching for term "/patches/delete/singlePatch"...
### Search Result:
Found class `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/dto/PatchPageTableDto.java:PatchPageTableDto`.
Source: Retrieved entity using keyword search (bm25).
```
50 | @Getter
51 | @Setter
52 | @NoArgsConstructor
53 | @AllArgsConstructor
54 | @ApiModel(value = "补丁页面查询分页对象")
55 | public class PatchPageTableDto extends PageTableBean
56 | {
57 |     @ApiModelProperty(value = "补丁类型", example = "zookeeper", dataType = "String")
58 |     private String patchType;
59 |     @ApiModelProperty (value = "搜索关键字", example = "DAP-ZOOKEEPER-V20.20.40.08-SP001-20210111", dataType = "String")
60 |     private String searchKeyWord;
61 | }
```

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/CleanPatchTaskByClusterDeleted.java:CleanPatchTaskByClusterDeleted.handle`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/PatchTaskOperateService.java:PatchTaskOperateService.deletePatchTaskWhenModifyOperateType`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/PatchTaskOperateService.java:PatchTaskOperateService.queryPatchTaskRollBackResource`.
Source: Retrieved code content using keyword search (bm25).

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/CleanPatchTaskByClusterDeleted.java:CleanPatchTaskByClusterDeleted`.
Source: Retrieved code content using keyword search (bm25).
```
35 | @Service
36 | @Slf4j
37 | @EventListenerTo("clusterDeleted")
38 | public class CleanPatchTaskByClusterDeleted implements ConsumerHandler<String> {
39 | 
40 |     @Autowired
41 |     private PatchTaskService patchTaskService;
42 | 
43 |     @Override
44 |     /* Started by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
45 |     public String handle(String body) throws MessageCenterException {
46 |         log.info("Receive clean patch task msg.");
47 |         RequestMessageBody<List<ClusterBean>> messageBody =
48 |             JSON.parseObject(body, new TypeReference<RequestMessageBody<List<ClusterBean>>>() {});
49 |         if (messageBody != null && messageBody.getBody() != null) {
50 |             List<String> clusterIds = messageBody.getBody().stream().map(bean -> String.valueOf(bean.getClusterId())).collect(Collectors.toList());
51 |             List<PatchTaskPo> patchTaskPos = patchTaskService.queryAllPatchTasks();
52 |             List<Long> needDeleteTaskIds = patchTaskPos.stream()
53 |                 .filter(patchTaskPo -> clusterIds.contains(patchTaskPo.getClusterId())
54 |                     && !StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory()))
55 |                 .map(PatchTaskPo::getTaskId).collect(Collectors.toList());
56 |             if (!needDeleteTaskIds.isEmpty()) {
57 |                 log.info(String.format("need delete patch taskId=%s", needDeleteTaskIds));
58 |                 patchTaskService.deleteByTaskIds(needDeleteTaskIds);
59 |             } else {
60 |                 log.info("No patch tasks need to be deleted.");
61 |             }
62 |         }
63 |         return "success";
64 |     }
65 |     /* Ended by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
66 | }
```

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/utils/PatcherFileUtils.java:PatcherFileUtils`.
Source: Retrieved code content using keyword search (bm25).
```
 77 | @Slf4j
 78 | @Component
 79 | public class PatcherFileUtils {
 80 | 
 81 |     private PatcherFileUtils() {}
 82 | 
 83 |     public static void deletePatch(File unzipPatch) {
 84 |         if (unzipPatch.exists()) {
 85 |             try {
 86 |                 FileUtils.deleteDirectory(unzipPatch);
 87 |             } catch (IOException e) {
 88 |                 log.debug("delete temp patch error");
 89 |             }
 90 |         }
 91 |     }
 92 | 
 93 |     public static void copyFileToTempDir(MultipartFile[] patchMultipartFiles, String patchUploadTemp) {
 94 |         for (MultipartFile patch : patchMultipartFiles) {
 95 |             try {
 96 |                 String filename = patch.getOriginalFilename();
 97 |                 File destination = FilePathCleaner.newFile(patchUploadTemp, filename);
 98 |                 FileUtils.copyInputStreamToFile(patch.getInputStream(), destination);
 99 |             } catch (IOException e) {
100 |                 log.error("copy upload-file error : ", e);
101 |             }
102 |         }
103 |     }
104 | 
105 | }
```

Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/dto/PatchPageTableDto.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.', 'thought_2': "The API endpoint for `/patches/delete/singlePatch` was not found in the `daip-patcher-interfaces` module. Let's search the **application layer** (`daip-patcher-application`) for business logic related to patch deletion, as this layer coordinates domain operations and infrastructure interactions.", 'tool_name_2': 'search_code_snippets', 'tool_args_2': {'search_terms': ['deleteSinglePatch', 'delete patch'], 'file_path_or_pattern': '**/daip-patcher-service/daip-patcher-application/**/*.java'}, 'observation_2': 'No files found for file pattern \'**/daip-patcher-service/daip-patcher-application/**/*.java\'. Will search all files.
...
##Searching for term "deleteSinglePatch", "delete patch", "deleteSinglePatch delete patch"...
### Search Result:
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Search `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java` for the full content if needed.



##Searching for term "deleteSinglePatch"...
### Search Result:
Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.generateDistributeHosts`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.getListener`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.rollback`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.notInHistory`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.queryPatchHostByServiceAndPatch`.
Source: Retrieved code content using keyword search (bm25).

Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatcherUpdateHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/dto/PatchPageTableDto.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.



##Searching for term "delete patch"...
### Search Result:
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchTaskHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchUploadHandler.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.



##Searching for term "delete patch", "deleteSinglePatch delete patch"...
### Search Result:
Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/CleanPatchTaskByClusterDeleted.java:CleanPatchTaskByClusterDeleted.handle`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/PatchTaskOperateService.java:PatchTaskOperateService.deletePatchTaskWhenModifyOperateType`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/PatchTaskOperateService.java:PatchTaskOperateService.queryPatchTaskRollBackResource`.
Source: Retrieved code content using keyword search (bm25).

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/task/service/CleanPatchTaskByClusterDeleted.java:CleanPatchTaskByClusterDeleted`.
Source: Retrieved code content using keyword search (bm25).
```
35 | @Service
36 | @Slf4j
37 | @EventListenerTo("clusterDeleted")
38 | public class CleanPatchTaskByClusterDeleted implements ConsumerHandler<String> {
39 | 
40 |     @Autowired
41 |     private PatchTaskService patchTaskService;
42 | 
43 |     @Override
44 |     /* Started by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
45 |     public String handle(String body) throws MessageCenterException {
46 |         log.info("Receive clean patch task msg.");
47 |         RequestMessageBody<List<ClusterBean>> messageBody =
48 |             JSON.parseObject(body, new TypeReference<RequestMessageBody<List<ClusterBean>>>() {});
49 |         if (messageBody != null && messageBody.getBody() != null) {
50 |             List<String> clusterIds = messageBody.getBody().stream().map(bean -> String.valueOf(bean.getClusterId())).collect(Collectors.toList());
51 |             List<PatchTaskPo> patchTaskPos = patchTaskService.queryAllPatchTasks();
52 |             List<Long> needDeleteTaskIds = patchTaskPos.stream()
53 |                 .filter(patchTaskPo -> clusterIds.contains(patchTaskPo.getClusterId())
54 |                     && !StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory()))
55 |                 .map(PatchTaskPo::getTaskId).collect(Collectors.toList());
56 |             if (!needDeleteTaskIds.isEmpty()) {
57 |                 log.info(String.format("need delete patch taskId=%s", needDeleteTaskIds));
58 |                 patchTaskService.deleteByTaskIds(needDeleteTaskIds);
59 |             } else {
60 |                 log.info("No patch tasks need to be deleted.");
61 |             }
62 |         }
63 |         return "success";
64 |     }
65 |     /* Ended by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
66 | }
```

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/utils/PatcherFileUtils.java:PatcherFileUtils`.
Source: Retrieved code content using keyword search (bm25).
```
 77 | @Slf4j
 78 | @Component
 79 | public class PatcherFileUtils {
 80 | 
 81 |     private PatcherFileUtils() {}
 82 | 
 83 |     public static void deletePatch(File unzipPatch) {
 84 |         if (unzipPatch.exists()) {
 85 |             try {
 86 |                 FileUtils.deleteDirectory(unzipPatch);
 87 |             } catch (IOException e) {
 88 |                 log.debug("delete temp patch error");
 89 |             }
 90 |         }
 91 |     }
 92 | 
 93 |     public static void copyFileToTempDir(MultipartFile[] patchMultipartFiles, String patchUploadTemp) {
 94 |         for (MultipartFile patch : patchMultipartFiles) {
 95 |             try {
 96 |                 String filename = patch.getOriginalFilename();
 97 |                 File destination = FilePathCleaner.newFile(patchUploadTemp, filename);
 98 |                 FileUtils.copyInputStreamToFile(patch.getInputStream(), destination);
 99 |             } catch (IOException e) {
100 |                 log.error("copy upload-file error : ", e);
101 |             }
102 |         }
103 |     }
104 | 
105 | }
```



##Searching for term "deleteSinglePatch delete patch"...
### Search Result:
Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/event/PatchValidateEvent.java:PatchValidateEvent`.
Source: Retrieved entity using keyword search (bm25).
```
5 | public class PatchValidateEvent extends ApplicationEvent {
6 |     public PatchValidateEvent(Object source) {
7 |         super(source);
8 |     }
9 | }
```

Found file `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/event/PatchValidateEvent.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Search `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/event/PatchValidateEvent.java` for the full content if needed.', 


'thought_3': 'The API endpoint and deletion logic were not found in the interface and application layers. Next, we should check the **domain layer** (`daip-patcher-domain`) for core business logic related to patch deletion and the **infrastructure layer** (`daip-patcher-infrastructure-api`) for database operations. Specifically, we need to locate:
1. Domain services that check if a patch is in history (`notInHistory` logic).
2. Repository interfaces for `dapmanager_patch_detail_info` and `dapmanager_patch_dispatch` tables to handle deletions.', 'tool_name_3': 'search_code_snippets', 'tool_args_3': {'search_terms': ['PatchInfoRepository', 'PatchDispatchRepository', 'deleteByPatchName'], 'file_path_or_pattern': '**/daip-patcher-service/daip-patcher-infrastructure-api/**/*.java'}, 'observation_3': 'No files found for file pattern \'**/daip-patcher-service/daip-patcher-infrastructure-api/**/*.java\'. Will search all files.
...
##Searching for term "PatchInfoRepository"...
### Search Result:
Found file `daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java`.
Source: Match found for entity name `PatchInfoRepository`.
```
  1 | /**
  2 |  * <p>
  3 |  * <owner>10259451</owner>
  4 |  * </p>
  5 |  * <p>
  6 |  * <createdate>2015-2-28</createdate>
  7 |  * </p>
  8 |  * <p>
  9 |  * 文件名称: PatchInfoRepository.java
 10 |  * </p>
 11 |  * <p>
 12 |  * 文件描述: 无
 13 |  * </p>
 14 |  * <p>
 15 |  * 版权所有: 版权所有(C)2001-2020
 16 |  * </p>
 17 |  * <p>
 18 |  * 公司名称: 深圳市中兴通讯股份有限公司
 19 |  * </p>
 20 |  * <p>
 21 |  * 内容摘要: 无
 22 |  * </p>
 23 |  * <p>
 24 |  * 其他说明: 无
 25 |  * </p>
 26 |  * <p>
 27 |  * 创建日期：2021/3/10
 28 |  * </p>
 29 |  * <p>
 30 |  * 完成日期：2021/3/10
 31 |  * </p>
 32 |  * <p>
 33 |  * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 34 |  * </p>
 35 |  *
 36 |  * <pre>
 37 |  *    修改日期：
 38 |  *    版 本 号：
 39 |  *    修 改 人：
 40 |  *    修改内容：
 41 |  * </pre>
 42 |  * <p>
 43 |  * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 44 |  * </p>
 45 |  *
 46 |  * <pre>
 47 |  *    评审日期：
 48 |  *    版 本 号：
 49 |  *    评 审 人：
 50 |  *    评审内容：
 51 |  * </pre>
 52 |  *
 53 |  * @version 1.0
 54 |  * <AUTHOR>
 55 |  */
 56 | package com.zte.daip.manager.patcher.infrastructure.repository;
 57 | 
 58 | import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
 59 | import org.springframework.data.jpa.repository.JpaRepository;
 60 | import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
 61 | import org.springframework.data.jpa.repository.Modifying;
 62 | import org.springframework.data.jpa.repository.Query;
 63 | import org.springframework.data.repository.query.Param;
 64 | import org.springframework.stereotype.Repository;
 65 | import org.springframework.transaction.annotation.Transactional;
 66 | 
 67 | import java.util.List;
 68 | 
 69 | /**
 70 |  * 功能描述:<br>
 71 |  * <p>
 72 |  * <p>
 73 |  * <p>
 74 |  * Note:
 75 |  *
 76 |  * <AUTHOR>
 77 |  * @version 1.0
 78 |  */
 79 | @Repository
 80 | @Transactional(rollbackFor = Exception.class)
 81 | public interface PatchInfoRepository
 82 |     extends JpaRepository<PatchDetailPo, Long>, JpaSpecificationExecutor<PatchDetailPo> {
 83 | 
 84 |     List<PatchDetailPo> findByIsFullPatch(int isFullPatch);
 85 | 
 86 |     @Modifying(clearAutomatically = true)
 87 |     @Query(value = "select p.patchName from  PatchDetailPo p")
 88 |     List<String> queryPatchName();
 89 | 
 90 |     @Modifying(clearAutomatically = true)
 91 |     @Query(value = "select p from  PatchDetailPo p where p.patchName not like CONCAT(\'%schema%\') ")
 92 |     List<PatchDetailPo> queryAllPatchExceptScheme();
 93 | 
 94 |     List<PatchDetailPo> findByServiceAndBaseVersion(String service, String baseVersion);
 95 | 
 96 |     List<PatchDetailPo> findByService(String service);
 97 | 
 98 |     List<PatchDetailPo> findByPatchNameAndServiceAndBaseVersion(String patchName, String service, String baseVersion);
 99 | }
100 | 
```



##Searching for term "PatchDispatchRepository"...
### Search Result:
Found file `daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java`.
Source: Match found for entity name `PatchDispatchRepository`.
```
 1 | /**
 2 |  * <p>
 3 |  * <owner>10208178</owner>
 4 |  * </p>
 5 |  * <p>
 6 |  * <createdate>2015-2-28</createdate>
 7 |  * </p>
 8 |  * <p>
 9 |  * 文件名称: PatchDispatchRepository.java
10 |  * </p>
11 |  * <p>
12 |  * 文件描述: 无
13 |  * </p>
14 |  * <p>
15 |  * 版权所有: 版权所有(C)2001-2020
16 |  * </p>
17 |  * <p>
18 |  * 公司名称: 深圳市中兴通讯股份有限公司
19 |  * </p>
20 |  * <p>
21 |  * 内容摘要: 无
22 |  * </p>
23 |  * <p>
24 |  * 其他说明: 无
25 |  * </p>
26 |  * <p>
27 |  * 创建日期：2021/3/23
28 |  * </p>
29 |  * <p>
30 |  * 完成日期：2021/3/23
31 |  * </p>
32 |  * <p>
33 |  * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
34 |  * </p>
35 |  *
36 |  * <pre>
37 |  *    修改日期：
38 |  *    版 本 号：
39 |  *    修 改 人：
40 |  *    修改内容：
41 |  * </pre>
42 |  * <p>
43 |  * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
44 |  * </p>
45 |  *
46 |  * <pre>
47 |  *    评审日期：
48 |  *    版 本 号：
49 |  *    评 审 人：
50 |  *    评审内容：
51 |  * </pre>
52 |  *
53 |  * @version 1.0
54 |  * <AUTHOR>
55 |  */
56 | package com.zte.daip.manager.patcher.infrastructure.repository;
57 | 
58 | import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
59 | import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatchKey;
60 | import org.springframework.data.jpa.repository.JpaRepository;
61 | import org.springframework.data.jpa.repository.Modifying;
62 | import org.springframework.data.jpa.repository.Query;
63 | import org.springframework.data.repository.query.Param;
64 | import org.springframework.stereotype.Repository;
65 | import org.springframework.transaction.annotation.Transactional;
66 | 
67 | import java.util.List;
68 | 
69 | /**
70 |  * 功能描述:<br>
71 |  *
72 |  *
73 |  * <p>
74 |  * Note:
75 |  *
76 |  * <AUTHOR>
77 |  * @version 1.0
78 |  */
79 | @Repository
80 | @Transactional(rollbackFor = Exception.class)
81 | public interface PatchDispatchRepository extends JpaRepository<PatchDispatch, PatchDispatchKey> {
82 | 
83 |     @Modifying(clearAutomatically = true)
84 |     @Query(value = "select p from  PatchDispatch p where p.id.patchName = :patchName")
85 |     List<PatchDispatch> queryPatchDispatchByPatchKey(@Param("patchName") String patchName);
86 | 
87 |     @Modifying(clearAutomatically = true)
88 |     @Query(value = "select p from PatchDispatch p where p.id.ip in (:ips)")
89 |     List<PatchDispatch> queryByIpList(@Param("ips") List<String> ips);
90 | 
91 |     @Query(value = "select * from dapmanager_patch_dispatch where patch_name = ?1 and ip = ?2", nativeQuery = true)
92 |     PatchDispatch queryByPatchNameAndIp(String patchName, String ip);
93 | 
94 |     List<PatchDispatch> findBySuccess(boolean success);
95 | 
96 |     @Modifying(clearAutomatically = true)
97 |     @Query(value = "select p from  PatchDispatch p where p.id.patchName = :patchName and p.success = :success")
98 |     List<PatchDispatch> findByPatchNameAndSuccess(String patchName, boolean success);
99 | }
```



##Searching for term "deleteByPatchName"...
### Search Result:
Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/taskmodel/service/TaskModelRetryService.java:TaskModelRetryService`.
Source: Retrieved entity using keyword search (bm25).
```
29 | @Service
30 | @Slf4j
31 | public class TaskModelRetryService {
32 |     private static final String LOCK_KEY_PREFIX = "PATCHER_LOAD_TASK_MODEL";
33 |     private static final int LOCK_LEASE_TIME = 60;
34 | 
35 |     @Autowired
36 |     private TaskModelApi taskModelApi;
37 | 
38 |     @Autowired
39 |     private LockUtils lockUtils;
40 | 
41 |     @Async
42 |     @Retryable(value = {Exception.class}, backoff = @Backoff(delay = 3000L), maxAttempts = Integer.MAX_VALUE)
43 |     public void saveTaskModelRetry(List<TaskModelDto> taskModelDto) {
44 |         RLock lock = lockUtils.getLock(LOCK_KEY_PREFIX);
45 | 
46 |         if (lock.isLocked()) {
47 |             return;
48 |         }
49 | 
50 |         try {
51 |             lock.lock(LOCK_LEASE_TIME, TimeUnit.SECONDS);
52 |             String taskModelNames = taskModelDto.stream().map(TaskModelDto::getModelName).collect(Collectors.joining(", "));
53 |             CommonResponse<Boolean> response = taskModelApi.loadModel(taskModelDto);
54 |             if (response.getData()) {
55 |                 log.info("success to load task model: {}", taskModelNames);
56 |             } else {
57 |                 log.warn("failed to load task model: {}", taskModelNames);
58 |             }
59 |         } finally {
60 |             lock.unlock();
61 |         }
62 |     }
63 | }
```

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/service/PatchDetailDomService.java:PatchDetailDomService`.
Source: Retrieved code content using keyword search (bm25).
```
 81 | @Service
 82 | @Slf4j
 83 | public class PatchDetailDomService {
 84 |     @Autowired
 85 |     private PatchDetailAssembler patchDetailAssembler;
 86 |     @Autowired
 87 |     private PatchInfoService patchInfoService;
 88 |     @Autowired
 89 |     private PatchDispatchService patchDispatchService;
 90 | 
 91 |     public void saveValidPatches(List<PatchBean> validPatches) {
 92 |         log.info("save valid patches.");
 93 |         List<PatchDetailPo> patchDetails = validPatches.stream()
 94 |             .map(patchBean -> patchDetailAssembler.patchBean2DbBean(patchBean)).collect(Collectors.toList());
 95 |         List<String> patchNames = patchDetails.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList());
 96 | 
 97 |         SecurityAnnotationCheckUtil.valid(patchDetails);
 98 | //        patchDispatchService.deleteByPatchNames(patchNames);
 99 | //        patchInfoService.deleteByPatchNames(patchNames);
100 |         patchInfoService.saveAllPatchDetail(patchDetails);
101 |     }
102 | }
```

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/operate/NormalPatchOperator.java:NormalPatchOperator.generateFullKey`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/operate/NormalPatchOperator.java:NormalPatchOperator.deleteByFullPatch`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/service/PatchDetailDomService.java:PatchDetailDomService.saveValidPatches`.
Source: Retrieved code content using keyword search (bm25).

Found class `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/upload/operate/NormalPatchOperator.java:NormalPatchOperator`.
Source: Retrieved code content using keyword search (bm25).
```
15 | @Service
16 | @Slf4j
17 | public class NormalPatchOperator implements PatchTypeOperator {
18 | 
19 |     @Autowired
20 |     private PatchInfoService patchInfoService;
21 | 
22 |     @Override
23 |     public String generateFullKey(PatchBean patchBean) {
24 |         return patchBean.getService() + ":" + patchBean.getSrcVersion();
25 |     }
26 | 
27 |     @Override
28 |     public void deleteByFullPatch(List<PatchDetailPo> patchDetailPos, String patchName) {
29 |         List<String> needDeletePatches = patchDetailPos.stream()
30 |             .filter(patchDetailPo -> patchDetailPo.getIsContainerPatch() != 1
31 |                 && !patchDetailPo.getPatchName().contains(Constants.REPOSITORY_VERSION_PATCH)
32 |                 && !patchDetailPo.getPatchName().contains(Constants.SCHEMA_PATCH)
33 |                 && patchName.compareToIgnoreCase(patchDetailPo.getPatchName()) > 0)
34 |             .map(PatchDetailPo::getPatchName).collect(Collectors.toList());
35 |         if (!CollectionUtils.isEmpty(needDeletePatches)) {
36 | //            patchInfoService.deleteByPatchNames(needDeletePatches);
37 |         }
38 |     }
39 | 
40 |     @Override
41 |     public List<PatchDetailPo> filterHistoryFullPatch(List<PatchDetailPo> fullPatchList) {
42 |         return fullPatchList.stream()
43 |             .filter(patchDetailPo -> !patchDetailPo.getPatchName().contains(Constants.REPOSITORY_VERSION_PATCH)
44 |                 && !patchDetailPo.getPatchName().contains(Constants.SCHEMA_PATCH)
45 |                 && patchDetailPo.getIsContainerPatch() != 1)
46 |             .collect(Collectors.toList());
47 |     }
48 | 
49 |     @Override
50 |     public String patchType() {
51 |         return Constants.NORMAL_PATCH;
52 |     }
53 | }
```

Found file `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/taskmodel/service/TaskModelRetryService.java`.
Found file `daip-patcher-service/daip-patcher-application/src/main/java/com/zte/daip/manager/patcher/application/service/UpdateRepositoryPatchService.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.', 

'thought_4': 'The infrastructure layer repositories `PatchInfoRepository` and `PatchDispatchRepository` were found, which handle database operations for patch details and dispatch records. However, the core deletion logic with history validation likely resides in the **domain layer**. Next, we need to search for domain services that:
1. Check if a patch exists in `PatchHistory` (using `PatchHistoryRepository`).
2. Coordinate deletions in `PatchInfoRepository` and `PatchDispatchRepository` if the patch is not applied.', 'tool_name_4': 'search_code_snippets', 'tool_args_4': {'search_terms': ['PatchHistoryRepository', 'deleteSinglePatch', 'notInHistory'], 'file_path_or_pattern': '**/daip-patcher-service/daip-patcher-infrastructure-api/**/*.java'}, 'observation_4': 'No files found for file pattern \'**/daip-patcher-service/daip-patcher-infrastructure-api/**/*.java\'. Will search all files.
...
##Searching for term "PatchHistoryRepository"...
### Search Result:
Found file `daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java`.
Source: Match found for entity name `PatchHistoryRepository`.
Just show the structure of this file due to response length limitations:
```
/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHistoryRepository.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/18
 * </p>
 * <p>
 * 完成日期：2021/3/18
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface PatchHistoryRepository extends JpaRepository<PatchHistory, PatchHistoryKey> {
    @Modifying(clearAutomatically = true)
    @Query(value = "select p.id.patchName from  PatchHistory p")
    List<String> queryPatchName();

    @Modifying(clearAutomatically = true)
    @Query(
        value = "select p from  PatchHistory p where p.id.patchName = :patchName and p.id.serviceName = :serviceName")
    List<PatchHistory> queryPatchHistoryByPatchKey(@Param("patchName") String patchName,
        @Param("serviceName") String serviceName);

    @Modifying(clearAutomatically = true)
    @Query(value = "select p from  PatchHistory p where p.id.serviceName = :serviceName and p.id.ip = :ip")
    List<PatchHistory> queryPatchHistoryInfoByServiceNameAndIp(@Param("serviceName") String serviceName,
        @Param("ip") String ip);

    @Modifying(clearAutomatically = true)
    @Query(value = "select p from  PatchHistory p where p.id.serviceName = :serviceName")
    List<PatchHistory> queryPatchHistoryByServiceName(@Param("serviceName") String serviceName);

}
```
Hint: Search `daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java` to get the full content if needed.



##Searching for term "deleteSinglePatch"...
### Search Result:
Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.generateDistributeHosts`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.getListener`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-task-worker/src/main/java/com/zte/daip/manager/patcher/task/worker/version/PatcherDistributeProcessor.java:PatcherDistributeProcessor.rollback`.
Source: Retrieved code content using keyword search (bm25).

Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.queryPatchHostByServiceAndPatch`.
Source: Retrieved code content using keyword search (bm25).

Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatchHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/PatcherUpdateHandler.java`.
Found file `daip-patcher-handler/daip-patcher-handler-api/src/main/java/com/zte/daip/manager/patcher/handler/api/dto/PatchPageTableDto.java`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.



##Searching for term "deleteSinglePatch", "notInHistory"...
### Search Result:
Found method `daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/query/service/servicerole/AbstractPatchSourceGenerator.java:AbstractPatchSourceGenerator.notInHistory`.
Source: Retrieved code content using keyword search (bm25).'},
